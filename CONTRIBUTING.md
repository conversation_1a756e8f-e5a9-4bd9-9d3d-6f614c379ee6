# Contributing to M&A Platform

Thank you for your interest in contributing to the M&A Platform! This guide will help you get started with contributing to our project.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Workflow](#development-workflow)
4. [Coding Standards](#coding-standards)
5. [Testing Guidelines](#testing-guidelines)
6. [Pull Request Process](#pull-request-process)
7. [Issue Reporting](#issue-reporting)
8. [Documentation](#documentation)
9. [Community](#community)

## Code of Conduct

This project and everyone participating in it is governed by our [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code. Please report unacceptable <NAME_EMAIL>.

## Getting Started

### Prerequisites

- Node.js 18.0.0 or higher
- PNPM 8.0.0 or higher
- Docker and Docker Compose
- Git

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork the repo on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/mna-platform.git
   cd mna-platform
   ```

2. **Add upstream remote**
   ```bash
   git remote add upstream https://github.com/mna-platform/mna-platform.git
   ```

3. **Install dependencies**
   ```bash
   pnpm install
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Start development environment**
   ```bash
   pnpm dev:setup
   pnpm dev
   ```

6. **Verify setup**
   ```bash
   pnpm test
   pnpm lint
   pnpm type-check
   ```

## Development Workflow

### Branch Naming Convention

Use descriptive branch names with the following prefixes:

- `feature/` - New features
- `fix/` - Bug fixes
- `docs/` - Documentation updates
- `refactor/` - Code refactoring
- `test/` - Test improvements
- `chore/` - Maintenance tasks

Examples:
- `feature/deal-status-filtering`
- `fix/authentication-token-expiry`
- `docs/api-documentation-update`

### Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks
- `perf`: Performance improvements
- `ci`: CI/CD changes

**Examples:**
```
feat(deals): add deal status filtering functionality

Add ability to filter deals by status in the deal list view.
Includes frontend UI components and backend API endpoints.

Closes #123

fix(auth): resolve JWT token expiration issue

The JWT tokens were expiring too quickly due to incorrect
configuration. Updated the expiration time to 24 hours.

Fixes #456

docs(api): update authentication documentation

Add examples for OAuth2 authentication flow and improve
existing JWT documentation with code samples.
```

### Development Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Write code following our coding standards
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   pnpm test
   pnpm lint
   pnpm type-check
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat(scope): your descriptive message"
   ```

5. **Keep your branch updated**
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

6. **Push your branch**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **Create a Pull Request**

## Coding Standards

### TypeScript Guidelines

- Use TypeScript for all new code
- Define proper types and interfaces
- Avoid `any` type unless absolutely necessary
- Use strict TypeScript configuration

```typescript
// Good
interface User {
  id: string
  email: string
  role: UserRole
}

function createUser(userData: Omit<User, 'id'>): User {
  return {
    id: generateId(),
    ...userData
  }
}

// Avoid
function createUser(userData: any): any {
  return {
    id: generateId(),
    ...userData
  }
}
```

### React Guidelines

- Use functional components with hooks
- Follow React best practices
- Use proper prop types
- Implement proper error boundaries

```tsx
// Good
interface DealCardProps {
  deal: Deal
  onStatusUpdate: (dealId: string, status: DealStatus) => void
}

export const DealCard: React.FC<DealCardProps> = ({ deal, onStatusUpdate }) => {
  const handleStatusChange = useCallback((status: DealStatus) => {
    onStatusUpdate(deal.id, status)
  }, [deal.id, onStatusUpdate])

  return (
    <Card>
      <CardContent>
        <Typography variant="h6">{deal.name}</Typography>
        <StatusSelector value={deal.status} onChange={handleStatusChange} />
      </CardContent>
    </Card>
  )
}
```

### Backend Guidelines

- Use Express.js best practices
- Implement proper error handling
- Use middleware for cross-cutting concerns
- Follow RESTful API conventions

```typescript
// Good
export const createDeal = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const dealData = createDealSchema.parse(req.body)
    const deal = await dealService.createDeal(dealData, req.user)
    
    res.status(201).json({
      success: true,
      data: deal
    })
  } catch (error) {
    next(error)
  }
}
```

### Code Style

We use Prettier and ESLint for code formatting and linting:

```bash
# Format code
pnpm format

# Lint code
pnpm lint

# Fix linting issues
pnpm lint:fix
```

**Key style guidelines:**
- Use 2 spaces for indentation
- Use single quotes for strings
- No trailing commas in objects
- Semicolons are optional but be consistent
- Maximum line length: 100 characters

## Testing Guidelines

### Test Structure

```
packages/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   └── __tests__/
│   │   ├── hooks/
│   │   │   └── __tests__/
│   │   └── utils/
│   │       └── __tests__/
│   └── e2e/
└── backend/
    ├── src/
    │   ├── controllers/
    │   │   └── __tests__/
    │   ├── services/
    │   │   └── __tests__/
    │   └── utils/
    │       └── __tests__/
    └── tests/
        ├── integration/
        └── fixtures/
```

### Writing Tests

**Unit Tests:**
```typescript
// packages/frontend/src/utils/__tests__/formatCurrency.test.ts
import { formatCurrency } from '../formatCurrency'

describe('formatCurrency', () => {
  it('formats USD currency correctly', () => {
    expect(formatCurrency(1000000, 'USD')).toBe('$1,000,000.00')
  })

  it('handles zero values', () => {
    expect(formatCurrency(0, 'USD')).toBe('$0.00')
  })

  it('handles negative values', () => {
    expect(formatCurrency(-1000, 'USD')).toBe('-$1,000.00')
  })
})
```

**Component Tests:**
```typescript
// packages/frontend/src/components/__tests__/DealCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { DealCard } from '../DealCard'

const mockDeal = {
  id: '1',
  name: 'Test Deal',
  value: 1000000,
  status: 'active'
}

describe('DealCard', () => {
  it('renders deal information', () => {
    render(<DealCard deal={mockDeal} onStatusUpdate={jest.fn()} />)
    
    expect(screen.getByText('Test Deal')).toBeInTheDocument()
    expect(screen.getByText('$1,000,000.00')).toBeInTheDocument()
  })

  it('calls onStatusUpdate when status changes', () => {
    const onStatusUpdate = jest.fn()
    render(<DealCard deal={mockDeal} onStatusUpdate={onStatusUpdate} />)
    
    fireEvent.click(screen.getByRole('button', { name: /change status/i }))
    fireEvent.click(screen.getByText('Completed'))
    
    expect(onStatusUpdate).toHaveBeenCalledWith('1', 'completed')
  })
})
```

**Integration Tests:**
```typescript
// packages/backend/tests/integration/deals.test.ts
import request from 'supertest'
import { app } from '../../src/app'
import { createTestUser, createTestDeal } from '../fixtures'

describe('Deals API', () => {
  let authToken: string

  beforeEach(async () => {
    const user = await createTestUser()
    authToken = generateAuthToken(user)
  })

  describe('POST /api/deals', () => {
    it('creates a new deal', async () => {
      const dealData = {
        name: 'Test Deal',
        value: 1000000,
        currency: 'USD'
      }

      const response = await request(app)
        .post('/api/deals')
        .set('Authorization', `Bearer ${authToken}`)
        .send(dealData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.name).toBe(dealData.name)
    })
  })
})
```

### Test Coverage

We aim for high test coverage:
- **Unit tests**: 90%+ coverage
- **Integration tests**: Cover all API endpoints
- **E2E tests**: Cover critical user journeys

```bash
# Run tests with coverage
pnpm test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

## Pull Request Process

### Before Submitting

1. **Ensure your code follows our standards**
   ```bash
   pnpm lint
   pnpm type-check
   pnpm test
   ```

2. **Update documentation**
   - Update README if needed
   - Add/update API documentation
   - Update user guides if applicable

3. **Add tests**
   - Unit tests for new functions/components
   - Integration tests for new API endpoints
   - E2E tests for new user flows

### Pull Request Template

When creating a pull request, use this template:

```markdown
## Description
Brief description of the changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes

## Related Issues
Closes #(issue number)
```

### Review Process

1. **Automated checks** must pass:
   - CI/CD pipeline
   - Code quality checks
   - Security scans

2. **Code review** by maintainers:
   - At least one approval required
   - Address all feedback
   - Resolve all conversations

3. **Final checks**:
   - Rebase on latest main
   - Squash commits if needed
   - Update PR description

## Issue Reporting

### Bug Reports

Use the bug report template:

```markdown
**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Additional context**
Add any other context about the problem here.
```

### Feature Requests

Use the feature request template:

```markdown
**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is.

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.
```

## Documentation

### Types of Documentation

1. **Code Documentation**
   - JSDoc comments for functions
   - README files for packages
   - Inline comments for complex logic

2. **API Documentation**
   - OpenAPI/Swagger specifications
   - Endpoint descriptions
   - Request/response examples

3. **User Documentation**
   - User guides
   - Feature documentation
   - Troubleshooting guides

### Documentation Standards

```typescript
/**
 * Creates a new deal in the system
 * 
 * @param dealData - The deal information
 * @param user - The user creating the deal
 * @returns Promise that resolves to the created deal
 * @throws {ValidationError} When deal data is invalid
 * @throws {AuthorizationError} When user lacks permissions
 * 
 * @example
 * ```typescript
 * const deal = await createDeal({
 *   name: 'Acme Acquisition',
 *   value: 1000000,
 *   currency: 'USD'
 * }, user)
 * ```
 */
export async function createDeal(
  dealData: CreateDealData,
  user: User
): Promise<Deal> {
  // Implementation
}
```

## Community

### Communication Channels

- **GitHub Discussions**: For general questions and discussions
- **GitHub Issues**: For bug reports and feature requests
- **Slack**: For real-time communication (invite only)
- **Email**: For security issues and private matters

### Getting Help

1. **Check existing documentation**
2. **Search existing issues**
3. **Ask in GitHub Discussions**
4. **Create a new issue if needed**

### Recognition

Contributors are recognized in:
- CONTRIBUTORS.md file
- Release notes
- Annual contributor awards
- Conference speaking opportunities

## License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project (MIT License).

---

Thank you for contributing to the M&A Platform! Your contributions help make this project better for everyone.

For questions about contributing, please contact <NAME_EMAIL>
