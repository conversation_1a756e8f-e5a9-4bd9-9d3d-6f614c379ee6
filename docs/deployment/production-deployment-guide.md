# Production Deployment Guide

Complete guide for deploying the M&A Platform to production environments.

## Table of Contents

1. [Pre-deployment Checklist](#pre-deployment-checklist)
2. [Environment Configuration](#environment-configuration)
3. [Docker Production Deployment](#docker-production-deployment)
4. [Kubernetes Production Deployment](#kubernetes-production-deployment)
5. [Cloud Platform Deployment](#cloud-platform-deployment)
6. [SSL/TLS Configuration](#ssltls-configuration)
7. [Monitoring & Logging](#monitoring--logging)
8. [Backup & Recovery](#backup--recovery)
9. [Security Hardening](#security-hardening)
10. [Performance Optimization](#performance-optimization)

## Pre-deployment Checklist

### Infrastructure Requirements

- [ ] **Compute Resources**
  - Minimum: 4 CPU cores, 8GB RAM
  - Recommended: 8+ CPU cores, 16GB+ RAM
  - Storage: 100GB+ SSD

- [ ] **Database**
  - PostgreSQL 14+ with SSL enabled
  - Connection pooling configured
  - Backup strategy implemented

- [ ] **Cache**
  - Redis 6+ with persistence enabled
  - Memory: 2GB+ allocated
  - SSL/TLS encryption enabled

- [ ] **Load Balancer**
  - SSL termination configured
  - Health checks enabled
  - Rate limiting configured

- [ ] **File Storage**
  - S3-compatible storage
  - CDN configured
  - Backup replication enabled

### Security Requirements

- [ ] SSL certificates obtained and configured
- [ ] Firewall rules configured
- [ ] VPN/VPC network isolation
- [ ] Secrets management system
- [ ] Security scanning completed
- [ ] Penetration testing completed

### Monitoring Requirements

- [ ] Application monitoring (APM)
- [ ] Infrastructure monitoring
- [ ] Log aggregation
- [ ] Alerting configured
- [ ] Uptime monitoring
- [ ] Performance monitoring

## Environment Configuration

### Production Environment Variables

```env
# Application Configuration
NODE_ENV=production
PORT=3001
API_VERSION=v1
LOG_LEVEL=info

# Database Configuration
DATABASE_URL=*******************************************/mna_platform
DATABASE_POOL_MIN=10
DATABASE_POOL_MAX=50
DATABASE_SSL=true
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://redis-host:6379
REDIS_PASSWORD=your-secure-redis-password
REDIS_TLS=true
REDIS_TIMEOUT=5000

# Authentication & Security
JWT_SECRET=your-super-secure-jwt-secret-minimum-256-bits
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_S3_BUCKET=mna-platform-documents-prod
AWS_REGION=us-west-2
AWS_CLOUDFRONT_DOMAIN=cdn.mnaplatform.com

# Email Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# External Services
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Monitoring & Observability
SENTRY_DSN=https://<EMAIL>/project-id
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
DATADOG_API_KEY=your-datadog-api-key

# Security Configuration
CORS_ORIGIN=https://app.mnaplatform.com,https://admin.mnaplatform.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
HELMET_CSP_ENABLED=true

# Frontend Configuration
VITE_API_URL=https://api.mnaplatform.com
VITE_WS_URL=wss://api.mnaplatform.com
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
```

### Secrets Management

**Using AWS Secrets Manager:**
```bash
# Store database credentials
aws secretsmanager create-secret \
  --name "mna-platform/database" \
  --description "Database credentials for M&A Platform" \
  --secret-string '{"username":"dbuser","password":"securepassword"}'

# Store JWT secret
aws secretsmanager create-secret \
  --name "mna-platform/jwt-secret" \
  --description "JWT secret for M&A Platform" \
  --secret-string '{"secret":"your-jwt-secret"}'
```

**Using Kubernetes Secrets:**
```bash
# Create secrets from environment file
kubectl create secret generic app-secrets \
  --from-env-file=.env.production \
  --namespace=mna-platform

# Create TLS secret for ingress
kubectl create secret tls mnaplatform-tls \
  --cert=path/to/tls.crt \
  --key=path/to/tls.key \
  --namespace=mna-platform
```

## Docker Production Deployment

### Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - app-network

  frontend:
    build:
      context: .
      dockerfile: packages/frontend/Dockerfile.prod
    environment:
      - VITE_API_URL=https://api.mnaplatform.com
      - VITE_WS_URL=wss://api.mnaplatform.com
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - app-network

  backend:
    build:
      context: .
      dockerfile: packages/backend/Dockerfile.prod
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network
      - db-network

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: mna_platform
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    restart: unless-stopped
    networks:
      - db-network

  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - db-network

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  app-network:
    driver: bridge
  db-network:
    driver: bridge
    internal: true
  monitoring:
    driver: bridge
```

### Optimized Production Dockerfiles

**Frontend Dockerfile:**
```dockerfile
# packages/frontend/Dockerfile.prod
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json pnpm-lock.yaml ./
COPY packages/frontend/package.json ./packages/frontend/
COPY packages/shared/package.json ./packages/shared/

# Install dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile --prod=false

# Copy source code
COPY packages/frontend ./packages/frontend
COPY packages/shared ./packages/shared

# Build application
WORKDIR /app/packages/frontend
RUN pnpm build

# Production stage
FROM nginx:alpine

# Install security updates
RUN apk update && apk upgrade

# Copy built application
COPY --from=builder /app/packages/frontend/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# Add non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set ownership
RUN chown -R nextjs:nodejs /usr/share/nginx/html
RUN chown -R nextjs:nodejs /var/cache/nginx

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

**Backend Dockerfile:**
```dockerfile
# packages/backend/Dockerfile.prod
FROM node:18-alpine AS builder

WORKDIR /app

# Install security updates
RUN apk update && apk upgrade

# Copy package files
COPY package*.json pnpm-lock.yaml ./
COPY packages/backend/package.json ./packages/backend/
COPY packages/shared/package.json ./packages/shared/

# Install dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile --prod=false

# Copy source code
COPY packages/backend ./packages/backend
COPY packages/shared ./packages/shared

# Build application
WORKDIR /app/packages/backend
RUN pnpm build

# Production stage
FROM node:18-alpine AS production

# Install security updates and curl for health checks
RUN apk update && apk upgrade && apk add --no-cache curl

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files
COPY --from=builder /app/packages/backend/package.json ./
COPY --from=builder /app/packages/backend/pnpm-lock.yaml ./

# Install production dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile --prod

# Copy built application
COPY --from=builder --chown=nodejs:nodejs /app/packages/backend/dist ./dist
COPY --from=builder --chown=nodejs:nodejs /app/packages/shared ./shared

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start application
CMD ["node", "dist/index.js"]
```

### Deployment Commands

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --force-recreate

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Backup database
docker-compose -f docker-compose.prod.yml exec postgres \
  pg_dump -U $DB_USER mna_platform > backup_$(date +%Y%m%d_%H%M%S).sql

# Monitor resource usage
docker stats

# Clean up unused resources
docker system prune -f
docker volume prune -f
```

## SSL/TLS Configuration

### Nginx SSL Configuration

```nginx
# nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Frontend server
    server {
        listen 80;
        server_name app.mnaplatform.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name app.mnaplatform.com;

        ssl_certificate /etc/nginx/ssl/mnaplatform.crt;
        ssl_certificate_key /etc/nginx/ssl/mnaplatform.key;

        location / {
            proxy_pass http://frontend:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # API server
    server {
        listen 80;
        server_name api.mnaplatform.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name api.mnaplatform.com;

        ssl_certificate /etc/nginx/ssl/mnaplatform.crt;
        ssl_certificate_key /etc/nginx/ssl/mnaplatform.key;

        # API rate limiting
        location /api/auth/login {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://backend:3001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend:3001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket support
        location /ws {
            proxy_pass http://backend:3001;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            proxy_pass http://backend:3001/health;
            access_log off;
        }
    }
}
```

### Let's Encrypt SSL Setup

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d app.mnaplatform.com -d api.mnaplatform.com

# Test auto-renewal
sudo certbot renew --dry-run

# Set up auto-renewal cron job
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## Monitoring & Logging

### Prometheus Configuration

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'mna-platform-backend'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

### Alert Rules

```yaml
# monitoring/alert_rules.yml
groups:
  - name: mna-platform-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High response time detected
          description: "95th percentile response time is {{ $value }} seconds"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High number of database connections
          description: "Database has {{ $value }} active connections"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Redis memory usage high
          description: "Redis memory usage is {{ $value }}%"

      - alert: DiskSpaceHigh
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Disk space usage high
          description: "Disk usage is {{ $value }}%"
```

### Application Logging

```typescript
// packages/backend/src/utils/logger.ts
import winston from 'winston'
import { ElasticsearchTransport } from 'winston-elasticsearch'

const esTransportOpts = {
  level: 'info',
  clientOpts: {
    node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200'
  },
  index: 'mna-platform-logs'
}

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'mna-platform-backend',
    environment: process.env.NODE_ENV
  },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
})

// Add Elasticsearch transport in production
if (process.env.NODE_ENV === 'production' && process.env.ELASTICSEARCH_URL) {
  logger.add(new ElasticsearchTransport(esTransportOpts))
}
```

## Backup & Recovery

### Automated Database Backup

```bash
#!/bin/bash
# scripts/backup-database.sh

set -e

# Configuration
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-mna_platform}
DB_USER=${DB_USER:-postgres}
BACKUP_DIR=${BACKUP_DIR:-/backups}
S3_BUCKET=${S3_BUCKET:-mna-platform-backups}
RETENTION_DAYS=${RETENTION_DAYS:-30}

# Create backup directory
mkdir -p $BACKUP_DIR

# Generate backup filename
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/mna_platform_backup_$TIMESTAMP.sql"

# Create database backup
echo "Creating database backup..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# Compress backup
echo "Compressing backup..."
gzip $BACKUP_FILE

# Upload to S3
echo "Uploading to S3..."
aws s3 cp "$BACKUP_FILE.gz" "s3://$S3_BUCKET/database/"

# Clean up old local backups
echo "Cleaning up old backups..."
find $BACKUP_DIR -name "mna_platform_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

# Clean up old S3 backups
aws s3 ls "s3://$S3_BUCKET/database/" | while read -r line; do
  createDate=$(echo $line | awk '{print $1" "$2}')
  createDate=$(date -d "$createDate" +%s)
  olderThan=$(date -d "$RETENTION_DAYS days ago" +%s)
  if [[ $createDate -lt $olderThan ]]; then
    fileName=$(echo $line | awk '{print $4}')
    if [[ $fileName != "" ]]; then
      aws s3 rm "s3://$S3_BUCKET/database/$fileName"
    fi
  fi
done

echo "Backup completed successfully!"
```

### Database Recovery

```bash
#!/bin/bash
# scripts/restore-database.sh

set -e

if [ $# -eq 0 ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

BACKUP_FILE=$1
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-mna_platform}
DB_USER=${DB_USER:-postgres}

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Create confirmation prompt
echo "WARNING: This will overwrite the existing database!"
echo "Database: $DB_NAME"
echo "Backup file: $BACKUP_FILE"
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Operation cancelled."
    exit 1
fi

# Drop existing database
echo "Dropping existing database..."
dropdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME

# Create new database
echo "Creating new database..."
createdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME

# Restore from backup
echo "Restoring from backup..."
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE | psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME
else
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME < $BACKUP_FILE
fi

echo "Database restored successfully!"
```

### File Storage Backup

```bash
#!/bin/bash
# scripts/backup-files.sh

set -e

# Configuration
SOURCE_BUCKET=${SOURCE_BUCKET:-mna-platform-documents-prod}
BACKUP_BUCKET=${BACKUP_BUCKET:-mna-platform-documents-backup}
AWS_REGION=${AWS_REGION:-us-west-2}

echo "Starting file storage backup..."

# Sync files to backup bucket
aws s3 sync "s3://$SOURCE_BUCKET" "s3://$BACKUP_BUCKET" \
  --region $AWS_REGION \
  --delete \
  --storage-class STANDARD_IA

echo "File storage backup completed!"

# Set up cross-region replication (one-time setup)
if [ "$1" = "--setup-replication" ]; then
    echo "Setting up cross-region replication..."
    
    # Create replication configuration
    cat > replication-config.json << EOF
{
    "Role": "arn:aws:iam::ACCOUNT-ID:role/replication-role",
    "Rules": [
        {
            "ID": "ReplicateEverything",
            "Status": "Enabled",
            "Prefix": "",
            "Destination": {
                "Bucket": "arn:aws:s3:::$BACKUP_BUCKET",
                "StorageClass": "STANDARD_IA"
            }
        }
    ]
}
EOF

    # Apply replication configuration
    aws s3api put-bucket-replication \
      --bucket $SOURCE_BUCKET \
      --replication-configuration file://replication-config.json

    # Clean up
    rm replication-config.json
    
    echo "Cross-region replication configured!"
fi
```

---

This production deployment guide provides comprehensive instructions for deploying the M&A Platform in production environments. For additional support or specific deployment scenarios, contact the DevOps <NAME_EMAIL>
