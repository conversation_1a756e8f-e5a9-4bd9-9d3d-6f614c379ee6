# M&A Platform Administrator Guide

This guide provides comprehensive information for system administrators managing the M&A Platform.

## Table of Contents

1. [System Overview](#system-overview)
2. [Installation & Setup](#installation--setup)
3. [User Management](#user-management)
4. [Tenant Management](#tenant-management)
5. [Security Configuration](#security-configuration)
6. [System Monitoring](#system-monitoring)
7. [Backup & Recovery](#backup--recovery)
8. [Performance Optimization](#performance-optimization)
9. [Troubleshooting](#troubleshooting)
10. [Maintenance](#maintenance)

## System Overview

### Architecture Components

The M&A Platform consists of several key components:

1. **Frontend Application** (React/TypeScript)
   - Web-based user interface
   - Progressive Web App (PWA)
   - Mobile-responsive design

2. **Backend API** (Node.js/Express)
   - RESTful API services
   - Authentication & authorization
   - Business logic processing

3. **Database Layer** (PostgreSQL)
   - Primary data storage
   - Multi-tenant data isolation
   - Backup and replication

4. **Cache Layer** (Redis)
   - Session storage
   - Application caching
   - Real-time data

5. **File Storage** (S3-compatible)
   - Document storage
   - Media files
   - Backup storage

### System Requirements

**Minimum Requirements:**
- CPU: 4 cores
- RAM: 8GB
- Storage: 100GB SSD
- Network: 1Gbps

**Recommended Production:**
- CPU: 8+ cores
- RAM: 32GB+
- Storage: 500GB+ SSD
- Network: 10Gbps
- Load balancer
- Database replication

## Installation & Setup

### Docker Deployment

1. **Prerequisites**
   ```bash
   # Install Docker and Docker Compose
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **Environment Configuration**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit configuration
   nano .env
   ```

3. **Start Services**
   ```bash
   # Start all services
   docker-compose up -d
   
   # Check service status
   docker-compose ps
   
   # View logs
   docker-compose logs -f
   ```

### Kubernetes Deployment

1. **Cluster Setup**
   ```bash
   # Apply namespace
   kubectl apply -f infrastructure/kubernetes/namespace.yaml
   
   # Apply configurations
   kubectl apply -f infrastructure/kubernetes/
   
   # Check deployment status
   kubectl get pods -n mna-platform
   ```

2. **Ingress Configuration**
   ```bash
   # Configure ingress controller
   kubectl apply -f infrastructure/kubernetes/ingress/
   
   # Verify ingress
   kubectl get ingress -n mna-platform
   ```

### Database Setup

1. **Initial Setup**
   ```bash
   # Run database migrations
   npm run db:migrate
   
   # Seed initial data
   npm run db:seed
   
   # Create admin user
   npm run create-admin
   ```

2. **Backup Configuration**
   ```bash
   # Configure automated backups
   crontab -e
   
   # Add backup schedule (daily at 2 AM)
   0 2 * * * /opt/mna-platform/scripts/backup-db.sh
   ```

## User Management

### Admin Dashboard Access

1. **Access Admin Panel**
   - URL: `https://your-domain.com/admin`
   - Login with admin credentials
   - Navigate to User Management

2. **Admin Permissions**
   - Create/edit/delete users
   - Manage user roles
   - Reset passwords
   - View user activity

### User Operations

1. **Creating Users**
   ```bash
   # Via CLI
   npm run create-user --email=<EMAIL> --role=analyst
   
   # Via Admin UI
   # Navigate to Users > Add User
   # Fill in user details
   # Assign appropriate role
   ```

2. **User Roles**
   - **Super Admin**: Full system access
   - **Tenant Admin**: Tenant-level administration
   - **Deal Manager**: Deal management capabilities
   - **Analyst**: Analysis and reporting tools
   - **Viewer**: Read-only access

3. **Bulk Operations**
   ```bash
   # Import users from CSV
   npm run import-users --file=users.csv
   
   # Export user list
   npm run export-users --format=csv
   ```

### SSO Configuration

1. **Google SSO**
   ```env
   GOOGLE_CLIENT_ID=your-client-id
   GOOGLE_CLIENT_SECRET=your-client-secret
   GOOGLE_REDIRECT_URI=https://your-domain.com/auth/google/callback
   ```

2. **SAML Configuration**
   ```env
   SAML_ENTRY_POINT=https://your-idp.com/sso
   SAML_ISSUER=your-app-identifier
   SAML_CERT=path/to/certificate.pem
   ```

## Tenant Management

### Multi-Tenancy Overview

The platform supports complete tenant isolation:

- **Data Isolation**: Separate database schemas
- **User Isolation**: Tenant-specific user management
- **Configuration Isolation**: Independent settings
- **Billing Isolation**: Separate subscription management

### Tenant Operations

1. **Creating Tenants**
   ```bash
   # Via CLI
   npm run create-tenant --name="Acme Corp" --domain="acme"
   
   # Via Admin API
   curl -X POST /api/admin/tenants \
     -H "Authorization: Bearer $ADMIN_TOKEN" \
     -d '{"name":"Acme Corp","domain":"acme"}'
   ```

2. **Tenant Configuration**
   - Custom branding
   - Domain configuration
   - Feature toggles
   - Subscription limits

3. **Tenant Monitoring**
   ```bash
   # View tenant metrics
   npm run tenant-metrics --tenant=acme
   
   # Check tenant health
   npm run tenant-health --all
   ```

## Security Configuration

### SSL/TLS Setup

1. **Certificate Installation**
   ```bash
   # Using Let's Encrypt
   certbot --nginx -d your-domain.com
   
   # Manual certificate
   cp certificate.crt /etc/ssl/certs/
   cp private.key /etc/ssl/private/
   ```

2. **Security Headers**
   ```nginx
   # Nginx configuration
   add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
   add_header X-Content-Type-Options nosniff;
   add_header X-Frame-Options DENY;
   add_header X-XSS-Protection "1; mode=block";
   ```

### Firewall Configuration

1. **Basic Firewall Rules**
   ```bash
   # Allow HTTP/HTTPS
   ufw allow 80/tcp
   ufw allow 443/tcp
   
   # Allow SSH (change port as needed)
   ufw allow 22/tcp
   
   # Database access (internal only)
   ufw allow from 10.0.0.0/8 to any port 5432
   
   # Enable firewall
   ufw enable
   ```

2. **Application-Level Security**
   ```env
   # Rate limiting
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   
   # Session security
   SESSION_SECRET=your-secure-secret
   SESSION_TIMEOUT=3600000
   
   # CORS configuration
   CORS_ORIGIN=https://your-domain.com
   ```

### Audit Logging

1. **Enable Audit Logs**
   ```env
   AUDIT_LOGGING_ENABLED=true
   AUDIT_LOG_LEVEL=info
   AUDIT_LOG_RETENTION_DAYS=365
   ```

2. **Log Analysis**
   ```bash
   # View recent audit logs
   tail -f /var/log/mna-platform/audit.log
   
   # Search for specific events
   grep "user_login" /var/log/mna-platform/audit.log
   
   # Generate security report
   npm run security-report --days=30
   ```

## System Monitoring

### Health Checks

1. **Application Health**
   ```bash
   # Check application status
   curl https://your-domain.com/health
   
   # Detailed health check
   curl https://your-domain.com/health/detailed
   ```

2. **Database Health**
   ```bash
   # Check database connectivity
   npm run db:health
   
   # Monitor database performance
   npm run db:monitor
   ```

### Monitoring Setup

1. **Prometheus Configuration**
   ```yaml
   # prometheus.yml
   global:
     scrape_interval: 15s
   
   scrape_configs:
     - job_name: 'mna-platform'
       static_configs:
         - targets: ['localhost:3001']
   ```

2. **Grafana Dashboards**
   ```bash
   # Import dashboard
   curl -X POST *********************************/api/dashboards/db \
     -H "Content-Type: application/json" \
     -d @dashboards/mna-platform.json
   ```

### Alerting

1. **Alert Rules**
   ```yaml
   # alerts.yml
   groups:
     - name: mna-platform
       rules:
         - alert: HighErrorRate
           expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
           for: 5m
           annotations:
             summary: High error rate detected
   ```

2. **Notification Channels**
   ```env
   ALERT_EMAIL_SMTP_HOST=smtp.gmail.com
   ALERT_EMAIL_SMTP_PORT=587
   ALERT_EMAIL_USERNAME=<EMAIL>
   ALERT_EMAIL_PASSWORD=your-password
   
   ALERT_SLACK_WEBHOOK=https://hooks.slack.com/services/...
   ```

## Backup & Recovery

### Database Backup

1. **Automated Backups**
   ```bash
   #!/bin/bash
   # backup-db.sh
   
   DATE=$(date +%Y%m%d_%H%M%S)
   BACKUP_DIR="/opt/backups"
   
   # Create backup
   pg_dump -h localhost -U postgres mna_platform > $BACKUP_DIR/db_backup_$DATE.sql
   
   # Compress backup
   gzip $BACKUP_DIR/db_backup_$DATE.sql
   
   # Upload to S3
   aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://your-backup-bucket/
   
   # Clean old backups (keep 30 days)
   find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete
   ```

2. **Point-in-Time Recovery**
   ```bash
   # Enable WAL archiving
   echo "wal_level = replica" >> /etc/postgresql/14/main/postgresql.conf
   echo "archive_mode = on" >> /etc/postgresql/14/main/postgresql.conf
   echo "archive_command = 'cp %p /opt/wal_archive/%f'" >> /etc/postgresql/14/main/postgresql.conf
   ```

### File Storage Backup

1. **S3 Backup**
   ```bash
   # Sync to backup bucket
   aws s3 sync s3://your-primary-bucket s3://your-backup-bucket --delete
   
   # Cross-region replication
   aws s3api put-bucket-replication \
     --bucket your-primary-bucket \
     --replication-configuration file://replication.json
   ```

### Disaster Recovery

1. **Recovery Procedures**
   ```bash
   # Database recovery
   psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS mna_platform;"
   psql -h localhost -U postgres -c "CREATE DATABASE mna_platform;"
   gunzip -c db_backup_20231201_020000.sql.gz | psql -h localhost -U postgres mna_platform
   
   # Application recovery
   docker-compose down
   docker-compose pull
   docker-compose up -d
   ```

2. **Recovery Testing**
   ```bash
   # Test recovery procedure monthly
   npm run test-recovery --environment=staging
   ```

## Performance Optimization

### Database Optimization

1. **Index Management**
   ```sql
   -- Check missing indexes
   SELECT schemaname, tablename, attname, n_distinct, correlation
   FROM pg_stats
   WHERE schemaname = 'public'
   ORDER BY n_distinct DESC;
   
   -- Create indexes
   CREATE INDEX CONCURRENTLY idx_deals_status ON deals(status);
   CREATE INDEX CONCURRENTLY idx_documents_tenant_id ON documents(tenant_id);
   ```

2. **Query Optimization**
   ```bash
   # Enable slow query logging
   echo "log_min_duration_statement = 1000" >> /etc/postgresql/14/main/postgresql.conf
   
   # Analyze slow queries
   npm run analyze-slow-queries
   ```

### Application Optimization

1. **Caching Strategy**
   ```env
   # Redis configuration
   REDIS_CACHE_TTL=3600
   REDIS_MAX_MEMORY=2gb
   REDIS_EVICTION_POLICY=allkeys-lru
   ```

2. **Connection Pooling**
   ```env
   # Database connection pool
   DB_POOL_MIN=5
   DB_POOL_MAX=20
   DB_POOL_IDLE_TIMEOUT=30000
   ```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database status
   systemctl status postgresql
   
   # Check connections
   psql -h localhost -U postgres -c "SELECT * FROM pg_stat_activity;"
   
   # Restart database
   systemctl restart postgresql
   ```

2. **Memory Issues**
   ```bash
   # Check memory usage
   free -h
   
   # Check application memory
   docker stats
   
   # Restart services
   docker-compose restart
   ```

3. **Performance Issues**
   ```bash
   # Check system load
   top
   htop
   
   # Check disk I/O
   iotop
   
   # Check network
   netstat -tulpn
   ```

### Log Analysis

1. **Application Logs**
   ```bash
   # View application logs
   docker-compose logs -f backend
   
   # Search for errors
   grep -i error /var/log/mna-platform/app.log
   
   # Monitor real-time logs
   tail -f /var/log/mna-platform/app.log | grep ERROR
   ```

2. **System Logs**
   ```bash
   # System logs
   journalctl -u mna-platform -f
   
   # Database logs
   tail -f /var/log/postgresql/postgresql-14-main.log
   
   # Nginx logs
   tail -f /var/log/nginx/access.log
   tail -f /var/log/nginx/error.log
   ```

## Maintenance

### Regular Maintenance Tasks

1. **Daily Tasks**
   - Check system health
   - Review error logs
   - Monitor disk space
   - Verify backups

2. **Weekly Tasks**
   - Update system packages
   - Review security logs
   - Analyze performance metrics
   - Clean temporary files

3. **Monthly Tasks**
   - Security updates
   - Database maintenance
   - Backup testing
   - Performance review

### Update Procedures

1. **Application Updates**
   ```bash
   # Backup before update
   npm run backup-all
   
   # Pull latest version
   git pull origin main
   
   # Update dependencies
   npm install
   
   # Run migrations
   npm run db:migrate
   
   # Restart services
   docker-compose restart
   ```

2. **System Updates**
   ```bash
   # Update packages
   apt update && apt upgrade -y
   
   # Reboot if required
   if [ -f /var/run/reboot-required ]; then
     reboot
   fi
   ```

### Maintenance Scripts

1. **Cleanup Script**
   ```bash
   #!/bin/bash
   # cleanup.sh
   
   # Clean old logs
   find /var/log/mna-platform -name "*.log" -mtime +30 -delete
   
   # Clean temporary files
   rm -rf /tmp/mna-platform-*
   
   # Clean Docker images
   docker image prune -f
   
   # Clean database
   npm run db:vacuum
   ```

2. **Health Check Script**
   ```bash
   #!/bin/bash
   # health-check.sh
   
   # Check services
   docker-compose ps | grep -q "Up" || echo "Service down!"
   
   # Check disk space
   df -h | awk '$5 > 80 {print "Disk space warning: " $0}'
   
   # Check memory
   free | awk 'NR==2{printf "Memory usage: %.2f%%\n", $3*100/$2}'
   ```

---

For additional support or questions, please contact the technical <NAME_EMAIL>
