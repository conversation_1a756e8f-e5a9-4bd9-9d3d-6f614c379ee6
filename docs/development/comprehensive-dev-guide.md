# Comprehensive Developer Guide

Complete development guide for the M&A Platform, covering setup, workflow, testing, and best practices.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Development Environment](#development-environment)
3. [Project Architecture](#project-architecture)
4. [Development Workflow](#development-workflow)
5. [Testing Strategy](#testing-strategy)
6. [Code Quality & Standards](#code-quality--standards)
7. [Debugging & Troubleshooting](#debugging--troubleshooting)
8. [Deployment](#deployment)

## Quick Start

### Prerequisites

- **Node.js** 18.0.0+
- **PNPM** 8.0.0+
- **Docker** & **Docker Compose**
- **Git**

### 5-Minute Setup

```bash
# 1. Clone repository
git clone https://github.com/your-org/mna-platform.git
cd mna-platform

# 2. Install dependencies
pnpm install

# 3. Start development environment
pnpm dev:setup    # Sets up databases and runs migrations
pnpm dev          # Starts all development servers

# 4. Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:3001
# API Docs: http://localhost:3001/api/docs
```

## Development Environment

### Environment Configuration

Create `.env` file in the project root:

```env
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/mna_platform"
DATABASE_URL_TEST="postgresql://postgres:password@localhost:5432/mna_platform_test"

# Redis
REDIS_URL="redis://localhost:6379"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"
REFRESH_TOKEN_EXPIRES_IN="7d"

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_S3_BUCKET="mna-platform-documents-dev"
AWS_REGION="us-west-2"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# Frontend Configuration
VITE_API_URL="http://localhost:3001"
VITE_WS_URL="ws://localhost:3001"
VITE_APP_NAME="M&A Platform"
VITE_SENTRY_DSN=""

# External Services
GOOGLE_CLIENT_ID="your-google-oauth-client-id"
GOOGLE_CLIENT_SECRET="your-google-oauth-client-secret"

# Development
NODE_ENV="development"
LOG_LEVEL="debug"
ENABLE_CORS="true"
```

### Docker Services

```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: mna_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI

volumes:
  postgres_data:
  redis_data:
```

### VS Code Configuration

**Recommended Extensions:**
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-eslint",
    "prisma.prisma",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-docker"
  ]
}
```

**Workspace Settings:**
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.next": true
  }
}
```

## Project Architecture

### Monorepo Structure

```
mna-platform/
├── packages/
│   ├── frontend/              # React/TypeScript web app
│   │   ├── src/
│   │   │   ├── components/    # Reusable UI components
│   │   │   ├── pages/         # Page components
│   │   │   ├── features/      # Feature-based modules
│   │   │   ├── hooks/         # Custom React hooks
│   │   │   ├── services/      # API service layer
│   │   │   ├── utils/         # Utility functions
│   │   │   ├── types/         # TypeScript definitions
│   │   │   └── styles/        # Global styles
│   │   └── public/            # Static assets
│   │
│   ├── backend/               # Node.js/Express API
│   │   ├── src/
│   │   │   ├── controllers/   # HTTP request handlers
│   │   │   ├── services/      # Business logic
│   │   │   ├── middleware/    # Express middleware
│   │   │   ├── routes/        # API route definitions
│   │   │   ├── models/        # Database models
│   │   │   ├── utils/         # Utility functions
│   │   │   └── types/         # TypeScript definitions
│   │   ├── prisma/            # Database schema
│   │   └── tests/             # Test files
│   │
│   ├── shared/                # Shared code
│   │   ├── src/
│   │   │   ├── types/         # Shared TypeScript types
│   │   │   ├── utils/         # Shared utilities
│   │   │   └── constants/     # Shared constants
│   │   └── package.json
│   │
│   └── mobile/                # React Native app
│       ├── src/
│       ├── android/
│       └── ios/
│
├── infrastructure/            # Infrastructure as Code
│   ├── kubernetes/           # K8s manifests
│   ├── terraform/            # Terraform configs
│   └── docker/               # Docker configurations
│
├── docs/                     # Documentation
├── scripts/                  # Utility scripts
└── .github/                  # CI/CD workflows
```

### Technology Stack

**Frontend:**
- **React 18** with TypeScript
- **Vite** for build tooling
- **Material-UI** for components
- **React Query** for state management
- **React Router** for navigation
- **React Hook Form** for forms
- **Zod** for validation

**Backend:**
- **Node.js** with Express
- **TypeScript** for type safety
- **Prisma** ORM with PostgreSQL
- **Redis** for caching
- **JWT** for authentication
- **Multer** for file uploads
- **Winston** for logging

**Testing:**
- **Jest** for unit testing
- **React Testing Library** for component testing
- **Supertest** for API testing
- **Playwright** for E2E testing

**DevOps:**
- **Docker** for containerization
- **Kubernetes** for orchestration
- **GitHub Actions** for CI/CD
- **Terraform** for infrastructure

## Development Workflow

### Available Scripts

**Root Level:**
```bash
pnpm dev              # Start all development servers
pnpm build            # Build all packages
pnpm test             # Run all tests
pnpm test:watch       # Run tests in watch mode
pnpm test:coverage    # Run tests with coverage
pnpm lint             # Lint all packages
pnpm lint:fix         # Fix linting issues
pnpm type-check       # TypeScript type checking
pnpm format           # Format code with Prettier
pnpm clean            # Clean build artifacts
pnpm dev:setup        # Setup development environment
```

**Package-Specific:**
```bash
# Frontend
pnpm --filter frontend dev
pnpm --filter frontend build
pnpm --filter frontend test
pnpm --filter frontend storybook

# Backend
pnpm --filter backend dev
pnpm --filter backend build
pnpm --filter backend test
pnpm --filter backend db:migrate
pnpm --filter backend db:seed
pnpm --filter backend db:studio

# Mobile
pnpm --filter mobile start
pnpm --filter mobile android
pnpm --filter mobile ios
```

### Feature Development Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/deal-management-improvements
   ```

2. **Development**
   ```bash
   # Start development servers
   pnpm dev
   
   # Make changes to code
   # Run tests continuously
   pnpm test:watch
   ```

3. **Testing**
   ```bash
   # Run all tests
   pnpm test
   
   # Run specific tests
   pnpm --filter backend test -- deals
   pnpm --filter frontend test -- DealCard
   ```

4. **Code Quality**
   ```bash
   # Lint and format
   pnpm lint:fix
   pnpm format
   
   # Type checking
   pnpm type-check
   ```

5. **Commit & Push**
   ```bash
   git add .
   git commit -m "feat(deals): add deal status filtering"
   git push origin feature/deal-management-improvements
   ```

### Database Management

```bash
# Create migration
pnpm --filter backend db:migrate:create --name add_deal_status

# Run migrations
pnpm --filter backend db:migrate

# Reset database (dev only)
pnpm --filter backend db:reset

# Seed database
pnpm --filter backend db:seed

# Open Prisma Studio
pnpm --filter backend db:studio

# Generate Prisma client
pnpm --filter backend db:generate
```

## Testing Strategy

### Test Structure

```
packages/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   └── __tests__/
│   │   ├── hooks/
│   │   │   └── __tests__/
│   │   └── utils/
│   │       └── __tests__/
│   └── e2e/
│
└── backend/
    ├── src/
    │   ├── controllers/
    │   │   └── __tests__/
    │   ├── services/
    │   │   └── __tests__/
    │   └── utils/
    │       └── __tests__/
    └── tests/
        ├── integration/
        └── fixtures/
```

### Testing Examples

**Frontend Component Test:**
```typescript
// packages/frontend/src/components/__tests__/DealCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { DealCard } from '../DealCard'

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  })
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('DealCard', () => {
  const mockDeal = {
    id: '1',
    name: 'Acme Corp Acquisition',
    value: *********,
    status: 'active',
    targetCompany: 'Acme Corp'
  }

  it('renders deal information correctly', () => {
    render(<DealCard deal={mockDeal} />, { wrapper: createWrapper() })
    
    expect(screen.getByText('Acme Corp Acquisition')).toBeInTheDocument()
    expect(screen.getByText('$100,000,000')).toBeInTheDocument()
    expect(screen.getByText('Active')).toBeInTheDocument()
  })

  it('handles deal status update', async () => {
    const onStatusUpdate = jest.fn()
    render(
      <DealCard deal={mockDeal} onStatusUpdate={onStatusUpdate} />,
      { wrapper: createWrapper() }
    )
    
    fireEvent.click(screen.getByRole('button', { name: /update status/i }))
    fireEvent.click(screen.getByText('Due Diligence'))
    
    expect(onStatusUpdate).toHaveBeenCalledWith('1', 'due_diligence')
  })
})
```

**Backend Service Test:**
```typescript
// packages/backend/src/services/__tests__/dealService.test.ts
import { DealService } from '../dealService'
import { prismaMock } from '../../utils/prismaMock'
import { createMockUser } from '../../tests/fixtures/user'

describe('DealService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createDeal', () => {
    it('creates a new deal successfully', async () => {
      const user = createMockUser()
      const dealData = {
        name: 'Test Deal',
        value: 1000000,
        currency: 'USD',
        targetCompany: 'Target Corp',
        tenantId: user.tenantId
      }

      const expectedDeal = {
        id: '1',
        ...dealData,
        status: 'prospecting',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      prismaMock.deal.create.mockResolvedValue(expectedDeal)

      const result = await DealService.createDeal(dealData, user)

      expect(result).toEqual(expectedDeal)
      expect(prismaMock.deal.create).toHaveBeenCalledWith({
        data: {
          ...dealData,
          status: 'prospecting',
          createdBy: user.id
        }
      })
    })

    it('throws error for invalid deal data', async () => {
      const user = createMockUser()
      const invalidDealData = {
        name: '',
        value: -1000,
        tenantId: user.tenantId
      }

      await expect(
        DealService.createDeal(invalidDealData, user)
      ).rejects.toThrow('Invalid deal data')
    })
  })
})
```

**Integration Test:**
```typescript
// packages/backend/tests/integration/deals.test.ts
import request from 'supertest'
import { app } from '../../src/app'
import { createTestUser, createTestDeal } from '../fixtures'
import { generateJWT } from '../../src/utils/auth'

describe('Deals API', () => {
  let authToken: string
  let testUser: any

  beforeEach(async () => {
    testUser = await createTestUser()
    authToken = generateJWT(testUser)
  })

  describe('POST /api/deals', () => {
    it('creates a new deal', async () => {
      const dealData = {
        name: 'Integration Test Deal',
        value: 5000000,
        currency: 'USD',
        targetCompany: 'Test Corp'
      }

      const response = await request(app)
        .post('/api/deals')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', testUser.tenantId)
        .send(dealData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.name).toBe(dealData.name)
      expect(response.body.data.value).toBe(dealData.value)
    })
  })

  describe('GET /api/deals', () => {
    it('returns paginated deals', async () => {
      // Create test deals
      await createTestDeal({ tenantId: testUser.tenantId })
      await createTestDeal({ tenantId: testUser.tenantId })

      const response = await request(app)
        .get('/api/deals?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', testUser.tenantId)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveLength(2)
      expect(response.body.pagination.total).toBe(2)
    })
  })
})
```

### E2E Testing

```typescript
// packages/frontend/e2e/deal-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Deal Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('[data-testid=email]', '<EMAIL>')
    await page.fill('[data-testid=password]', 'password')
    await page.click('[data-testid=login-button]')
    await expect(page).toHaveURL('/dashboard')
  })

  test('creates a new deal', async ({ page }) => {
    await page.click('[data-testid=new-deal-button]')
    await expect(page).toHaveURL('/deals/new')

    await page.fill('[data-testid=deal-name]', 'E2E Test Deal')
    await page.fill('[data-testid=deal-value]', '10000000')
    await page.selectOption('[data-testid=deal-currency]', 'USD')
    await page.fill('[data-testid=target-company]', 'E2E Target Corp')

    await page.click('[data-testid=create-deal-button]')

    await expect(page).toHaveURL(/\/deals\/\w+/)
    await expect(page.locator('[data-testid=deal-name]')).toContainText('E2E Test Deal')
  })

  test('updates deal status', async ({ page }) => {
    // Assuming a deal exists
    await page.goto('/deals')
    await page.click('[data-testid=deal-card]:first-child')

    await page.click('[data-testid=status-dropdown]')
    await page.click('[data-testid=status-due-diligence]')

    await expect(page.locator('[data-testid=deal-status]')).toContainText('Due Diligence')
  })
})
```

## Code Quality & Standards

### ESLint Configuration

```json
// .eslintrc.json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react/prop-types": "off",
    "import/order": [
      "error",
      {
        "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
        "newlines-between": "always"
      }
    ]
  }
}
```

### Prettier Configuration

```json
// .prettierrc
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false
}
```

### TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/utils/*": ["src/utils/*"]
    }
  },
  "include": ["src"],
  "exclude": ["node_modules"]
}
```

### Git Hooks (Husky)

```json
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

pnpm lint-staged
```

```json
// package.json
{
  "lint-staged": {
    "*.{ts,tsx,js,jsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,md,yml,yaml}": [
      "prettier --write"
    ]
  }
}
```

### Commit Message Convention

We use Conventional Commits:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting
- `refactor`: Code restructuring
- `test`: Adding tests
- `chore`: Maintenance

**Examples:**
```
feat(deals): add deal status filtering
fix(auth): resolve JWT token expiration
docs(api): update authentication guide
test(deals): add integration tests
```

## Debugging & Troubleshooting

### VS Code Debugging

**Frontend Debug Configuration:**
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Frontend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/packages/frontend/node_modules/.bin/vite",
      "args": ["dev"],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development"
      }
    }
  ]
}
```

**Backend Debug Configuration:**
```json
{
  "name": "Debug Backend",
  "type": "node",
  "request": "launch",
  "program": "${workspaceFolder}/packages/backend/src/index.ts",
  "outFiles": ["${workspaceFolder}/packages/backend/dist/**/*.js"],
  "runtimeArgs": ["-r", "ts-node/register"],
  "env": {
    "NODE_ENV": "development"
  },
  "console": "integratedTerminal"
}
```

### Common Issues & Solutions

**Port Already in Use:**
```bash
# Find process using port
lsof -i :3000
kill -9 <PID>

# Or use different port
PORT=3001 pnpm dev
```

**Database Connection Issues:**
```bash
# Check Docker services
docker-compose ps

# Restart database
docker-compose restart postgres

# Reset database
pnpm --filter backend db:reset
```

**Module Resolution Issues:**
```bash
# Clear all caches
pnpm clean
rm -rf node_modules
rm pnpm-lock.yaml
pnpm install

# Clear TypeScript cache
rm -rf packages/*/tsconfig.tsbuildinfo
```

**Memory Issues:**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
pnpm dev
```

### Logging & Monitoring

**Backend Logging:**
```typescript
// packages/backend/src/utils/logger.ts
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.simple()
    }),
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error'
    }),
    new winston.transports.File({
      filename: 'logs/combined.log'
    })
  ]
})
```

**Frontend Error Tracking:**
```typescript
// packages/frontend/src/utils/errorTracking.ts
import * as Sentry from '@sentry/react'

if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: process.env.VITE_SENTRY_DSN,
    environment: process.env.NODE_ENV
  })
}

export const captureError = (error: Error, context?: any) => {
  console.error(error)
  if (process.env.NODE_ENV === 'production') {
    Sentry.captureException(error, { extra: context })
  }
}
```

## Deployment

### Development Deployment

```bash
# Build all packages
pnpm build

# Run production build locally
pnpm start

# Docker deployment
docker-compose -f docker-compose.prod.yml up -d
```

### Staging Deployment

```bash
# Deploy to staging
git push origin develop

# GitHub Actions will automatically:
# 1. Run tests
# 2. Build Docker images
# 3. Deploy to staging environment
```

### Production Deployment

```bash
# Create release
git checkout main
git merge develop
git tag v1.0.0
git push origin main --tags

# GitHub Actions will automatically:
# 1. Run full test suite
# 2. Build and push Docker images
# 3. Deploy to production with zero downtime
```

---

For additional help, check our [troubleshooting guide](./troubleshooting.md) or contact the development <NAME_EMAIL>
