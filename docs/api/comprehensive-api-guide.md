# M&A Platform Comprehensive API Guide

Complete API documentation for the M&A Platform, providing programmatic access to all platform features.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Rate Limiting](#rate-limiting)
4. [Request/Response Format](#requestresponse-format)
5. [Error Handling](#error-handling)
6. [Core API Endpoints](#core-api-endpoints)
7. [Advanced Features](#advanced-features)
8. [Webhooks](#webhooks)
9. [SDKs & Examples](#sdks--examples)

## Overview

The M&A Platform API provides comprehensive access to:

- **Deal Management**: Complete deal lifecycle management
- **Virtual Data Room**: Secure document management
- **Due Diligence**: Workflow and checklist management
- **Financial Modeling**: Valuation and analysis tools
- **User Management**: Authentication and authorization
- **Analytics**: Reporting and business intelligence
- **Workflow Automation**: Process automation and triggers

### Base URLs

```
Production:  https://api.mnaplatform.com/v1
Staging:     https://staging-api.mnaplatform.com/v1
Development: http://localhost:3001/api/v1
```

### API Versioning

The API uses URL-based versioning. Current version: `v1`

## Authentication

### JWT Bearer Token (Recommended)

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-Tenant-ID: tenant_123
```

**Login Request:**
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password",
  "tenantId": "tenant_123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiresIn": 3600,
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "role": "deal_manager",
      "permissions": ["deals:read", "deals:write"]
    }
  }
}
```

### API Key Authentication

```http
X-API-Key: mna_live_sk_1234567890abcdef
X-Tenant-ID: tenant_123
```

## Rate Limiting

| Plan | Requests/Hour | Burst Limit |
|------|---------------|-------------|
| Free | 1,000 | 100 |
| Professional | 10,000 | 500 |
| Enterprise | 100,000 | 2,000 |

**Headers:**
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Request/Response Format

### Standard Response

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2023-12-01T10:00:00Z",
    "requestId": "req_123456789",
    "version": "v1"
  }
}
```

### Paginated Response

```json
{
  "success": true,
  "data": [
    // Array of items
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email is required",
        "code": "REQUIRED"
      }
    ]
  }
}
```

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | OK |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Validation Error |
| 429 | Rate Limited |
| 500 | Server Error |

## Core API Endpoints

### Authentication

#### Login
```http
POST /auth/login
```

#### Refresh Token
```http
POST /auth/refresh
Authorization: Bearer <token>

{
  "refreshToken": "refresh_token_here"
}
```

#### Logout
```http
POST /auth/logout
Authorization: Bearer <token>
```

### Deal Management

#### List Deals
```http
GET /deals?page=1&limit=20&status=active&sort=createdAt:desc
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (max: 100)
- `status`: Filter by status
- `sort`: Sort field and direction
- `search`: Search term
- `assignedTo`: Filter by assigned user
- `dealType`: Filter by deal type

#### Create Deal
```http
POST /deals
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "name": "Acme Corp Acquisition",
  "description": "Strategic acquisition of Acme Corp",
  "targetCompany": {
    "name": "Acme Corp",
    "industry": "Technology",
    "revenue": 50000000,
    "employees": 500,
    "location": "San Francisco, CA"
  },
  "dealValue": *********,
  "currency": "USD",
  "expectedCloseDate": "2024-06-30",
  "dealType": "acquisition",
  "status": "prospecting",
  "priority": "high",
  "teamMembers": [
    {
      "userId": "user_123",
      "role": "lead",
      "permissions": ["read", "write", "admin"]
    }
  ],
  "tags": ["strategic", "technology"],
  "confidentialityLevel": "restricted"
}
```

#### Get Deal Details
```http
GET /deals/{dealId}
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Update Deal
```http
PUT /deals/{dealId}
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "status": "due_diligence",
  "dealValue": *********,
  "notes": "Updated valuation based on latest financials"
}
```

#### Update Deal Stage
```http
POST /deals/{dealId}/stage
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "stage": "due_diligence",
  "notes": "Moving to due diligence phase",
  "approvedBy": "user_456"
}
```

### Document Management (VDR)

#### Upload Document
```http
POST /documents
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
Content-Type: multipart/form-data

file: [binary data]
dealId: deal_123
category: financial_statements
subcategory: annual_reports
description: 2023 Annual Report
accessLevel: restricted
tags: ["2023", "annual", "audited"]
```

#### List Documents
```http
GET /documents?dealId=deal_123&category=financial&page=1&limit=20
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Get Document Metadata
```http
GET /documents/{documentId}
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Download Document
```http
GET /documents/{documentId}/download
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

**Response:**
```http
Content-Type: application/pdf
Content-Disposition: attachment; filename="annual-report-2023.pdf"
X-Document-Watermark: <EMAIL> - 2023-12-01
```

#### Update Document Permissions
```http
PUT /documents/{documentId}/permissions
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "accessLevel": "restricted",
  "allowedUsers": ["user_123", "user_456"],
  "allowedRoles": ["deal_manager", "analyst"],
  "expiresAt": "2024-06-30T23:59:59Z",
  "downloadEnabled": false,
  "watermarkEnabled": true
}
```

### Due Diligence Management

#### List DD Checklists
```http
GET /due-diligence/checklists?dealId=deal_123&category=financial
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Create DD Item
```http
POST /due-diligence/items
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "dealId": "deal_123",
  "checklistId": "checklist_456",
  "title": "Review 3-year financial statements",
  "description": "Analyze revenue trends and profitability",
  "category": "financial",
  "priority": "high",
  "assignedTo": "user_789",
  "dueDate": "2024-01-15",
  "requiredDocuments": ["income_statement", "balance_sheet"],
  "dependencies": ["item_123"]
}
```

#### Update DD Item Status
```http
PUT /due-diligence/items/{itemId}
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "status": "completed",
  "completedBy": "user_789",
  "completedAt": "2023-12-01T15:30:00Z",
  "notes": "Financial statements reviewed. No major concerns identified.",
  "findings": [
    {
      "type": "observation",
      "severity": "low",
      "description": "Minor accounting policy change in 2023"
    }
  ]
}
```

### User Management

#### List Users
```http
GET /users?role=analyst&status=active&page=1&limit=20
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Create User
```http
POST /users
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "analyst",
  "department": "Corporate Development",
  "permissions": ["deals:read", "documents:read", "analytics:read"],
  "sendInvitation": true,
  "temporaryPassword": false
}
```

#### Get User Profile
```http
GET /users/{userId}
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Update User Permissions
```http
PUT /users/{userId}/permissions
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "permissions": [
    "deals:read",
    "deals:write",
    "documents:read",
    "documents:write",
    "analytics:read"
  ],
  "dealAccess": [
    {
      "dealId": "deal_123",
      "level": "full"
    }
  ]
}
```

### Financial Modeling

#### Create Financial Model
```http
POST /financial-models
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "dealId": "deal_123",
  "modelType": "dcf",
  "name": "Acme Corp DCF Analysis",
  "assumptions": {
    "revenueGrowthRate": 0.15,
    "discountRate": 0.12,
    "terminalGrowthRate": 0.03,
    "projectionYears": 5
  },
  "scenarios": ["base", "optimistic", "pessimistic"]
}
```

#### Get Model Results
```http
GET /financial-models/{modelId}/results?scenario=base
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

### Analytics & Reporting

#### Deal Pipeline Analytics
```http
GET /analytics/deals/pipeline?period=30d&groupBy=stage
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Performance Metrics
```http
GET /analytics/performance?startDate=2023-01-01&endDate=2023-12-31&metrics=deal_velocity,success_rate
Authorization: Bearer <token>
X-Tenant-ID: tenant_123
```

#### Generate Report
```http
POST /reports/generate
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "reportType": "deal_summary",
  "dealId": "deal_123",
  "format": "pdf",
  "sections": ["overview", "financials", "timeline", "documents"],
  "includeConfidential": false
}
```

## Advanced Features

### Bulk Operations

#### Bulk Update Deals
```http
POST /deals/bulk-update
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "dealIds": ["deal_123", "deal_456", "deal_789"],
  "updates": {
    "status": "on_hold",
    "tags": ["covid_impact"]
  }
}
```

### Search & Filtering

#### Advanced Search
```http
POST /search
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "query": "technology acquisition",
  "filters": {
    "dealValue": {
      "min": 10000000,
      "max": *********
    },
    "status": ["active", "due_diligence"],
    "dateRange": {
      "start": "2023-01-01",
      "end": "2023-12-31"
    }
  },
  "sort": [
    {"field": "dealValue", "direction": "desc"},
    {"field": "createdAt", "direction": "desc"}
  ]
}
```

### Workflow Automation

#### Create Automation Rule
```http
POST /automation/rules
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "name": "Auto-assign analyst on deal creation",
  "trigger": {
    "event": "deal.created",
    "conditions": {
      "dealValue": {"gte": 50000000}
    }
  },
  "actions": [
    {
      "type": "assign_user",
      "userId": "user_senior_analyst"
    },
    {
      "type": "send_notification",
      "template": "deal_assignment",
      "recipients": ["user_senior_analyst", "user_manager"]
    }
  ],
  "enabled": true
}
```

## Webhooks

### Configure Webhook
```http
POST /webhooks
Authorization: Bearer <token>
X-Tenant-ID: tenant_123

{
  "url": "https://your-app.com/webhooks/mna",
  "events": [
    "deal.created",
    "deal.updated",
    "deal.stage_changed",
    "document.uploaded",
    "due_diligence.completed"
  ],
  "secret": "webhook_secret_key",
  "active": true,
  "retryPolicy": {
    "maxRetries": 3,
    "retryDelay": 5000
  }
}
```

### Webhook Payload Example
```json
{
  "id": "evt_123456789",
  "event": "deal.stage_changed",
  "data": {
    "deal": {
      "id": "deal_123",
      "name": "Acme Corp Acquisition",
      "previousStage": "prospecting",
      "currentStage": "due_diligence",
      "changedBy": "user_456",
      "changedAt": "2023-12-01T10:00:00Z"
    }
  },
  "timestamp": "2023-12-01T10:00:00Z",
  "tenantId": "tenant_123"
}
```

## SDKs & Examples

### JavaScript/TypeScript SDK
```bash
npm install @mnaplatform/sdk
```

```typescript
import { MNAPlatform } from '@mnaplatform/sdk';

const client = new MNAPlatform({
  apiKey: 'mna_live_sk_...',
  tenantId: 'tenant_123',
  baseUrl: 'https://api.mnaplatform.com/v1'
});

// List deals
const deals = await client.deals.list({
  status: 'active',
  limit: 10
});

// Create deal
const newDeal = await client.deals.create({
  name: 'New Acquisition',
  dealValue: 50000000,
  currency: 'USD'
});

// Upload document
const document = await client.documents.upload({
  file: fileBuffer,
  dealId: newDeal.id,
  category: 'financial'
});
```

### Python SDK
```bash
pip install mnaplatform-python
```

```python
import mnaplatform

client = mnaplatform.Client(
    api_key='mna_live_sk_...',
    tenant_id='tenant_123'
)

# List deals
deals = client.deals.list(status='active', limit=10)

# Create deal
new_deal = client.deals.create({
    'name': 'New Acquisition',
    'deal_value': 50000000,
    'currency': 'USD'
})
```

### cURL Examples
```bash
# Authentication
curl -X POST "https://api.mnaplatform.com/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","tenantId":"tenant_123"}'

# List deals
curl -X GET "https://api.mnaplatform.com/v1/deals?status=active&limit=10" \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: tenant_123"

# Create deal
curl -X POST "https://api.mnaplatform.com/v1/deals" \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: tenant_123" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Strategic Acquisition",
    "dealValue": *********,
    "currency": "USD",
    "dealType": "acquisition"
  }'
```

---

**API Version**: v1  
**Last Updated**: December 2023  
**Support**: <EMAIL>  
**Status Page**: https://status.mnaplatform.com
