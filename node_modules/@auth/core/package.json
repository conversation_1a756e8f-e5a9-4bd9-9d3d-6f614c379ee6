{"name": "@auth/core", "version": "0.18.6", "description": "Authentication for the Web.", "keywords": ["authentication", "authjs", "jwt", "o<PERSON>h", "oidc", "passwordless", "standard", "vanilla", "webapi"], "homepage": "https://authjs.dev", "repository": "https://github.com/nextauthjs/next-auth.git", "author": "Balá<PERSON>s <PERSON> <<EMAIL>>", "contributors": ["Balá<PERSON>s <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>"], "type": "module", "types": "./index.d.ts", "files": ["*.js", "*.d.ts*", "lib", "providers", "src", "!vitest.config.js"], "exports": {".": {"types": "./index.d.ts", "import": "./index.js"}, "./adapters": {"types": "./adapters.d.ts"}, "./errors": {"types": "./errors.d.ts", "import": "./errors.js"}, "./jwt": {"types": "./jwt.d.ts", "import": "./jwt.js"}, "./providers": {"types": "./providers/index.d.ts"}, "./providers/*": {"types": "./providers/*.d.ts", "import": "./providers/*.js"}, "./types": {"types": "./types.d.ts"}}, "license": "ISC", "dependencies": {"@panva/hkdf": "^1.1.1", "cookie": "0.6.0", "@types/cookie": "0.6.0", "jose": "^5.1.3", "oauth4webapi": "^2.4.0", "preact": "10.11.3", "preact-render-to-string": "5.2.3"}, "peerDependencies": {"nodemailer": "^6.8.0"}, "peerDependenciesMeta": {"nodemailer": {"optional": true}}, "devDependencies": {"@types/node": "18.11.10", "@types/nodemailer": "6.4.6", "@types/react": "18.0.37", "autoprefixer": "10.4.13", "postcss": "8.4.19", "postcss-nested": "6.0.0", "vite": "^5.0.2", "vitest": "^0.25.3"}, "scripts": {"build": "pnpm css && pnpm providers && tsc", "clean": "rm -rf *.js *.d.ts* lib providers", "css": "node scripts/generate-css", "dev": "pnpm css && pnpm providers && tsc -w", "test": "vitest", "providers": "node scripts/generate-providers"}}