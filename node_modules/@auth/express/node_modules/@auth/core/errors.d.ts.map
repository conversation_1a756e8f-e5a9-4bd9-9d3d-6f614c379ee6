{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["src/errors.ts"], "names": [], "mappings": "AAAA,KAAK,YAAY,GAAG,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;AAEnD,KAAK,SAAS,GACV,cAAc,GACd,cAAc,GACd,oBAAoB,GACpB,eAAe,GACf,YAAY,GACZ,oBAAoB,GACpB,mBAAmB,GACnB,kBAAkB,GAClB,cAAc,GACd,iBAAiB,GACjB,gBAAgB,GAChB,uBAAuB,GACvB,kBAAkB,GAClB,eAAe,GACf,uBAAuB,GACvB,oBAAoB,GACpB,wBAAwB,GACxB,mBAAmB,GACnB,kBAAkB,GAClB,kBAAkB,GAClB,cAAc,GACd,eAAe,GACf,qBAAqB,GACrB,iBAAiB,GACjB,eAAe,GACf,cAAc,GACd,aAAa,GACb,kBAAkB,GAClB,wBAAwB,GACxB,6BAA6B,GAC7B,2BAA2B,GAC3B,+BAA+B,CAAA;AAEnC;;;;GAIG;AACH,qBAAa,SAAU,SAAQ,KAAK;IAClC,8DAA8D;IAC9D,IAAI,EAAE,SAAS,CAAA;IAOf,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;QAAE,GAAG,CAAC,EAAE,KAAK,CAAA;KAAE,CAAA;gBAE/C,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,GAAG,YAAY,EACvC,YAAY,CAAC,EAAE,YAAY;CAwB9B;AAED,qBAAa,WAAY,SAAQ,SAAS;IACxC,MAAM,CAAC,IAAI,SAAW;CACvB;AAED;;;;;;;;;;;;GAYG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;GAGG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,MAAM,CAAC,IAAI,SAAuB;CACnC;AAED;;;;;;;GAOG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;;;;GAOG;AACH,qBAAa,UAAW,SAAQ,SAAS;IACvC,MAAM,CAAC,IAAI,SAAe;CAC3B;AAED;;;;;;;;;GASG;AACH,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,MAAM,CAAC,IAAI,SAAuB;CACnC;AAED;;;;;GAKG;AACH,qBAAa,iBAAkB,SAAQ,KAAK;IAC1C,MAAM,CAAC,IAAI,SAAsB;IACjC;;;;;;;;;;OAUG;IACH,IAAI,EAAE,MAAM,CAAgB;CAC7B;AAED;;;;;GAKG;AACH,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;GAKG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;;;;;;GAUG;AACH,qBAAa,eAAgB,SAAQ,SAAS;IAC5C,MAAM,CAAC,IAAI,SAAoB;CAChC;AAED;;;;;;GAMG;AACH,qBAAa,cAAe,SAAQ,SAAS;IAC3C,MAAM,CAAC,IAAI,SAAmB;CAC/B;AAED;;;;;;GAMG;AACH,qBAAa,qBAAsB,SAAQ,SAAS;IAClD,MAAM,CAAC,IAAI,SAA0B;CACtC;AAED;;;;;GAKG;AACH,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;;;;;;;GAYG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;;;;;;;;GAWG;AACH,qBAAa,qBAAsB,SAAQ,WAAW;IACpD,MAAM,CAAC,IAAI,SAA0B;CACtC;AAED;;;;;GAKG;AACH,qBAAa,kBAAmB,SAAQ,WAAW;IACjD,MAAM,CAAC,IAAI,SAAuB;CACnC;AAED;;;;GAIG;AACH,qBAAa,sBAAuB,SAAQ,SAAS;IACnD,MAAM,CAAC,IAAI,SAA2B;CACvC;AAED;;;;;;GAMG;AACH,qBAAa,iBAAkB,SAAQ,SAAS;IAC9C,MAAM,CAAC,IAAI,SAAsB;CAClC;AAED;;;;;;;;;;;;;;GAcG;AACH,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;;;;GASG;AACH,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;;;;GASG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;GAIG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;GAIG;AACH,qBAAa,mBAAoB,SAAQ,SAAS;IAChD,MAAM,CAAC,IAAI,SAAwB;CACpC;AAED,0GAA0G;AAC1G,qBAAa,eAAgB,SAAQ,SAAS;IAC5C,MAAM,CAAC,IAAI,SAAoB;CAChC;AAED;;;;;;;;;;GAUG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;GAIG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;;;;;GASG;AACH,qBAAa,WAAY,SAAQ,WAAW;IAC1C,MAAM,CAAC,IAAI,SAAgB;CAC5B;AAuBD;;;GAGG;AACH,qBAAa,sBAAuB,SAAQ,SAAS;IACnD,MAAM,CAAC,IAAI,SAA2B;CACvC;AAED;;;;GAIG;AACH,qBAAa,2BAA4B,SAAQ,SAAS;IACxD,MAAM,CAAC,IAAI,SAAgC;CAC5C;AAED;;GAEG;AACH,qBAAa,yBAA0B,SAAQ,SAAS;IACtD,MAAM,CAAC,IAAI,SAA8B;CAC1C;AAED;;;;;GAKG;AACH,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;GAEG;AACH,qBAAa,6BAA8B,SAAQ,SAAS;IAC1D,MAAM,CAAC,IAAI,SAAkC;CAC9C"}