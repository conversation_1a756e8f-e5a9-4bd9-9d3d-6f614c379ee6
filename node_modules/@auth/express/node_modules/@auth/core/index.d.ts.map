{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AASH,OAAO,EAAgB,GAAG,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAA;AACjE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAA;AAEpE,OAAO,EAAqB,KAAK,cAAc,EAAE,MAAM,uBAAuB,CAAA;AAG9E,OAAO,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,eAAe,CAAA;AACzE,OAAO,KAAK,EACV,OAAO,EAEP,SAAS,EACT,cAAc,EACd,cAAc,EACd,YAAY,EACZ,OAAO,EACP,gBAAgB,EAChB,OAAO,EACP,KAAK,EACL,IAAI,EACL,MAAM,YAAY,CAAA;AACnB,OAAO,KAAK,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAA;AACrE,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,UAAU,CAAA;AAC1C,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAA;AAErD,OAAO,EAAE,aAAa,EAAE,GAAG,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,EAAE,CAAA;AAE5E,wBAAsB,IAAI,CACxB,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,UAAU,GAAG;IAAE,GAAG,EAAE,OAAO,GAAG,CAAA;CAAE,GACvC,OAAO,CAAC,gBAAgB,CAAC,CAAA;AAE5B,wBAAsB,IAAI,CACxB,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,GAC9B,OAAO,CAAC,QAAQ,CAAC,CAAA;AAuHpB;;;;;;;;;;;;;;GAcG;AACH,MAAM,WAAW,UAAU;IACzB;;;;;;OAMG;IACH,SAAS,EAAE,QAAQ,EAAE,CAAA;IACrB;;;;;;;;;;OAUG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;IAC1B;;;OAGG;IACH,OAAO,CAAC,EAAE;QACR;;;;;;;;;;;WAWG;QACH,QAAQ,CAAC,EAAE,KAAK,GAAG,UAAU,CAAA;QAC7B;;;;WAIG;QACH,MAAM,CAAC,EAAE,MAAM,CAAA;QACf;;;;;WAKG;QACH,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB;;;;;;WAMG;QACH,oBAAoB,CAAC,EAAE,MAAM,MAAM,CAAA;KACpC,CAAA;IACD;;;OAGG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;IACzB;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,CAAA;IAC7B;;;;OAIG;IACH,SAAS,CAAC,EAAE;QACV;;;;;;;;;;;;;;;;;;WAkBG;QACH,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;YAChB,IAAI,EAAE,IAAI,GAAG,WAAW,CAAA;YACxB,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;YACvB;;;eAGG;YACH,OAAO,CAAC,EAAE,OAAO,CAAA;YACjB;;;;;;;eAOG;YACH,KAAK,CAAC,EAAE;gBACN,mBAAmB,CAAC,EAAE,OAAO,CAAA;aAC9B,CAAA;YACD,wEAAwE;YACxE,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;SAC9C,KAAK,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA;QACjC;;;;;;;;;;;;;;;;;;;WAmBG;QACH,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE;YAClB,iDAAiD;YACjD,GAAG,EAAE,MAAM,CAAA;YACX,yDAAyD;YACzD,OAAO,EAAE,MAAM,CAAA;SAChB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA;QACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;WA4BG;QACH,OAAO,CAAC,EAAE,CACR,MAAM,EAAE,CAAC;YACP,OAAO,EAAE;gBAAE,IAAI,EAAE,WAAW,CAAA;aAAE,GAAG,cAAc,CAAA;YAC/C,kFAAkF;YAClF,IAAI,EAAE,WAAW,CAAA;SAClB,GAAG;YACF,OAAO,EAAE,OAAO,CAAA;YAChB,4EAA4E;YAC5E,KAAK,EAAE,GAAG,CAAA;SACX,CAAC,GAAG;YACH;;;;;;eAMG;YACH,UAAU,EAAE,GAAG,CAAA;YACf,OAAO,CAAC,EAAE,QAAQ,CAAA;SACnB,KACE,SAAS,CAAC,OAAO,GAAG,cAAc,CAAC,CAAA;QACxC;;;;;;;;;WASG;QACH,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE;YACb;;;;;eAKG;YACH,KAAK,EAAE,GAAG,CAAA;YACV;;;;;;;eAOG;YACH,IAAI,EAAE,IAAI,GAAG,WAAW,CAAA;YACxB;;;;eAIG;YACH,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;YACvB;;;;eAIG;YACH,OAAO,CAAC,EAAE,OAAO,CAAA;YACjB;;;;;;eAMG;YACH,OAAO,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAA;YACxC,qDAAqD;YACrD,SAAS,CAAC,EAAE,OAAO,CAAA;YACnB;;;;;eAKG;YACH,OAAO,CAAC,EAAE,GAAG,CAAA;SACd,KAAK,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,CAAA;KAC5B,CAAA;IACD;;;;;;;;;OASG;IACH,MAAM,CAAC,EAAE;QACP;;;;;WAKG;QACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE;YACjB,IAAI,EAAE,IAAI,CAAA;YACV,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;YACvB,OAAO,CAAC,EAAE,OAAO,CAAA;YACjB,SAAS,CAAC,EAAE,OAAO,CAAA;SACpB,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;QACrB;;;;;WAKG;QACH,OAAO,CAAC,EAAE,CACR,OAAO,EACH;YAAE,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;SAAE,GACpE;YAAE,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SAAE,KACrD,SAAS,CAAC,IAAI,CAAC,CAAA;QACpB,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE;YAAE,IAAI,EAAE,IAAI,CAAA;SAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;QACzD,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE;YAAE,IAAI,EAAE,IAAI,CAAA;SAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;QACzD,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE;YACtB,IAAI,EAAE,IAAI,GAAG,WAAW,CAAA;YACxB,OAAO,EAAE,OAAO,CAAA;YAChB,OAAO,EAAE,IAAI,GAAG,WAAW,CAAA;SAC5B,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;QACrB;;;;;WAKG;QACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;YAAE,OAAO,EAAE,OAAO,CAAC;YAAC,KAAK,EAAE,GAAG,CAAA;SAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;KACzE,CAAA;IACD,uEAAuE;IACvE,OAAO,CAAC,EAAE,OAAO,CAAA;IACjB;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,OAAO,CAAA;IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IAChC,8DAA8D;IAC9D,KAAK,CAAC,EAAE,KAAK,CAAA;IACb;;;;;;;;;;;OAWG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;IAC1B;;;;;;;;;;;;;OAaG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IACjC;;;;;;;;OAQG;IACH,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB,aAAa,CAAC,EAAE,OAAO,aAAa,CAAA;IACpC,GAAG,CAAC,EAAE,OAAO,GAAG,CAAA;IAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAA;IAEzB;;;;;OAKG;IACH,YAAY,CAAC,EAAE;QACb;;;;WAIG;QACH,cAAc,CAAC,EAAE,OAAO,CAAA;KACzB,CAAA;IACD;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB"}