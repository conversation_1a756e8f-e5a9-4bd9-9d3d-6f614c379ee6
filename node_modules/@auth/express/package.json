{"name": "@auth/express", "description": "Authentication for Express.", "version": "0.5.6", "type": "module", "files": ["*.js", "*.d.ts*", "lib", "providers", "src"], "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "default": "./index.js"}, "./providers": {"types": "./providers/index.d.ts"}, "./adapters": {"types": "./adapters.d.ts"}, "./providers/*": {"types": "./providers/*.d.ts", "import": "./providers/*.js", "default": "./providers/*.js"}, "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "dependencies": {"@auth/core": "0.34.1"}, "devDependencies": {"@auth/core": "experimental", "@types/express": "^4.17.17", "@types/supertest": "^2.0.12", "supertest": "^6.3.3"}, "peerDependencies": {"express": "^4.18.2"}, "keywords": ["Express", "Auth.js"], "author": "<PERSON><PERSON> Essilfie <<EMAIL>>", "contributors": ["<PERSON><PERSON> Essilfie <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "repository": "https://github.com/nextauthjs/next-auth", "license": "ISC", "scripts": {"build": "pnpm clean && pnpm providers && tsc", "clean": "rm -rf lib index.* src/lib/providers", "test": "vitest run -c ../utils/vitest.config.ts", "providers": "node ../utils/scripts/providers"}}