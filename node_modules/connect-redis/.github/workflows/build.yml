name: build
on:
  push:
    branches:
      - master
  pull_request:
jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node: [16, 18, 20]
    name: Node v${{ matrix.node }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node }}
      - run: sudo apt-get install -y redis-server
      - run: npm install
      - run: npm run fmt-check
      - run: npm run lint
      - run: npm test
