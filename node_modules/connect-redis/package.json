{"name": "connect-redis", "description": "Redis session store for Connect", "version": "7.1.1", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "main": "./dist/esm/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "default": "./dist/esm/index.js"}}, "types": "./dist/esm/index.d.ts", "scripts": {"prepublishOnly": "rm -rf dist && tsc & tsc --project tsconfig.esm.json && echo '{\"type\":\"module\"}' > dist/esm/package.json", "build": "npm run prepublishOnly", "test": "nyc ts-node node_modules/blue-tape/bin/blue-tape \"**/*_test.ts\"", "lint": "tsc --noemit && eslint --max-warnings 0 --ext ts testdata *.ts", "fmt": "prettier --write .", "fmt-check": "prettier --check ."}, "repository": {"type": "git", "url": "**************:tj/connect-redis.git"}, "devDependencies": {"@types/blue-tape": "^0.1.36", "@types/express-session": "^1.17.10", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "blue-tape": "^1.0.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "express-session": "^1.17.3", "ioredis": "^5.3.2", "nyc": "^15.1.0", "prettier": "^3.2.4", "prettier-plugin-organize-imports": "^3.2.4", "redis": "^4.6.12", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "peerDependencies": {"express-session": ">=1"}, "engines": {"node": ">=16"}, "bugs": {"url": "https://github.com/tj/connect-redis/issues"}, "keywords": ["connect", "redis", "session", "express"]}