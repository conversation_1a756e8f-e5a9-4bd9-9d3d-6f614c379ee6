{"version": 3, "file": "channel-credentials.js", "sourceRoot": "", "sources": ["../../src/channel-credentials.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAidH,kGAEC;AAjdD,6BAOa;AAEb,yDAAqD;AACrD,+CAAmE;AAInE,6CAAgE;AAChE,yCAAiD;AACjD,uCAAgC;AAChC,2CAA2C;AAE3C,8DAA8D;AAC9D,SAAS,oBAAoB,CAAC,GAAQ,EAAE,YAAoB;IAC1D,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,YAAY,MAAM,CAAC,EAAE,CAAC;QACpC,MAAM,IAAI,SAAS,CAAC,GAAG,YAAY,kCAAkC,CAAC,CAAC;IACzE,CAAC;AACH,CAAC;AAsCD;;;;GAIG;AACH,MAAsB,kBAAkB;IACtC;;;;;OAKG;IACH,OAAO,CAAC,eAAgC;QACtC,OAAO,IAAI,8BAA8B,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IACnE,CAAC;IAgBD;;;;;;;;OAQG;IACH,MAAM,CAAC,SAAS,CACd,SAAyB,EACzB,UAA0B,EAC1B,SAAyB,EACzB,aAA6B;;QAE7B,oBAAoB,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QACpD,oBAAoB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAChD,oBAAoB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;QACrD,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;QACJ,CAAC;QACD,MAAM,aAAa,GAAG,IAAA,yBAAmB,EAAC;YACxC,EAAE,EAAE,MAAA,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,IAAA,iCAAmB,GAAE,mCAAI,SAAS;YACnD,GAAG,EAAE,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,SAAS;YAC5B,IAAI,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,SAAS;YAC5B,OAAO,EAAE,2BAAa;SACvB,CAAC,CAAC;QACH,OAAO,IAAI,4BAA4B,CAAC,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,uBAAuB,CAC5B,aAA4B,EAC5B,aAA6B;QAE7B,OAAO,IAAI,4BAA4B,CAAC,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc;QACnB,OAAO,IAAI,8BAA8B,EAAE,CAAC;IAC9C,CAAC;CACF;AArFD,gDAqFC;AAED,MAAM,8BAA+B,SAAQ,kBAAkB;IAC7D;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAEQ,OAAO,CAAC,eAAgC;QAC/C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IACD,SAAS;QACP,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,CAAC,KAAyB;QAC/B,OAAO,KAAK,YAAY,8BAA8B,CAAC;IACzD,CAAC;IACD,sBAAsB,CAAC,aAAsB,EAAE,OAAuB,EAAE,eAAiC;QACvG,OAAO;YACL,OAAO,CAAC,MAAM;gBACZ,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,MAAM;oBACN,MAAM,EAAE,KAAK;iBACd,CAAC,CAAC;YACL,CAAC;YACD,YAAY,EAAE,GAAG,EAAE;gBACjB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC;YACD,kBAAkB,EAAE,GAAG,EAAE;gBACvB,OAAO,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,kCAAe,CAAC,WAAW,EAAE,CAAC;YAC1D,CAAC;YACD,OAAO,KAAI,CAAC;SACb,CAAA;IACH,CAAC;CACF;AAED,SAAS,oBAAoB,CAAC,aAA4B,EAAE,aAA4B,EAAE,aAAsB,EAAE,OAAuB;;IACvI,MAAM,iBAAiB,GAAsB;QAC3C,aAAa,EAAE,aAAa;KAC7B,CAAC;IACF,IAAI,UAAU,GAAY,aAAa,CAAC;IACxC,IAAI,0BAA0B,IAAI,OAAO,EAAE,CAAC;QAC1C,MAAM,YAAY,GAAG,IAAA,qBAAQ,EAAC,OAAO,CAAC,0BAA0B,CAAE,CAAC,CAAC;QACpE,IAAI,YAAY,EAAE,CAAC;YACjB,UAAU,GAAG,YAAY,CAAC;QAC5B,CAAC;IACH,CAAC;IACD,MAAM,UAAU,GAAG,IAAA,8BAAmB,EAAC,UAAU,CAAC,CAAC;IACnD,MAAM,QAAQ,GAAG,IAAA,0BAAa,EAAC,UAAU,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,mCAAI,UAAU,CAAC;IAChD,iBAAiB,CAAC,IAAI,GAAG,UAAU,CAAC;IAEpC,IAAI,aAAa,CAAC,mBAAmB,EAAE,CAAC;QACtC,iBAAiB,CAAC,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC;IAC5E,CAAC;IACD,IAAI,aAAa,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;QACnD,iBAAiB,CAAC,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC;IAC1E,CAAC;IACD,iBAAiB,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,OAAO,CAAC,+BAA+B,CAAC,EAAE,CAAC;QAC7C,MAAM,qBAAqB,GAAG,OAAO,CAAC,+BAA+B,CAAE,CAAC;QACxE,MAAM,2BAA2B,GAC/B,MAAA,iBAAiB,CAAC,mBAAmB,mCAAI,yBAAmB,CAAC;QAC/D,iBAAiB,CAAC,mBAAmB,GAAG,CACtC,IAAY,EACZ,IAAqB,EACF,EAAE;YACrB,OAAO,2BAA2B,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC;QACF,iBAAiB,CAAC,UAAU,GAAG,qBAAqB,CAAC;IACvD,CAAC;SAAM,CAAC;QACN,iBAAiB,CAAC,UAAU,GAAG,UAAU,CAAC;IAC5C,CAAC;IACD,IAAI,OAAO,CAAC,4BAA4B,CAAC,EAAE,CAAC;QAC1C,iBAAiB,CAAC,WAAW,GAAG,IAAI,CAAC;IACvC,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,MAAM,mBAAmB;IACvB,YAAoB,iBAAoC,EAAU,eAAgC;QAA9E,sBAAiB,GAAjB,iBAAiB,CAAmB;QAAU,oBAAe,GAAf,eAAe,CAAiB;IAClG,CAAC;IACD,OAAO,CAAC,MAAc;QACpB,MAAM,iBAAiB,mBACrB,MAAM,EAAE,MAAM,IACX,IAAI,CAAC,iBAAiB,CAC1B,CAAC;QACF,OAAO,IAAI,OAAO,CAAsB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1D,MAAM,SAAS,GAAG,IAAA,aAAU,EAAC,iBAAiB,EAAE,GAAG,EAAE;;gBACnD,IAAI,CAAC,MAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,mCAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;oBACjF,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;oBACrC,OAAO;gBACT,CAAC;gBACD,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;oBACjB,MAAM,EAAE,IAAI;iBACb,CAAC,CAAA;YACJ,CAAC,CAAC,CAAC;YACH,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;gBACrC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IACD,YAAY;QACV,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IACD,kBAAkB;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IACD,OAAO,KAAI,CAAC;CACb;AAED,MAAM,4BAA6B,SAAQ,kBAAkB;IAC3D,YACU,aAA4B,EAC5B,aAA4B;QAEpC,KAAK,EAAE,CAAC;QAHA,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;IAGtC,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,KAAyB;QAC/B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,YAAY,4BAA4B,EAAE,CAAC;YAClD,OAAO,CACL,IAAI,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa;gBAC1C,IAAI,CAAC,aAAa,CAAC,mBAAmB;oBACpC,KAAK,CAAC,aAAa,CAAC,mBAAmB,CAC1C,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,sBAAsB,CAAC,aAAsB,EAAE,OAAuB,EAAE,eAAiC;QACvG,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC/G,OAAO,IAAI,mBAAmB,CAAC,iBAAiB,EAAE,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,kCAAe,CAAC,WAAW,EAAE,CAAC,CAAC;IACtG,CAAC;CACF;AAED,MAAM,yCAA0C,SAAQ,kBAAkB;IAoExE,YACU,qBAA0C,EAC1C,2BAAuD,EACvD,aAA4B;QAEpC,KAAK,EAAE,CAAC;QAJA,0BAAqB,GAArB,qBAAqB,CAAqB;QAC1C,gCAA2B,GAA3B,2BAA2B,CAA4B;QACvD,kBAAa,GAAb,aAAa,CAAe;QAtE9B,aAAQ,GAAW,CAAC,CAAC;QAC7B;;;WAGG;QACK,mBAAc,GAA2C,SAAS,CAAC;QAC3E;;;WAGG;QACK,yBAAoB,GAAiD,SAAS,CAAC;QAC/E,gCAA2B,GAAgC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrG,sCAAiC,GAAsC,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzH,0BAAqB,GAAgD,EAAE,CAAC;IA4DhF,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,KAAyB;;QAC/B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,YAAY,yCAAyC,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC,qBAAqB,KAAK,KAAK,CAAC,qBAAqB;gBAC/D,IAAI,CAAC,2BAA2B,KAAK,KAAK,CAAC,2BAA2B;gBACtE,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,mBAAmB,OAAK,MAAA,KAAK,CAAC,aAAa,0CAAE,mBAAmB,CAAA,CAAC;QACzF,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACO,GAAG;;QACT,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACtF,MAAA,IAAI,CAAC,2BAA2B,0CAAE,8BAA8B,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC3G,CAAC;QACD,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;IACrB,CAAC;IACO,KAAK;;QACX,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzF,MAAA,IAAI,CAAC,2BAA2B,0CAAE,iCAAiC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IACD,sBAAsB,CAAC,aAAsB,EAAE,OAAuB,EAAE,eAAiC;QACvG,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,IAAI,yCAAyC,CAAC,mBAAmB,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,kCAAe,CAAC,WAAW,EAAE,CAAC,CAAC;IAC3J,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACjD,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,MAAkC;QAClE,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,iCAAiC,CAAC,MAAwC;QAChF,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;QACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAChF,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB;QACtB,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC3B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,sBAAsB;;QAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,2BAA2B,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5E,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,CAAC;YACH,OAAO,IAAA,yBAAmB,EAAC;gBACzB,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa;gBACrC,GAAG,EAAE,MAAA,IAAI,CAAC,oBAAoB,0CAAE,UAAU;gBAC1C,IAAI,EAAE,MAAA,IAAI,CAAC,oBAAoB,0CAAE,WAAW;gBAC5C,OAAO,EAAE,2BAAa;aACvB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAA,aAAG,EAAC,wBAAY,CAAC,KAAK,EAAE,2CAA2C,GAAI,CAAW,CAAC,OAAO,CAAC,CAAC;YAC5F,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;;AAvJc,6DAAmB,GAAG;IACnC,YAAoB,MAAiD,EAAU,aAAsB,EAAU,OAAuB,EAAU,eAAgC;QAA5J,WAAM,GAAN,MAAM,CAA2C;QAAU,kBAAa,GAAb,aAAa,CAAS;QAAU,YAAO,GAAP,OAAO,CAAgB;QAAU,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEpL,OAAO,CAAC,MAAc;QACpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YAC3D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC;gBAChD,OAAO;YACT,CAAC;YACD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC,CAAC;YAC/D,CAAC;YACD,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5H,MAAM,iBAAiB,mBACrB,MAAM,EAAE,MAAM,IACX,kBAAkB,CACtB,CAAA;YACD,MAAM,aAAa,GAAG,GAAG,EAAE;gBACzB,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC;YACF,MAAM,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;gBACrC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAA;YACD,MAAM,SAAS,GAAG,IAAA,aAAU,EAAC,iBAAiB,EAAE,GAAG,EAAE;;gBACnD,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;gBACjD,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;gBACjD,IAAI,CAAC,MAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,kBAAkB,mCAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;oBACpF,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;oBACrC,OAAO;gBACT,CAAC;gBACD,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;oBACjB,MAAM,EAAE,IAAI;iBACb,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACvC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;IACvC,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,OAAO;QACL,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;CACF,AApDiC,CAoDjC;AAsGH,SAAgB,2CAA2C,CAAC,qBAA0C,EAAE,2BAAuD,EAAE,aAA6B;IAC5L,OAAO,IAAI,yCAAyC,CAAC,qBAAqB,EAAE,2BAA2B,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,EAAE,CAAC,CAAC;AAChI,CAAC;AAED,MAAM,8BAA+B,SAAQ,kBAAkB;IAC7D,YACU,kBAAsC,EACtC,eAAgC;QAExC,KAAK,EAAE,CAAC;QAHA,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,oBAAe,GAAf,eAAe,CAAiB;QAGxC,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IACD,OAAO,CAAC,eAAgC;QACtC,MAAM,uBAAuB,GAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAChD,OAAO,IAAI,8BAA8B,CACvC,IAAI,CAAC,kBAAkB,EACvB,uBAAuB,CACxB,CAAC;IACJ,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,KAAyB;QAC/B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,YAAY,8BAA8B,EAAE,CAAC;YACpD,OAAO,CACL,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC;gBACzD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CACpD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,sBAAsB,CAAC,aAAsB,EAAE,OAAuB,EAAE,eAAiC;QACvG,MAAM,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,kCAAe,CAAC,WAAW,EAAE,CAAC,CAAC;QAC/G,OAAO,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,aAAa,EAAE,OAAO,EAAE,uBAAuB,CAAC,CAAC;IACzG,CAAC;CACF"}