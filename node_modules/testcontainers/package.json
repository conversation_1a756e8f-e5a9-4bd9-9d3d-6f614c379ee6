{"name": "testcontainers", "version": "10.28.0", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "keywords": ["testcontainers", "docker", "testing"], "description": "Testcontainers is a NodeJS library that supports tests, providing lightweight, throwaway instances of common databases, Selenium web browsers, or anything else that can run in a Docker container", "homepage": "https://github.com/testcontainers/testcontainers-node#readme", "repository": {"type": "git", "url": "git+https://github.com/testcontainers/testcontainers-node.git"}, "bugs": {"url": "https://github.com/testcontainers/testcontainers-node/issues"}, "main": "build/index.js", "files": ["build"], "publishConfig": {"access": "public"}, "scripts": {"prebuild": "node -p \"'export const LIB_VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > src/version.ts", "prepack": "shx cp ../../README.md . && shx cp ../../LICENSE .", "build": "tsc --project tsconfig.build.json"}, "dependencies": {"@balena/dockerignore": "^1.0.2", "@types/dockerode": "^3.3.35", "archiver": "^7.0.1", "async-lock": "^1.4.1", "byline": "^5.0.0", "debug": "^4.3.5", "docker-compose": "^0.24.8", "dockerode": "^4.0.5", "get-port": "^7.1.0", "proper-lockfile": "^4.1.2", "properties-reader": "^2.3.0", "ssh-remote-port-forward": "^1.0.4", "tar-fs": "^3.0.7", "tmp": "^0.2.3", "undici": "^5.29.0"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/async-lock": "^1.4.2", "@types/byline": "^4.2.36", "@types/debug": "^4.1.12", "@types/proper-lockfile": "^4.1.4", "@types/properties-reader": "^2.1.3", "@types/tar-fs": "^2.0.4", "@types/tmp": "^0.2.6"}}