/// <reference types="node" />
import { ContainerCreateOptions, HostConfig } from "dockerode";
import { Readable } from "stream";
import { ImageName } from "../container-runtime";
import { StartedNetwork } from "../network/network";
import { StartedTestContainer, TestContainer } from "../test-container";
import { ArchiveToCopy, BindMount, ContentToCopy, DirectoryToCopy, Environment, ExtraHost, FileToCopy, HealthCheck, InspectResult, Labels, ResourcesQuota, TmpFs, Ulimits } from "../types";
import { PortWithOptionalBinding } from "../utils/port";
import { ImagePullPolicy } from "../utils/pull-policy";
import { WaitStrategy } from "../wait-strategies/wait-strategy";
import { GenericContainerBuilder } from "./generic-container-builder";
export declare class GenericContainer implements TestContainer {
    static fromDockerfile(context: string, dockerfileName?: string): GenericContainerBuilder;
    protected createOpts: ContainerCreateOptions;
    protected hostConfig: HostConfig;
    protected imageName: ImageName;
    protected startupTimeout?: number;
    protected waitStrategy: WaitStrategy;
    protected environment: Record<string, string>;
    protected exposedPorts: PortWithOptionalBinding[];
    protected reuse: boolean;
    protected autoRemove: boolean;
    protected networkMode?: string;
    protected networkAliases: string[];
    protected pullPolicy: ImagePullPolicy;
    protected logConsumer?: (stream: Readable) => unknown;
    protected filesToCopy: FileToCopy[];
    protected directoriesToCopy: DirectoryToCopy[];
    protected contentsToCopy: ContentToCopy[];
    protected archivesToCopy: ArchiveToCopy[];
    protected healthCheck?: HealthCheck;
    constructor(image: string);
    private isHelperContainer;
    private isReaper;
    protected beforeContainerCreated?(): Promise<void>;
    protected containerCreated?(containerId: string): Promise<void>;
    protected containerStarting?(inspectResult: InspectResult, reused: boolean): Promise<void>;
    start(): Promise<StartedTestContainer>;
    private reuseOrStartContainer;
    private reuseContainer;
    private startContainer;
    private connectContainerToPortForwarder;
    private createArchiveToCopyToContainer;
    protected containerStarted?(container: StartedTestContainer, inspectResult: InspectResult, reused: boolean): Promise<void>;
    withCommand(command: string[]): this;
    withEntrypoint(entrypoint: string[]): this;
    withName(name: string): this;
    withLabels(labels: Labels): this;
    withEnvironment(environment: Environment): this;
    withPlatform(platform: string): this;
    withTmpFs(tmpFs: TmpFs): this;
    withUlimits(ulimits: Ulimits): this;
    withAddedCapabilities(...capabilities: string[]): this;
    withDroppedCapabilities(...capabilities: string[]): this;
    withNetwork(network: StartedNetwork): this;
    withNetworkMode(networkMode: string): this;
    withNetworkAliases(...networkAliases: string[]): this;
    withExtraHosts(extraHosts: ExtraHost[]): this;
    withExposedPorts(...ports: PortWithOptionalBinding[]): this;
    withBindMounts(bindMounts: BindMount[]): this;
    withHealthCheck(healthCheck: HealthCheck): this;
    withStartupTimeout(startupTimeoutMs: number): this;
    withWaitStrategy(waitStrategy: WaitStrategy): this;
    withDefaultLogDriver(): this;
    withPrivilegedMode(): this;
    withUser(user: string): this;
    withReuse(): this;
    withAutoRemove(autoRemove: boolean): this;
    withPullPolicy(pullPolicy: ImagePullPolicy): this;
    withIpcMode(ipcMode: string): this;
    withCopyFilesToContainer(filesToCopy: FileToCopy[]): this;
    withCopyDirectoriesToContainer(directoriesToCopy: DirectoryToCopy[]): this;
    withCopyContentToContainer(contentsToCopy: ContentToCopy[]): this;
    withCopyArchivesToContainer(archivesToCopy: ArchiveToCopy[]): this;
    withWorkingDir(workingDir: string): this;
    withResourcesQuota({ memory, cpu }: ResourcesQuota): this;
    withSharedMemorySize(bytes: number): this;
    withLogConsumer(logConsumer: (stream: Readable) => unknown): this;
    withHostname(hostname: string): this;
}
