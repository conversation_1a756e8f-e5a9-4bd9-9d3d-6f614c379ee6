# Testcontainers

[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://github.com/codespaces/new?hide_repo_select=true&ref=main&repo=116414510&machine=standardLinux32gb&devcontainer_path=.devcontainer%2Fdevcontainer.json&location=EastUs)

[![Test](https://github.com/testcontainers/testcontainers-node/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/testcontainers/testcontainers-node/actions/workflows/test.yml)
[![npm version](https://badge.fury.io/js/testcontainers.svg)](https://www.npmjs.com/package/testcontainers)
[![npm version](https://img.shields.io/npm/dm/testcontainers.svg)](https://www.npmjs.com/package/testcontainers)

![Testcontainers Banner](https://github.com/testcontainers/testcontainers-node/raw/main/docs/site/logo.png)

## [📖 Documentation](https://node.testcontainers.org/)

## License

See [LICENSE](https://github.com/testcontainers/testcontainers-node/blob/main/LICENSE).

## Copyright

Copyright (c) 2018 - 2023 Cristian Greco and other authors.

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for how to contribute to this repo.

See [contributors](https://github.com/testcontainers/testcontainers-node/graphs/contributors/) for all contributors.


----

Join our [Slack workspace](https://slack.testcontainers.org/) | [Testcontainers OSS](https://java.testcontainers.org/) | [Testcontainers Cloud](https://www.testcontainers.cloud/)
