hoistPattern:
  - '*'
hoistedDependencies:
  /@adobe/css-tools/4.4.3:
    '@adobe/css-tools': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@asamuzakjp/css-color/3.2.0:
    '@asamuzakjp/css-color': private
  /@auth/core/0.40.0(nodemailer@7.0.3):
    '@auth/core': private
  /@auth/express/0.11.0(express@5.1.0)(nodemailer@7.0.3):
    '@auth/express': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.27.7:
    '@babel/compat-data': private
  /@babel/core/7.27.7:
    '@babel/core': private
  /@babel/eslint-parser/7.27.5(@babel/core@7.27.7)(eslint@8.57.1):
    '@babel/eslint-parser': public
  /@babel/generator/7.27.5:
    '@babel/generator': private
  /@babel/helper-annotate-as-pure/7.27.3:
    '@babel/helper-annotate-as-pure': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-create-class-features-plugin/7.27.1(@babel/core@7.27.7):
    '@babel/helper-create-class-features-plugin': private
  /@babel/helper-create-regexp-features-plugin/7.27.1(@babel/core@7.27.7):
    '@babel/helper-create-regexp-features-plugin': private
  /@babel/helper-define-polyfill-provider/0.6.5(@babel/core@7.27.7):
    '@babel/helper-define-polyfill-provider': private
  /@babel/helper-environment-visitor/7.24.7:
    '@babel/helper-environment-visitor': private
  /@babel/helper-member-expression-to-functions/7.27.1:
    '@babel/helper-member-expression-to-functions': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.27.7):
    '@babel/helper-module-transforms': private
  /@babel/helper-optimise-call-expression/7.27.1:
    '@babel/helper-optimise-call-expression': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-remap-async-to-generator/7.27.1(@babel/core@7.27.7):
    '@babel/helper-remap-async-to-generator': private
  /@babel/helper-replace-supers/7.27.1(@babel/core@7.27.7):
    '@babel/helper-replace-supers': private
  /@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    '@babel/helper-skip-transparent-expression-wrappers': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helper-wrap-function/7.27.1:
    '@babel/helper-wrap-function': private
  /@babel/helpers/7.27.6:
    '@babel/helpers': private
  /@babel/parser/7.27.7:
    '@babel/parser': private
  /@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  /@babel/plugin-bugfix-safari-class-field-initializer-scope/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  /@babel/plugin-proposal-async-generator-functions/7.20.7(@babel/core@7.27.7):
    '@babel/plugin-proposal-async-generator-functions': private
  /@babel/plugin-proposal-class-properties/7.18.6(@babel/core@7.27.7):
    '@babel/plugin-proposal-class-properties': private
  /@babel/plugin-proposal-export-default-from/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-proposal-export-default-from': private
  /@babel/plugin-proposal-nullish-coalescing-operator/7.18.6(@babel/core@7.27.7):
    '@babel/plugin-proposal-nullish-coalescing-operator': private
  /@babel/plugin-proposal-numeric-separator/7.18.6(@babel/core@7.27.7):
    '@babel/plugin-proposal-numeric-separator': private
  /@babel/plugin-proposal-object-rest-spread/7.20.7(@babel/core@7.27.7):
    '@babel/plugin-proposal-object-rest-spread': private
  /@babel/plugin-proposal-optional-catch-binding/7.18.6(@babel/core@7.27.7):
    '@babel/plugin-proposal-optional-catch-binding': private
  /@babel/plugin-proposal-optional-chaining/7.21.0(@babel/core@7.27.7):
    '@babel/plugin-proposal-optional-chaining': private
  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.7):
    '@babel/plugin-proposal-private-property-in-object': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.27.7):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.27.7):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-dynamic-import/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-dynamic-import': private
  /@babel/plugin-syntax-export-default-from/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-export-default-from': private
  /@babel/plugin-syntax-flow/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-flow': private
  /@babel/plugin-syntax-import-assertions/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-import-assertions': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.27.7):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.27.7):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.27.7):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.27.7):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-syntax-typescript': private
  /@babel/plugin-syntax-unicode-sets-regex/7.18.6(@babel/core@7.27.7):
    '@babel/plugin-syntax-unicode-sets-regex': private
  /@babel/plugin-transform-arrow-functions/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-arrow-functions': private
  /@babel/plugin-transform-async-generator-functions/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-async-generator-functions': private
  /@babel/plugin-transform-async-to-generator/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-async-to-generator': private
  /@babel/plugin-transform-block-scoped-functions/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-block-scoped-functions': private
  /@babel/plugin-transform-block-scoping/7.27.5(@babel/core@7.27.7):
    '@babel/plugin-transform-block-scoping': private
  /@babel/plugin-transform-class-properties/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-class-properties': private
  /@babel/plugin-transform-class-static-block/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-class-static-block': private
  /@babel/plugin-transform-classes/7.27.7(@babel/core@7.27.7):
    '@babel/plugin-transform-classes': private
  /@babel/plugin-transform-computed-properties/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-computed-properties': private
  /@babel/plugin-transform-destructuring/7.27.7(@babel/core@7.27.7):
    '@babel/plugin-transform-destructuring': private
  /@babel/plugin-transform-dotall-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-dotall-regex': private
  /@babel/plugin-transform-duplicate-keys/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-duplicate-keys': private
  /@babel/plugin-transform-duplicate-named-capturing-groups-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  /@babel/plugin-transform-dynamic-import/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-dynamic-import': private
  /@babel/plugin-transform-exponentiation-operator/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-exponentiation-operator': private
  /@babel/plugin-transform-export-namespace-from/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-export-namespace-from': private
  /@babel/plugin-transform-flow-strip-types/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-flow-strip-types': private
  /@babel/plugin-transform-for-of/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-for-of': private
  /@babel/plugin-transform-function-name/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-function-name': private
  /@babel/plugin-transform-json-strings/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-json-strings': private
  /@babel/plugin-transform-literals/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-literals': private
  /@babel/plugin-transform-logical-assignment-operators/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-logical-assignment-operators': private
  /@babel/plugin-transform-member-expression-literals/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-member-expression-literals': private
  /@babel/plugin-transform-modules-amd/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-modules-amd': private
  /@babel/plugin-transform-modules-commonjs/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-modules-commonjs': private
  /@babel/plugin-transform-modules-systemjs/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-modules-systemjs': private
  /@babel/plugin-transform-modules-umd/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-modules-umd': private
  /@babel/plugin-transform-named-capturing-groups-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-named-capturing-groups-regex': private
  /@babel/plugin-transform-new-target/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-new-target': private
  /@babel/plugin-transform-nullish-coalescing-operator/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-nullish-coalescing-operator': private
  /@babel/plugin-transform-numeric-separator/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-numeric-separator': private
  /@babel/plugin-transform-object-rest-spread/7.27.7(@babel/core@7.27.7):
    '@babel/plugin-transform-object-rest-spread': private
  /@babel/plugin-transform-object-super/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-object-super': private
  /@babel/plugin-transform-optional-catch-binding/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-optional-catch-binding': private
  /@babel/plugin-transform-optional-chaining/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-optional-chaining': private
  /@babel/plugin-transform-parameters/7.27.7(@babel/core@7.27.7):
    '@babel/plugin-transform-parameters': private
  /@babel/plugin-transform-private-methods/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-private-methods': private
  /@babel/plugin-transform-private-property-in-object/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-private-property-in-object': private
  /@babel/plugin-transform-property-literals/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-property-literals': private
  /@babel/plugin-transform-react-display-name/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-react-display-name': private
  /@babel/plugin-transform-react-jsx-self/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-react-jsx-self': private
  /@babel/plugin-transform-react-jsx-source/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-react-jsx-source': private
  /@babel/plugin-transform-react-jsx/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-react-jsx': private
  /@babel/plugin-transform-regenerator/7.27.5(@babel/core@7.27.7):
    '@babel/plugin-transform-regenerator': private
  /@babel/plugin-transform-regexp-modifiers/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-regexp-modifiers': private
  /@babel/plugin-transform-reserved-words/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-reserved-words': private
  /@babel/plugin-transform-runtime/7.27.4(@babel/core@7.27.7):
    '@babel/plugin-transform-runtime': private
  /@babel/plugin-transform-shorthand-properties/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-shorthand-properties': private
  /@babel/plugin-transform-spread/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-spread': private
  /@babel/plugin-transform-sticky-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-sticky-regex': private
  /@babel/plugin-transform-template-literals/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-template-literals': private
  /@babel/plugin-transform-typeof-symbol/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-typeof-symbol': private
  /@babel/plugin-transform-typescript/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-typescript': private
  /@babel/plugin-transform-unicode-escapes/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-unicode-escapes': private
  /@babel/plugin-transform-unicode-property-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-unicode-property-regex': private
  /@babel/plugin-transform-unicode-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-unicode-regex': private
  /@babel/plugin-transform-unicode-sets-regex/7.27.1(@babel/core@7.27.7):
    '@babel/plugin-transform-unicode-sets-regex': private
  /@babel/preset-env/7.27.2(@babel/core@7.27.7):
    '@babel/preset-env': private
  /@babel/preset-flow/7.27.1(@babel/core@7.27.7):
    '@babel/preset-flow': private
  /@babel/preset-modules/0.1.6-no-external-plugins(@babel/core@7.27.7):
    '@babel/preset-modules': private
  /@babel/preset-typescript/7.27.1(@babel/core@7.27.7):
    '@babel/preset-typescript': private
  /@babel/register/7.27.1(@babel/core@7.27.7):
    '@babel/register': private
  /@babel/runtime/7.27.6:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.27.7:
    '@babel/traverse': private
  /@babel/types/7.27.7:
    '@babel/types': private
  /@balena/dockerignore/1.0.2:
    '@balena/dockerignore': private
  /@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  /@bundled-es-modules/cookie/2.0.1:
    '@bundled-es-modules/cookie': private
  /@bundled-es-modules/statuses/1.0.1:
    '@bundled-es-modules/statuses': private
  /@bundled-es-modules/tough-cookie/0.1.6:
    '@bundled-es-modules/tough-cookie': private
  /@callstack/react-theme-provider/3.0.9(react@18.2.0):
    '@callstack/react-theme-provider': private
  /@colors/colors/1.6.0:
    '@colors/colors': private
  /@csstools/color-helpers/5.0.2:
    '@csstools/color-helpers': private
  /@csstools/css-calc/2.1.4(@csstools/css-parser-algorithms@3.0.5)(@csstools/css-tokenizer@3.0.4):
    '@csstools/css-calc': private
  /@csstools/css-color-parser/3.0.10(@csstools/css-parser-algorithms@3.0.5)(@csstools/css-tokenizer@3.0.4):
    '@csstools/css-color-parser': private
  /@csstools/css-parser-algorithms/3.0.5(@csstools/css-tokenizer@3.0.4):
    '@csstools/css-parser-algorithms': private
  /@csstools/css-tokenizer/3.0.4:
    '@csstools/css-tokenizer': private
  /@dabh/diagnostics/2.0.3:
    '@dabh/diagnostics': private
  /@egjs/hammerjs/2.0.17:
    '@egjs/hammerjs': private
  /@esbuild/aix-ppc64/0.25.5:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.25.5:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.25.5:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.25.5:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.25.5:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.25.5:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.25.5:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.25.5:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.25.5:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.25.5:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.25.5:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.25.5:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.25.5:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.25.5:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.25.5:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.25.5:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.25.5:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-arm64/0.25.5:
    '@esbuild/netbsd-arm64': private
  /@esbuild/netbsd-x64/0.25.5:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-arm64/0.25.5:
    '@esbuild/openbsd-arm64': private
  /@esbuild/openbsd-x64/0.25.5:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.25.5:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.25.5:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.25.5:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.25.5:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/config-array/0.21.0:
    '@eslint/config-array': public
  /@eslint/config-helpers/0.3.0:
    '@eslint/config-helpers': public
  /@eslint/core/0.14.0:
    '@eslint/core': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@eslint/object-schema/2.1.6:
    '@eslint/object-schema': public
  /@eslint/plugin-kit/0.3.3:
    '@eslint/plugin-kit': public
  /@flatten-js/interval-tree/1.1.3:
    '@flatten-js/interval-tree': private
  /@floating-ui/core/1.7.2:
    '@floating-ui/core': private
  /@floating-ui/dom/1.7.2:
    '@floating-ui/dom': private
  /@floating-ui/react-dom/2.1.4(react-dom@19.1.0)(react@19.1.0):
    '@floating-ui/react-dom': private
  /@floating-ui/utils/0.2.10:
    '@floating-ui/utils': private
  /@grpc/grpc-js/1.13.4:
    '@grpc/grpc-js': private
  /@grpc/proto-loader/0.7.15:
    '@grpc/proto-loader': private
  /@hapi/hoek/9.3.0:
    '@hapi/hoek': private
  /@hapi/topo/5.1.0:
    '@hapi/topo': private
  /@hookform/resolvers/5.1.1(react-hook-form@7.59.0):
    '@hookform/resolvers': private
  /@humanfs/core/0.19.1:
    '@humanfs/core': private
  /@humanfs/node/0.16.6:
    '@humanfs/node': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@humanwhocodes/retry/0.4.3:
    '@humanwhocodes/retry': private
  /@inquirer/confirm/5.1.12:
    '@inquirer/confirm': private
  /@inquirer/core/10.1.13:
    '@inquirer/core': private
  /@inquirer/figures/1.0.12:
    '@inquirer/figures': private
  /@inquirer/type/3.0.7:
    '@inquirer/type': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0:
    '@jest/core': private
  /@jest/create-cache-key-function/29.7.0:
    '@jest/create-cache-key-function': private
  /@jest/diff-sequences/30.0.1:
    '@jest/diff-sequences': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/get-type/30.0.1:
    '@jest/get-type': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/pattern/30.0.1:
    '@jest/pattern': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/snapshot-utils/30.0.1:
    '@jest/snapshot-utils': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/source-map/0.3.6:
    '@jridgewell/source-map': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@js-sdsl/ordered-map/4.4.2:
    '@js-sdsl/ordered-map': private
  /@mswjs/interceptors/0.39.2:
    '@mswjs/interceptors': private
  /@nicolo-ribaudo/eslint-scope-5-internals/5.1.1-v1:
    '@nicolo-ribaudo/eslint-scope-5-internals': public
  /@noble/hashes/1.8.0:
    '@noble/hashes': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@open-draft/deferred-promise/2.2.0:
    '@open-draft/deferred-promise': private
  /@open-draft/logger/0.3.0:
    '@open-draft/logger': private
  /@open-draft/until/2.1.0:
    '@open-draft/until': private
  /@otplib/core/12.0.1:
    '@otplib/core': private
  /@otplib/plugin-crypto/12.0.1:
    '@otplib/plugin-crypto': private
  /@otplib/plugin-thirty-two/12.0.1:
    '@otplib/plugin-thirty-two': private
  /@otplib/preset-default/12.0.1:
    '@otplib/preset-default': private
  /@otplib/preset-v11/12.0.1:
    '@otplib/preset-v11': private
  /@panva/hkdf/1.2.1:
    '@panva/hkdf': private
  /@paralleldrive/cuid2/2.2.2:
    '@paralleldrive/cuid2': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pkgr/core/0.2.7:
    '@pkgr/core': private
  /@polka/url/1.0.0-next.29:
    '@polka/url': private
  /@prisma/client/6.10.1(prisma@6.10.1)(typescript@5.8.3):
    '@prisma/client': private
  /@prisma/config/6.10.1:
    '@prisma/config': private
  /@prisma/debug/6.10.1:
    '@prisma/debug': private
  /@prisma/engines-version/6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c:
    '@prisma/engines-version': private
  /@prisma/engines/6.10.1:
    '@prisma/engines': private
  /@prisma/fetch-engine/6.10.1:
    '@prisma/fetch-engine': private
  /@prisma/get-platform/6.10.1:
    '@prisma/get-platform': private
  /@protobufjs/aspromise/1.1.2:
    '@protobufjs/aspromise': private
  /@protobufjs/base64/1.1.2:
    '@protobufjs/base64': private
  /@protobufjs/codegen/2.0.4:
    '@protobufjs/codegen': private
  /@protobufjs/eventemitter/1.1.0:
    '@protobufjs/eventemitter': private
  /@protobufjs/fetch/1.1.0:
    '@protobufjs/fetch': private
  /@protobufjs/float/1.0.2:
    '@protobufjs/float': private
  /@protobufjs/inquire/1.1.0:
    '@protobufjs/inquire': private
  /@protobufjs/path/1.1.2:
    '@protobufjs/path': private
  /@protobufjs/pool/1.1.0:
    '@protobufjs/pool': private
  /@protobufjs/utf8/1.1.0:
    '@protobufjs/utf8': private
  /@radix-ui/number/1.1.1:
    '@radix-ui/number': private
  /@radix-ui/primitive/1.1.2:
    '@radix-ui/primitive': private
  /@radix-ui/react-arrow/1.1.7(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-arrow': private
  /@radix-ui/react-avatar/1.1.10(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-avatar': private
  /@radix-ui/react-collection/1.1.7(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-collection': private
  /@radix-ui/react-compose-refs/1.1.2(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-compose-refs': private
  /@radix-ui/react-context/1.1.2(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-context': private
  /@radix-ui/react-dialog/1.1.14(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-dialog': private
  /@radix-ui/react-direction/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-direction': private
  /@radix-ui/react-dismissable-layer/1.1.10(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-dismissable-layer': private
  /@radix-ui/react-dropdown-menu/2.1.15(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-dropdown-menu': private
  /@radix-ui/react-focus-guards/1.1.2(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-focus-guards': private
  /@radix-ui/react-focus-scope/1.1.7(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-focus-scope': private
  /@radix-ui/react-id/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-id': private
  /@radix-ui/react-menu/2.1.15(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-menu': private
  /@radix-ui/react-popper/1.2.7(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-popper': private
  /@radix-ui/react-portal/1.1.9(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-portal': private
  /@radix-ui/react-presence/1.1.4(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-presence': private
  /@radix-ui/react-primitive/2.1.3(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-primitive': private
  /@radix-ui/react-roving-focus/1.1.10(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-roving-focus': private
  /@radix-ui/react-select/2.2.5(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-select': private
  /@radix-ui/react-slot/1.2.3(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-slot': private
  /@radix-ui/react-tabs/1.1.12(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-tabs': private
  /@radix-ui/react-toast/1.2.14(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-toast': private
  /@radix-ui/react-tooltip/1.2.7(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-tooltip': private
  /@radix-ui/react-use-callback-ref/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-callback-ref': private
  /@radix-ui/react-use-controllable-state/1.2.2(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-controllable-state': private
  /@radix-ui/react-use-effect-event/0.0.2(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-effect-event': private
  /@radix-ui/react-use-escape-keydown/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-escape-keydown': private
  /@radix-ui/react-use-is-hydrated/0.1.0(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-is-hydrated': private
  /@radix-ui/react-use-layout-effect/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-layout-effect': private
  /@radix-ui/react-use-previous/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-previous': private
  /@radix-ui/react-use-rect/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-rect': private
  /@radix-ui/react-use-size/1.1.1(@types/react@19.1.8)(react@19.1.0):
    '@radix-ui/react-use-size': private
  /@radix-ui/react-visually-hidden/1.2.3(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@radix-ui/react-visually-hidden': private
  /@radix-ui/rect/1.1.1:
    '@radix-ui/rect': private
  /@react-native-async-storage/async-storage/1.24.0(react-native@0.72.3):
    '@react-native-async-storage/async-storage': private
  /@react-native-community/cli-clean/11.3.5:
    '@react-native-community/cli-clean': private
  /@react-native-community/cli-config/11.3.5:
    '@react-native-community/cli-config': private
  /@react-native-community/cli-debugger-ui/11.3.5:
    '@react-native-community/cli-debugger-ui': private
  /@react-native-community/cli-doctor/11.3.5:
    '@react-native-community/cli-doctor': private
  /@react-native-community/cli-hermes/11.3.5:
    '@react-native-community/cli-hermes': private
  /@react-native-community/cli-platform-android/11.3.5:
    '@react-native-community/cli-platform-android': private
  /@react-native-community/cli-platform-ios/11.3.5:
    '@react-native-community/cli-platform-ios': private
  /@react-native-community/cli-plugin-metro/11.3.5(@babel/core@7.27.7):
    '@react-native-community/cli-plugin-metro': private
  /@react-native-community/cli-server-api/11.3.5:
    '@react-native-community/cli-server-api': private
  /@react-native-community/cli-tools/11.3.5:
    '@react-native-community/cli-tools': private
  /@react-native-community/cli-types/11.3.5:
    '@react-native-community/cli-types': private
  /@react-native-community/cli/11.3.5(@babel/core@7.27.7):
    '@react-native-community/cli': private
  /@react-native-community/netinfo/9.5.0(react-native@0.72.3):
    '@react-native-community/netinfo': private
  /@react-native-firebase/analytics/18.9.0(@react-native-firebase/app@18.9.0):
    '@react-native-firebase/analytics': private
  /@react-native-firebase/app/18.9.0(react-native@0.72.3)(react@18.2.0):
    '@react-native-firebase/app': private
  /@react-native-firebase/crashlytics/18.9.0(@react-native-firebase/app@18.9.0):
    '@react-native-firebase/crashlytics': private
  /@react-native-firebase/messaging/18.9.0(@react-native-firebase/app@18.9.0):
    '@react-native-firebase/messaging': private
  /@react-native/assets-registry/0.72.0:
    '@react-native/assets-registry': private
  /@react-native/codegen/0.72.8(@babel/preset-env@7.27.2):
    '@react-native/codegen': private
  /@react-native/eslint-config/0.72.2(eslint@8.57.1)(jest@29.7.0)(prettier@3.6.2)(typescript@5.8.3):
    '@react-native/eslint-config': public
  /@react-native/eslint-plugin/0.72.0:
    '@react-native/eslint-plugin': public
  /@react-native/gradle-plugin/0.72.11:
    '@react-native/gradle-plugin': private
  /@react-native/js-polyfills/0.72.1:
    '@react-native/js-polyfills': private
  /@react-native/metro-config/0.72.12(@babel/core@7.27.7):
    '@react-native/metro-config': private
  /@react-native/normalize-colors/0.72.0:
    '@react-native/normalize-colors': private
  /@react-native/virtualized-lists/0.72.8(react-native@0.72.3):
    '@react-native/virtualized-lists': private
  /@react-navigation/bottom-tabs/6.6.1(@react-navigation/native@6.1.18)(react-native-safe-area-context@4.14.1)(react-native-screens@3.37.0)(react-native@0.72.3)(react@18.2.0):
    '@react-navigation/bottom-tabs': private
  /@react-navigation/core/6.4.17(react@18.2.0):
    '@react-navigation/core': private
  /@react-navigation/drawer/6.7.2(@react-navigation/native@6.1.18)(react-native-gesture-handler@2.26.0)(react-native-reanimated@3.18.0)(react-native-safe-area-context@4.14.1)(react-native-screens@3.37.0)(react-native@0.72.3)(react@18.2.0):
    '@react-navigation/drawer': private
  /@react-navigation/elements/1.3.31(@react-navigation/native@6.1.18)(react-native-safe-area-context@4.14.1)(react-native@0.72.3)(react@18.2.0):
    '@react-navigation/elements': private
  /@react-navigation/native/6.1.18(react-native@0.72.3)(react@18.2.0):
    '@react-navigation/native': private
  /@react-navigation/routers/6.1.9:
    '@react-navigation/routers': private
  /@react-navigation/stack/6.4.1(@react-navigation/native@6.1.18)(react-native-gesture-handler@2.26.0)(react-native-safe-area-context@4.14.1)(react-native-screens@3.37.0)(react-native@0.72.3)(react@18.2.0):
    '@react-navigation/stack': private
  /@redis/bloom/5.5.6(@redis/client@5.5.6):
    '@redis/bloom': private
  /@redis/client/5.5.6:
    '@redis/client': private
  /@redis/json/5.5.6(@redis/client@5.5.6):
    '@redis/json': private
  /@redis/search/5.5.6(@redis/client@5.5.6):
    '@redis/search': private
  /@redis/time-series/5.5.6(@redis/client@5.5.6):
    '@redis/time-series': private
  /@reduxjs/toolkit/1.9.7(react-redux@8.1.3)(react@18.2.0):
    '@reduxjs/toolkit': private
  /@rolldown/pluginutils/1.0.0-beta.19:
    '@rolldown/pluginutils': private
  /@rollup/rollup-android-arm-eabi/4.44.1:
    '@rollup/rollup-android-arm-eabi': private
  /@rollup/rollup-android-arm64/4.44.1:
    '@rollup/rollup-android-arm64': private
  /@rollup/rollup-darwin-arm64/4.44.1:
    '@rollup/rollup-darwin-arm64': private
  /@rollup/rollup-darwin-x64/4.44.1:
    '@rollup/rollup-darwin-x64': private
  /@rollup/rollup-freebsd-arm64/4.44.1:
    '@rollup/rollup-freebsd-arm64': private
  /@rollup/rollup-freebsd-x64/4.44.1:
    '@rollup/rollup-freebsd-x64': private
  /@rollup/rollup-linux-arm-gnueabihf/4.44.1:
    '@rollup/rollup-linux-arm-gnueabihf': private
  /@rollup/rollup-linux-arm-musleabihf/4.44.1:
    '@rollup/rollup-linux-arm-musleabihf': private
  /@rollup/rollup-linux-arm64-gnu/4.44.1:
    '@rollup/rollup-linux-arm64-gnu': private
  /@rollup/rollup-linux-arm64-musl/4.44.1:
    '@rollup/rollup-linux-arm64-musl': private
  /@rollup/rollup-linux-loongarch64-gnu/4.44.1:
    '@rollup/rollup-linux-loongarch64-gnu': private
  /@rollup/rollup-linux-powerpc64le-gnu/4.44.1:
    '@rollup/rollup-linux-powerpc64le-gnu': private
  /@rollup/rollup-linux-riscv64-gnu/4.44.1:
    '@rollup/rollup-linux-riscv64-gnu': private
  /@rollup/rollup-linux-riscv64-musl/4.44.1:
    '@rollup/rollup-linux-riscv64-musl': private
  /@rollup/rollup-linux-s390x-gnu/4.44.1:
    '@rollup/rollup-linux-s390x-gnu': private
  /@rollup/rollup-linux-x64-gnu/4.44.1:
    '@rollup/rollup-linux-x64-gnu': private
  /@rollup/rollup-linux-x64-musl/4.44.1:
    '@rollup/rollup-linux-x64-musl': private
  /@rollup/rollup-win32-arm64-msvc/4.44.1:
    '@rollup/rollup-win32-arm64-msvc': private
  /@rollup/rollup-win32-ia32-msvc/4.44.1:
    '@rollup/rollup-win32-ia32-msvc': private
  /@rollup/rollup-win32-x64-msvc/4.44.1:
    '@rollup/rollup-win32-x64-msvc': private
  /@sideway/address/4.1.5:
    '@sideway/address': private
  /@sideway/formula/3.0.1:
    '@sideway/formula': private
  /@sideway/pinpoint/2.0.0:
    '@sideway/pinpoint': private
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/13.0.5:
    '@sinonjs/fake-timers': private
  /@standard-schema/utils/0.3.0:
    '@standard-schema/utils': private
  /@tanstack/form-core/1.12.4:
    '@tanstack/form-core': private
  /@tanstack/query-core/5.81.5:
    '@tanstack/query-core': private
  /@tanstack/react-form/1.12.4(react-dom@19.1.0)(react@19.1.0):
    '@tanstack/react-form': private
  /@tanstack/react-query/5.81.5(react@19.1.0):
    '@tanstack/react-query': private
  /@tanstack/react-store/0.7.1(react-dom@19.1.0)(react@19.1.0):
    '@tanstack/react-store': private
  /@tanstack/store/0.7.1:
    '@tanstack/store': private
  /@testcontainers/postgresql/11.0.3:
    '@testcontainers/postgresql': private
  /@testing-library/dom/10.4.0:
    '@testing-library/dom': private
  /@testing-library/jest-dom/6.6.3:
    '@testing-library/jest-dom': private
  /@testing-library/jest-native/5.4.3(react-native@0.72.3)(react-test-renderer@18.2.0)(react@18.2.0):
    '@testing-library/jest-native': private
  /@testing-library/react-native/12.9.0(jest@29.7.0)(react-native@0.72.3)(react-test-renderer@18.2.0)(react@18.2.0):
    '@testing-library/react-native': private
  /@testing-library/react/16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@19.1.6)(@types/react@19.1.8)(react-dom@19.1.0)(react@19.1.0):
    '@testing-library/react': private
  /@testing-library/user-event/14.6.1(@testing-library/dom@10.4.0):
    '@testing-library/user-event': private
  /@types/aria-query/5.0.4:
    '@types/aria-query': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  /@types/bcryptjs/3.0.0:
    '@types/bcryptjs': private
  /@types/body-parser/1.19.6:
    '@types/body-parser': private
  /@types/chai/5.2.2:
    '@types/chai': private
  /@types/compression/1.8.1:
    '@types/compression': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/cookie/0.6.0:
    '@types/cookie': private
  /@types/cookiejar/2.1.5:
    '@types/cookiejar': private
  /@types/cors/2.8.19:
    '@types/cors': private
  /@types/deep-eql/4.0.2:
    '@types/deep-eql': private
  /@types/docker-modem/3.0.6:
    '@types/docker-modem': private
  /@types/dockerode/3.3.41:
    '@types/dockerode': private
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/express-serve-static-core/5.0.6:
    '@types/express-serve-static-core': private
  /@types/express-session/1.18.2:
    '@types/express-session': private
  /@types/express/5.0.3:
    '@types/express': private
  /@types/faker/6.6.11:
    '@types/faker': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/hammerjs/2.0.46:
    '@types/hammerjs': private
  /@types/hoist-non-react-statics/3.3.6:
    '@types/hoist-non-react-statics': private
  /@types/http-errors/2.0.5:
    '@types/http-errors': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/jest/30.0.0:
    '@types/jest': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/jsonwebtoken/9.0.10:
    '@types/jsonwebtoken': private
  /@types/methods/1.1.4:
    '@types/methods': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/morgan/1.9.10:
    '@types/morgan': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/multer/1.4.13:
    '@types/multer': private
  /@types/node/24.0.7:
    '@types/node': private
  /@types/nodemailer/6.4.17:
    '@types/nodemailer': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /@types/qrcode/1.5.5:
    '@types/qrcode': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/react-dom/19.1.6(@types/react@19.1.8):
    '@types/react-dom': private
  /@types/react-native/0.72.8(react-native@0.72.3):
    '@types/react-native': private
  /@types/react-test-renderer/18.3.1:
    '@types/react-test-renderer': private
  /@types/react/19.1.8:
    '@types/react': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.5:
    '@types/send': private
  /@types/serve-static/1.15.8:
    '@types/serve-static': private
  /@types/speakeasy/2.0.10:
    '@types/speakeasy': private
  /@types/ssh2-streams/0.1.12:
    '@types/ssh2-streams': private
  /@types/ssh2/1.15.5:
    '@types/ssh2': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/statuses/2.0.6:
    '@types/statuses': private
  /@types/superagent/8.1.9:
    '@types/superagent': private
  /@types/supertest/6.0.3:
    '@types/supertest': private
  /@types/tough-cookie/4.0.5:
    '@types/tough-cookie': private
  /@types/triple-beam/1.3.5:
    '@types/triple-beam': private
  /@types/use-sync-external-store/0.0.3:
    '@types/use-sync-external-store': private
  /@types/uuid/10.0.0:
    '@types/uuid': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@typescript-eslint/eslint-plugin/8.35.0(@typescript-eslint/parser@8.35.0)(eslint@9.30.0)(typescript@5.8.3):
    '@typescript-eslint/eslint-plugin': public
  /@typescript-eslint/parser/8.35.0(eslint@9.30.0)(typescript@5.8.3):
    '@typescript-eslint/parser': public
  /@typescript-eslint/project-service/8.35.0(typescript@5.8.3):
    '@typescript-eslint/project-service': public
  /@typescript-eslint/scope-manager/6.21.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/tsconfig-utils/8.35.0(typescript@5.8.3):
    '@typescript-eslint/tsconfig-utils': public
  /@typescript-eslint/type-utils/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/6.21.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/6.21.0(typescript@5.8.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/6.21.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@unrs/resolver-binding-android-arm-eabi/1.9.2:
    '@unrs/resolver-binding-android-arm-eabi': private
  /@unrs/resolver-binding-android-arm64/1.9.2:
    '@unrs/resolver-binding-android-arm64': private
  /@unrs/resolver-binding-darwin-arm64/1.9.2:
    '@unrs/resolver-binding-darwin-arm64': private
  /@unrs/resolver-binding-darwin-x64/1.9.2:
    '@unrs/resolver-binding-darwin-x64': private
  /@unrs/resolver-binding-freebsd-x64/1.9.2:
    '@unrs/resolver-binding-freebsd-x64': private
  /@unrs/resolver-binding-linux-arm-gnueabihf/1.9.2:
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  /@unrs/resolver-binding-linux-arm-musleabihf/1.9.2:
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  /@unrs/resolver-binding-linux-arm64-gnu/1.9.2:
    '@unrs/resolver-binding-linux-arm64-gnu': private
  /@unrs/resolver-binding-linux-arm64-musl/1.9.2:
    '@unrs/resolver-binding-linux-arm64-musl': private
  /@unrs/resolver-binding-linux-ppc64-gnu/1.9.2:
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-gnu/1.9.2:
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-musl/1.9.2:
    '@unrs/resolver-binding-linux-riscv64-musl': private
  /@unrs/resolver-binding-linux-s390x-gnu/1.9.2:
    '@unrs/resolver-binding-linux-s390x-gnu': private
  /@unrs/resolver-binding-linux-x64-gnu/1.9.2:
    '@unrs/resolver-binding-linux-x64-gnu': private
  /@unrs/resolver-binding-linux-x64-musl/1.9.2:
    '@unrs/resolver-binding-linux-x64-musl': private
  /@unrs/resolver-binding-wasm32-wasi/1.9.2:
    '@unrs/resolver-binding-wasm32-wasi': private
  /@unrs/resolver-binding-win32-arm64-msvc/1.9.2:
    '@unrs/resolver-binding-win32-arm64-msvc': private
  /@unrs/resolver-binding-win32-ia32-msvc/1.9.2:
    '@unrs/resolver-binding-win32-ia32-msvc': private
  /@unrs/resolver-binding-win32-x64-msvc/1.9.2:
    '@unrs/resolver-binding-win32-x64-msvc': private
  /@vitejs/plugin-react/4.6.0(vite@7.0.0):
    '@vitejs/plugin-react': private
  /@vitest/expect/3.2.4:
    '@vitest/expect': private
  /@vitest/mocker/3.2.4(msw@2.10.2)(vite@7.0.0):
    '@vitest/mocker': private
  /@vitest/pretty-format/3.2.4:
    '@vitest/pretty-format': private
  /@vitest/runner/3.2.4:
    '@vitest/runner': private
  /@vitest/snapshot/3.2.4:
    '@vitest/snapshot': private
  /@vitest/spy/3.2.4:
    '@vitest/spy': private
  /@vitest/ui/3.2.4(vitest@3.2.4):
    '@vitest/ui': private
  /@vitest/utils/3.2.4:
    '@vitest/utils': private
  /@wix-pilot/core/3.3.4(expect@29.7.0):
    '@wix-pilot/core': private
  /@wix-pilot/detox/1.0.11(@wix-pilot/core@3.3.4)(detox@20.39.0)(expect@29.7.0):
    '@wix-pilot/detox': private
  /abort-controller/3.0.0:
    abort-controller: private
  /accepts/2.0.0:
    accepts: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn/8.15.0:
    acorn: private
  /agent-base/7.1.3:
    agent-base: private
  /ajv/8.17.1:
    ajv: private
  /anser/1.4.10:
    anser: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-fragments/0.2.1:
    ansi-fragments: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /anymatch/3.1.3:
    anymatch: private
  /appdirsjs/1.2.7:
    appdirsjs: private
  /append-field/1.0.0:
    append-field: private
  /archiver-utils/5.0.2:
    archiver-utils: private
  /archiver/7.0.1:
    archiver: private
  /argparse/2.0.1:
    argparse: private
  /aria-hidden/1.2.6:
    aria-hidden: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-includes/3.1.9:
    array-includes: private
  /array-union/2.1.0:
    array-union: private
  /array.prototype.findlast/1.2.5:
    array.prototype.findlast: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /array.prototype.tosorted/1.1.4:
    array.prototype.tosorted: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /asap/2.0.6:
    asap: private
  /asn1/0.2.6:
    asn1: private
  /assertion-error/2.0.1:
    assertion-error: private
  /ast-types/0.15.2:
    ast-types: private
  /astral-regex/1.0.0:
    astral-regex: private
  /async-function/1.0.0:
    async-function: private
  /async-limiter/1.0.1:
    async-limiter: private
  /async-lock/1.4.1:
    async-lock: private
  /async/3.2.6:
    async: private
  /asynckit/0.4.0:
    asynckit: private
  /autoprefixer/10.4.21(postcss@8.5.6):
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axios/1.10.0:
    axios: private
  /b4a/1.6.7:
    b4a: private
  /babel-core/7.0.0-bridge.0(@babel/core@7.27.7):
    babel-core: private
  /babel-jest/29.7.0(@babel/core@7.27.7):
    babel-jest: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-plugin-polyfill-corejs2/0.4.14(@babel/core@7.27.7):
    babel-plugin-polyfill-corejs2: private
  /babel-plugin-polyfill-corejs3/0.11.1(@babel/core@7.27.7):
    babel-plugin-polyfill-corejs3: private
  /babel-plugin-polyfill-regenerator/0.6.5(@babel/core@7.27.7):
    babel-plugin-polyfill-regenerator: private
  /babel-plugin-syntax-trailing-function-commas/7.0.0-beta.0:
    babel-plugin-syntax-trailing-function-commas: private
  /babel-plugin-transform-flow-enums/0.0.2(@babel/core@7.27.7):
    babel-plugin-transform-flow-enums: private
  /babel-preset-current-node-syntax/1.1.0(@babel/core@7.27.7):
    babel-preset-current-node-syntax: private
  /babel-preset-fbjs/3.4.0(@babel/core@7.27.7):
    babel-preset-fbjs: private
  /babel-preset-jest/29.6.3(@babel/core@7.27.7):
    babel-preset-jest: private
  /balanced-match/1.0.2:
    balanced-match: private
  /bare-events/2.5.4:
    bare-events: private
  /bare-fs/4.1.5:
    bare-fs: private
  /bare-os/3.6.1:
    bare-os: private
  /bare-path/3.0.0:
    bare-path: private
  /bare-stream/2.6.5(bare-events@2.5.4):
    bare-stream: private
  /base32.js/0.0.1:
    base32.js: private
  /base64-js/1.5.1:
    base64-js: private
  /basic-auth/2.0.1:
    basic-auth: private
  /bcrypt-pbkdf/1.0.2:
    bcrypt-pbkdf: private
  /bcryptjs/3.0.2:
    bcryptjs: private
  /bl/4.1.0:
    bl: private
  /bluebird/3.7.2:
    bluebird: private
  /body-parser/2.2.0:
    body-parser: private
  /boolbase/1.0.0:
    boolbase: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browser-process-hrtime/1.0.0:
    browser-process-hrtime: private
  /browserslist/4.25.1:
    browserslist: private
  /bs-logger/0.2.6:
    bs-logger: private
  /bser/2.1.1:
    bser: private
  /buffer-crc32/1.0.0:
    buffer-crc32: private
  /buffer-equal-constant-time/1.0.1:
    buffer-equal-constant-time: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer/6.0.3:
    buffer: private
  /buildcheck/0.0.6:
    buildcheck: private
  /bunyamin/1.6.3(bunyan@2.0.5):
    bunyamin: private
  /bunyan-debug-stream/3.1.1(bunyan@1.8.15):
    bunyan-debug-stream: private
  /bunyan/1.8.15:
    bunyan: private
  /busboy/1.6.0:
    busboy: private
  /byline/5.0.0:
    byline: private
  /bytes/3.1.2:
    bytes: private
  /cac/6.7.14:
    cac: private
  /caf/15.0.1:
    caf: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /caller-callsite/2.0.0:
    caller-callsite: private
  /caller-path/2.0.0:
    caller-path: private
  /callsites/3.1.0:
    callsites: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-lite/1.0.30001726:
    caniuse-lite: private
  /chai/5.2.0:
    chai: private
  /chalk/3.0.0:
    chalk: private
  /char-regex/1.0.2:
    char-regex: private
  /check-error/2.1.1:
    check-error: private
  /chownr/1.1.4:
    chownr: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /class-variance-authority/0.7.1:
    class-variance-authority: private
  /cli-cursor/5.0.0:
    cli-cursor: private
  /cli-spinners/2.9.2:
    cli-spinners: private
  /cli-truncate/4.0.0:
    cli-truncate: private
  /cli-width/4.1.0:
    cli-width: private
  /cliui/6.0.0:
    cliui: private
  /clone-deep/2.0.2:
    clone-deep: private
  /clone/1.0.4:
    clone: private
  /clsx/2.1.1:
    clsx: private
  /cluster-key-slot/1.1.2:
    cluster-key-slot: private
  /co/4.6.0:
    co: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/1.9.3:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /color-string/1.9.1:
    color-string: private
  /color/4.2.3:
    color: private
  /colorette/2.0.20:
    colorette: private
  /colorspace/1.1.4:
    colorspace: private
  /combined-stream/1.0.8:
    combined-stream: private
  /command-exists/1.2.9:
    command-exists: private
  /commander/13.1.0:
    commander: private
  /commondir/1.0.1:
    commondir: private
  /component-emitter/1.3.1:
    component-emitter: private
  /compress-commons/6.0.2:
    compress-commons: private
  /compressible/2.0.18:
    compressible: private
  /compression/1.8.0:
    compression: private
  /concat-map/0.0.1:
    concat-map: private
  /concat-stream/2.0.0:
    concat-stream: private
  /connect-redis/9.0.0(express-session@1.18.1)(redis@5.5.6):
    connect-redis: private
  /connect/3.7.0:
    connect: private
  /content-disposition/1.0.0:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.7:
    cookie-signature: private
  /cookie/0.7.2:
    cookie: private
  /cookiejar/2.1.4:
    cookiejar: private
  /core-js-compat/3.43.0:
    core-js-compat: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cors/2.8.5:
    cors: private
  /cosmiconfig/5.2.1:
    cosmiconfig: private
  /cpu-features/0.0.10:
    cpu-features: private
  /crc-32/1.2.2:
    crc-32: private
  /crc32-stream/6.0.0:
    crc32-stream: private
  /create-jest/29.7.0:
    create-jest: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /css-select/5.2.2:
    css-select: private
  /css-tree/1.1.3:
    css-tree: private
  /css-what/6.2.2:
    css-what: private
  /css.escape/1.5.1:
    css.escape: private
  /cssstyle/4.6.0:
    cssstyle: private
  /csstype/3.1.3:
    csstype: private
  /data-urls/5.0.0:
    data-urls: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /date-fns/2.30.0:
    date-fns: private
  /dayjs/1.11.13:
    dayjs: private
  /debug/4.4.1:
    debug: private
  /decamelize/4.0.0:
    decamelize: private
  /decimal.js/10.5.0:
    decimal.js: private
  /decode-formdata/0.9.0:
    decode-formdata: private
  /decode-uri-component/0.2.2:
    decode-uri-component: private
  /dedent/1.6.0:
    dedent: private
  /deep-eql/5.0.2:
    deep-eql: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/2.2.1:
    deepmerge: private
  /defaults/1.0.4:
    defaults: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /denodeify/1.2.1:
    denodeify: private
  /depd/2.0.0:
    depd: private
  /deprecated-react-native-prop-types/4.1.0:
    deprecated-react-native-prop-types: private
  /dequal/2.0.3:
    dequal: private
  /destroy/1.2.0:
    destroy: private
  /detect-newline/3.1.0:
    detect-newline: private
  /detect-node-es/1.1.0:
    detect-node-es: private
  /detox/20.39.0(expect@29.7.0)(jest@29.7.0):
    detox: private
  /devalue/5.1.1:
    devalue: private
  /dezalgo/1.0.4:
    dezalgo: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /dijkstrajs/1.0.3:
    dijkstrajs: private
  /dir-glob/3.0.1:
    dir-glob: private
  /docker-compose/1.2.0:
    docker-compose: private
  /docker-modem/5.0.6:
    docker-modem: private
  /dockerode/4.0.7:
    dockerode: private
  /doctrine/2.1.0:
    doctrine: private
  /dom-accessibility-api/0.6.3:
    dom-accessibility-api: private
  /dom-serializer/2.0.0:
    dom-serializer: private
  /domelementtype/2.3.0:
    domelementtype: private
  /domhandler/5.0.3:
    domhandler: private
  /domutils/3.2.2:
    domutils: private
  /dotenv/17.0.0:
    dotenv: private
  /dtrace-provider/0.8.8:
    dtrace-provider: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /duplexer2/0.1.4:
    duplexer2: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /easy-stack/1.0.1:
    easy-stack: private
  /ecdsa-sig-formatter/1.0.11:
    ecdsa-sig-formatter: private
  /ee-first/1.1.1:
    ee-first: private
  /ejs/3.1.10:
    ejs: private
  /electron-to-chromium/1.5.177:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /enabled/2.0.0:
    enabled: private
  /encodeurl/2.0.0:
    encodeurl: private
  /end-of-stream/1.4.5:
    end-of-stream: private
  /entities/6.0.1:
    entities: private
  /envinfo/7.14.0:
    envinfo: private
  /environment/1.1.0:
    environment: private
  /error-ex/1.3.2:
    error-ex: private
  /error-stack-parser/2.1.4:
    error-stack-parser: private
  /errorhandler/1.5.1:
    errorhandler: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-iterator-helpers/1.2.1:
    es-iterator-helpers: private
  /es-module-lexer/1.7.0:
    es-module-lexer: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /esbuild/0.25.5:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-config-prettier/8.10.0(eslint@8.57.1):
    eslint-config-prettier: public
  /eslint-plugin-eslint-comments/3.2.0(eslint@8.57.1):
    eslint-plugin-eslint-comments: public
  /eslint-plugin-ft-flow/2.0.3(@babel/eslint-parser@7.27.5)(eslint@8.57.1):
    eslint-plugin-ft-flow: public
  /eslint-plugin-jest/26.9.0(@typescript-eslint/eslint-plugin@5.62.0)(eslint@8.57.1)(jest@29.7.0)(typescript@5.8.3):
    eslint-plugin-jest: public
  /eslint-plugin-prettier/4.2.1(eslint-config-prettier@8.10.0)(eslint@8.57.1)(prettier@3.6.2):
    eslint-plugin-prettier: public
  /eslint-plugin-react-hooks/5.2.0(eslint@9.30.0):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react-native-globals/0.1.2:
    eslint-plugin-react-native-globals: public
  /eslint-plugin-react-native/4.1.0(eslint@8.57.1):
    eslint-plugin-react-native: public
  /eslint-plugin-react-refresh/0.4.20(eslint@9.30.0):
    eslint-plugin-react-refresh: public
  /eslint-plugin-react/7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /eslint/9.30.0:
    eslint: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /estree-walker/3.0.3:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /event-pubsub/4.3.0:
    event-pubsub: private
  /event-target-shim/5.0.1:
    event-target-shim: private
  /eventemitter3/5.0.1:
    eventemitter3: private
  /events/3.3.0:
    events: private
  /execa/5.1.1:
    execa: private
  /exeunt/1.1.0:
    exeunt: private
  /exit-x/0.2.2:
    exit-x: private
  /exit/0.1.2:
    exit: private
  /expect-type/1.2.1:
    expect-type: private
  /expect/29.7.0:
    expect: private
  /express-rate-limit/7.5.1(express@5.1.0):
    express-rate-limit: private
  /express-session/1.18.1:
    express-session: private
  /express-validator/7.2.1:
    express-validator: private
  /express/5.1.0:
    express: private
  /faker/6.6.6:
    faker: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-fifo/1.3.2:
    fast-fifo: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  /fast-uri/3.0.6:
    fast-uri: private
  /fast-xml-parser/4.5.3:
    fast-xml-parser: private
  /fastq/1.19.1:
    fastq: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /fdir/6.4.6(picomatch@4.0.2):
    fdir: private
  /fecha/4.2.3:
    fecha: private
  /fflate/0.8.2:
    fflate: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /filelist/1.0.4:
    filelist: private
  /fill-range/7.1.1:
    fill-range: private
  /filter-obj/1.1.0:
    filter-obj: private
  /finalhandler/2.1.0:
    finalhandler: private
  /find-cache-dir/2.1.0:
    find-cache-dir: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flat/5.0.2:
    flat: private
  /flatted/3.3.3:
    flatted: private
  /flow-enums-runtime/0.0.5:
    flow-enums-runtime: private
  /flow-parser/0.206.0:
    flow-parser: private
  /fn.name/1.1.0:
    fn.name: private
  /follow-redirects/1.15.9:
    follow-redirects: private
  /for-each/0.3.5:
    for-each: private
  /for-in/1.0.2:
    for-in: private
  /for-own/1.0.0:
    for-own: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data/4.0.3:
    form-data: private
  /formidable/3.5.4:
    formidable: private
  /formik/2.4.6(react@18.2.0):
    formik: private
  /forwarded/0.2.0:
    forwarded: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fresh/2.0.0:
    fresh: private
  /fs-constants/1.0.0:
    fs-constants: private
  /fs-extra/11.3.0:
    fs-extra: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /funpermaproxy/1.1.0:
    funpermaproxy: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-east-asian-width/1.3.0:
    get-east-asian-width: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-nonce/1.0.1:
    get-nonce: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-port/7.1.0:
    get-port: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/8.1.0:
    glob: private
  /globals/13.24.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /graphql/16.11.0:
    graphql: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/4.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /headers-polyfill/4.0.3:
    headers-polyfill: private
  /helmet/8.1.0:
    helmet: private
  /hermes-estree/0.12.0:
    hermes-estree: private
  /hermes-parser/0.12.0:
    hermes-parser: private
  /hermes-profile-transformer/0.0.6:
    hermes-profile-transformer: private
  /hoist-non-react-statics/3.3.2:
    hoist-non-react-statics: private
  /html-encoding-sniffer/4.0.0:
    html-encoding-sniffer: private
  /html-escaper/2.0.2:
    html-escaper: private
  /http-errors/2.0.0:
    http-errors: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.6.3:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/5.3.2:
    ignore: private
  /image-size/1.2.1:
    image-size: private
  /immer/9.0.21:
    immer: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /internal-slot/1.1.0:
    internal-slot: private
  /invariant/2.2.4:
    invariant: private
  /ip/1.1.9:
    ip: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.3.2:
    is-arrayish: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-directory/0.3.1:
    is-directory: private
  /is-extendable/0.1.1:
    is-extendable: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-interactive/1.0.0:
    is-interactive: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-node-process/1.2.0:
    is-node-process: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-plain-obj/2.1.0:
    is-plain-obj: private
  /is-plain-object/2.0.4:
    is-plain-object: private
  /is-potential-custom-element-name/1.0.1:
    is-potential-custom-element-name: private
  /is-promise/4.0.0:
    is-promise: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-stream/2.0.1:
    is-stream: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /is-wsl/1.1.0:
    is-wsl: private
  /isarray/2.0.5:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /isobject/3.0.1:
    isobject: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/5.2.1:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /iterator.prototype/1.1.5:
    iterator.prototype: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jake/10.9.2:
    jake: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0:
    jest-cli: private
  /jest-config/29.7.0(@types/node@24.0.7):
    jest-config: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/30.0.1:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-emit/1.2.0(jest@29.7.0):
    jest-environment-emit: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/30.0.2:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/29.7.0:
    jest-worker: private
  /jest/30.0.3(@types/node@24.0.7):
    jest: private
  /jiti/2.4.2:
    jiti: private
  /joi/17.13.3:
    joi: private
  /jose/6.0.11:
    jose: private
  /js-message/1.0.7:
    js-message: private
  /js-queue/2.0.2:
    js-queue: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsc-android/250231.0.0:
    jsc-android: private
  /jsc-safe-url/0.2.4:
    jsc-safe-url: private
  /jscodeshift/0.14.0(@babel/preset-env@7.27.2):
    jscodeshift: private
  /jsdom/26.1.0:
    jsdom: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-cycle/1.5.0:
    json-cycle: private
  /json-parse-better-errors/1.0.2:
    json-parse-better-errors: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json-stringify-safe/5.0.1:
    json-stringify-safe: private
  /json5/2.2.3:
    json5: private
  /jsonfile/6.1.0:
    jsonfile: private
  /jsonwebtoken/9.0.2:
    jsonwebtoken: private
  /jsx-ast-utils/3.3.5:
    jsx-ast-utils: private
  /jwa/1.4.2:
    jwa: private
  /jws/3.2.2:
    jws: private
  /keyv/4.5.4:
    keyv: private
  /kind-of/6.0.3:
    kind-of: private
  /kleur/3.0.3:
    kleur: private
  /kuler/2.0.0:
    kuler: private
  /lazystream/1.0.1:
    lazystream: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /listr2/8.3.3:
    listr2: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash-es/4.17.21:
    lodash-es: private
  /lodash.camelcase/4.3.0:
    lodash.camelcase: private
  /lodash.debounce/4.0.8:
    lodash.debounce: private
  /lodash.includes/4.3.0:
    lodash.includes: private
  /lodash.isboolean/3.0.3:
    lodash.isboolean: private
  /lodash.isinteger/4.0.4:
    lodash.isinteger: private
  /lodash.isnumber/3.0.3:
    lodash.isnumber: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.isstring/4.0.1:
    lodash.isstring: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.once/4.1.1:
    lodash.once: private
  /lodash.throttle/4.1.1:
    lodash.throttle: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/4.1.0:
    log-symbols: private
  /log-update/6.1.0:
    log-update: private
  /logform/2.7.0:
    logform: private
  /logkitty/0.7.1:
    logkitty: private
  /long/5.3.2:
    long: private
  /loose-envify/1.4.0:
    loose-envify: private
  /loupe/3.1.4:
    loupe: private
  /lru-cache/5.1.1:
    lru-cache: private
  /lucide-react/0.525.0(react@19.1.0):
    lucide-react: private
  /lz-string/1.5.0:
    lz-string: private
  /magic-string/0.30.17:
    magic-string: private
  /make-dir/2.1.0:
    make-dir: private
  /make-error/1.3.6:
    make-error: private
  /makeerror/1.0.12:
    makeerror: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /mdn-data/2.0.14:
    mdn-data: private
  /media-typer/0.3.0:
    media-typer: private
  /memoize-one/5.2.1:
    memoize-one: private
  /merge-descriptors/2.0.0:
    merge-descriptors: private
  /merge-options/3.0.4:
    merge-options: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /metro-babel-transformer/0.76.9:
    metro-babel-transformer: private
  /metro-cache-key/0.76.9:
    metro-cache-key: private
  /metro-cache/0.76.9:
    metro-cache: private
  /metro-config/0.76.9:
    metro-config: private
  /metro-core/0.76.9:
    metro-core: private
  /metro-file-map/0.76.9:
    metro-file-map: private
  /metro-inspector-proxy/0.76.9:
    metro-inspector-proxy: private
  /metro-minify-terser/0.76.9:
    metro-minify-terser: private
  /metro-minify-uglify/0.76.9:
    metro-minify-uglify: private
  /metro-react-native-babel-preset/0.76.9(@babel/core@7.27.7):
    metro-react-native-babel-preset: private
  /metro-react-native-babel-transformer/0.76.9(@babel/core@7.27.7):
    metro-react-native-babel-transformer: private
  /metro-resolver/0.76.7:
    metro-resolver: private
  /metro-runtime/0.76.9:
    metro-runtime: private
  /metro-source-map/0.76.7:
    metro-source-map: private
  /metro-symbolicate/0.76.7:
    metro-symbolicate: private
  /metro-transform-plugins/0.76.9:
    metro-transform-plugins: private
  /metro-transform-worker/0.76.9:
    metro-transform-worker: private
  /metro/0.76.9:
    metro: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.54.0:
    mime-db: private
  /mime-types/3.0.1:
    mime-types: private
  /mime/2.6.0:
    mime: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /mimic-function/5.0.1:
    mimic-function: private
  /min-indent/1.0.1:
    min-indent: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mixin-object/2.0.1:
    mixin-object: private
  /mkdirp-classic/0.5.3:
    mkdirp-classic: private
  /mkdirp/0.5.6:
    mkdirp: private
  /moment/2.30.1:
    moment: private
  /morgan/1.10.0:
    morgan: private
  /mrmime/2.0.1:
    mrmime: private
  /ms/2.1.3:
    ms: private
  /msw/2.10.2(typescript@5.8.3):
    msw: private
  /multer/2.0.1:
    multer: private
  /multi-sort-stream/1.0.4:
    multi-sort-stream: private
  /multipipe/4.0.0:
    multipipe: private
  /mute-stream/2.0.0:
    mute-stream: private
  /mv/2.1.1:
    mv: private
  /nan/2.22.2:
    nan: private
  /nanoid/3.3.11:
    nanoid: private
  /napi-postinstall/0.2.5:
    napi-postinstall: private
  /natural-compare-lite/1.4.0:
    natural-compare-lite: private
  /natural-compare/1.4.0:
    natural-compare: private
  /ncp/2.0.0:
    ncp: private
  /negotiator/0.6.4:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /nocache/3.0.4:
    nocache: private
  /nock/14.0.5:
    nock: private
  /node-abort-controller/3.1.1:
    node-abort-controller: private
  /node-dir/0.1.17:
    node-dir: private
  /node-fetch/2.7.0:
    node-fetch: private
  /node-int64/0.4.0:
    node-int64: private
  /node-ipc/9.2.1:
    node-ipc: private
  /node-releases/2.0.19:
    node-releases: private
  /node-stream-zip/1.15.0:
    node-stream-zip: private
  /nodemailer/7.0.3:
    nodemailer: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /nth-check/2.1.1:
    nth-check: private
  /nullthrows/1.1.1:
    nullthrows: private
  /nwsapi/2.2.20:
    nwsapi: private
  /oauth4webapi/3.5.3:
    oauth4webapi: private
  /ob1/0.76.7:
    ob1: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.entries/1.1.9:
    object.entries: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.values/1.2.1:
    object.values: private
  /on-finished/2.4.1:
    on-finished: private
  /on-headers/1.0.2:
    on-headers: private
  /once/1.4.0:
    once: private
  /one-time/1.0.0:
    one-time: private
  /onetime/5.1.2:
    onetime: private
  /open/6.4.0:
    open: private
  /opencollective-postinstall/2.0.3:
    opencollective-postinstall: private
  /optionator/0.9.4:
    optionator: private
  /ora/5.4.1:
    ora: private
  /otplib/12.0.1:
    otplib: private
  /outvariant/1.4.3:
    outvariant: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-try/2.2.0:
    p-try: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/4.0.0:
    parse-json: private
  /parse5/7.3.0:
    parse5: private
  /parseurl/1.3.3:
    parseurl: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-to-regexp/6.3.0:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pathe/2.0.3:
    pathe: private
  /pathval/2.0.1:
    pathval: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/4.0.2:
    picomatch: private
  /pidtree/0.6.0:
    pidtree: private
  /pify/4.0.1:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /pngjs/5.0.0:
    pngjs: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.5.6:
    postcss: private
  /preact-render-to-string/6.5.11(preact@10.24.3):
    preact-render-to-string: private
  /preact/10.24.3:
    preact: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-format/29.7.0:
    pretty-format: private
  /prisma/6.10.1(typescript@5.8.3):
    prisma: private
  /process-nextick-args/2.0.1:
    process-nextick-args: private
  /process/0.11.10:
    process: private
  /promise/8.3.0:
    promise: private
  /promisify-child-process/4.1.2:
    promisify-child-process: private
  /prompts/2.4.2:
    prompts: private
  /prop-types/15.8.1:
    prop-types: private
  /propagate/2.0.1:
    propagate: private
  /proper-lockfile/3.2.0:
    proper-lockfile: private
  /properties-reader/2.3.0:
    properties-reader: private
  /property-expr/2.0.6:
    property-expr: private
  /protobufjs/7.5.3:
    protobufjs: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /proxy-from-env/1.1.0:
    proxy-from-env: private
  /psl/1.15.0:
    psl: private
  /pump/3.0.3:
    pump: private
  /punycode/2.3.1:
    punycode: private
  /pure-rand/6.1.0:
    pure-rand: private
  /qrcode/1.5.4:
    qrcode: private
  /qs/6.14.0:
    qs: private
  /query-string/7.1.3:
    query-string: private
  /querystringify/2.2.0:
    querystringify: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /queue/6.0.2:
    queue: private
  /random-bytes/1.0.0:
    random-bytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/3.0.0:
    raw-body: private
  /react-devtools-core/4.28.5:
    react-devtools-core: private
  /react-dom/19.1.0(react@19.1.0):
    react-dom: private
  /react-fast-compare/2.0.4:
    react-fast-compare: private
  /react-freeze/1.0.4(react@18.2.0):
    react-freeze: private
  /react-hook-form/7.59.0(react@19.1.0):
    react-hook-form: private
  /react-is/18.3.1:
    react-is: private
  /react-native-biometrics/3.0.1(react-native@0.72.3):
    react-native-biometrics: private
  /react-native-config/1.5.5:
    react-native-config: private
  /react-native-device-info/10.14.0(react-native@0.72.3):
    react-native-device-info: private
  /react-native-encrypted-storage/4.0.3(react-native@0.72.3)(react@18.2.0):
    react-native-encrypted-storage: private
  /react-native-fast-image/8.6.3(react-native@0.72.3)(react@18.2.0):
    react-native-fast-image: private
  /react-native-gesture-handler/2.26.0(react-native@0.72.3)(react@18.2.0):
    react-native-gesture-handler: private
  /react-native-is-edge-to-edge/1.1.7(react-native@0.72.3)(react@18.2.0):
    react-native-is-edge-to-edge: private
  /react-native-keychain/8.2.0:
    react-native-keychain: private
  /react-native-paper/5.14.5(react-native-safe-area-context@4.14.1)(react-native@0.72.3)(react@18.2.0):
    react-native-paper: private
  /react-native-permissions/3.10.1(react-native@0.72.3)(react@18.2.0):
    react-native-permissions: private
  /react-native-reanimated/3.18.0(@babel/core@7.27.7)(react-native@0.72.3)(react@18.2.0):
    react-native-reanimated: private
  /react-native-safe-area-context/4.14.1(react-native@0.72.3)(react@18.2.0):
    react-native-safe-area-context: private
  /react-native-screens/3.37.0(react-native@0.72.3)(react@18.2.0):
    react-native-screens: private
  /react-native-splash-screen/3.3.0(react-native@0.72.3):
    react-native-splash-screen: private
  /react-native-sqlite-storage/6.0.1(react-native@0.72.3):
    react-native-sqlite-storage: private
  /react-native-svg/13.14.1(react-native@0.72.3)(react@18.2.0):
    react-native-svg: private
  /react-native-vector-icons/10.2.0:
    react-native-vector-icons: private
  /react-native-webview/13.15.0(react-native@0.72.3)(react@18.2.0):
    react-native-webview: private
  /react-native/0.72.3(@babel/core@7.27.7)(@babel/preset-env@7.27.2)(react@18.2.0):
    react-native: private
  /react-redux/8.1.3(@types/react@18.3.23)(react-native@0.72.3)(react@18.2.0)(redux@5.0.1):
    react-redux: private
  /react-refresh/0.17.0:
    react-refresh: private
  /react-remove-scroll-bar/2.3.8(@types/react@19.1.8)(react@19.1.0):
    react-remove-scroll-bar: private
  /react-remove-scroll/2.7.1(@types/react@19.1.8)(react@19.1.0):
    react-remove-scroll: private
  /react-router-dom/7.6.3(react-dom@19.1.0)(react@19.1.0):
    react-router-dom: private
  /react-router/7.6.3(react-dom@19.1.0)(react@19.1.0):
    react-router: private
  /react-shallow-renderer/16.15.0(react@18.2.0):
    react-shallow-renderer: private
  /react-style-singleton/2.2.3(@types/react@19.1.8)(react@19.1.0):
    react-style-singleton: private
  /react-test-renderer/18.2.0(react@18.2.0):
    react-test-renderer: private
  /react/19.1.0:
    react: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdir-glob/1.1.3:
    readdir-glob: private
  /readline/1.3.0:
    readline: private
  /recast/0.21.5:
    recast: private
  /redent/3.0.0:
    redent: private
  /redis/5.5.6:
    redis: private
  /redux-persist/6.0.0(react@18.2.0)(redux@5.0.1):
    redux-persist: private
  /redux-thunk/2.4.2(redux@4.2.1):
    redux-thunk: private
  /redux/4.2.1:
    redux: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regenerate-unicode-properties/10.2.0:
    regenerate-unicode-properties: private
  /regenerate/1.4.2:
    regenerate: private
  /regenerator-runtime/0.13.11:
    regenerator-runtime: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /regexpu-core/6.2.0:
    regexpu-core: private
  /regjsgen/0.8.0:
    regjsgen: private
  /regjsparser/0.12.0:
    regjsparser: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /require-main-filename/2.0.0:
    require-main-filename: private
  /requires-port/1.0.0:
    requires-port: private
  /reselect/4.1.8:
    reselect: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/2.0.0-next.5:
    resolve: private
  /restore-cursor/3.1.0:
    restore-cursor: private
  /retry/0.12.0:
    retry: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/3.0.2:
    rimraf: private
  /rollup/4.44.1:
    rollup: private
  /router/2.2.0:
    router: private
  /rrweb-cssom/0.8.0:
    rrweb-cssom: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-json-stringify/1.2.0:
    safe-json-stringify: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safe-stable-stringify/2.5.0:
    safe-stable-stringify: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /sanitize-filename/1.6.3:
    sanitize-filename: private
  /saxes/6.0.0:
    saxes: private
  /scheduler/0.26.0:
    scheduler: private
  /semver/6.3.1:
    semver: private
  /send/1.2.0:
    send: private
  /serialize-error/8.1.0:
    serialize-error: private
  /serve-static/2.2.0:
    serve-static: private
  /set-blocking/2.0.0:
    set-blocking: private
  /set-cookie-parser/2.7.1:
    set-cookie-parser: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shallow-clone/1.0.0:
    shallow-clone: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /shell-quote/1.8.3:
    shell-quote: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /siginfo/2.0.0:
    siginfo: private
  /signal-exit/3.0.7:
    signal-exit: private
  /simple-swizzle/0.2.2:
    simple-swizzle: private
  /sirv/3.0.1:
    sirv: private
  /sisteransi/1.0.5:
    sisteransi: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/5.0.0:
    slice-ansi: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.13:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /spawn-command/0.0.2:
    spawn-command: private
  /speakeasy/2.0.0:
    speakeasy: private
  /split-ca/1.0.1:
    split-ca: private
  /split-on-first/1.1.0:
    split-on-first: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /ssh-remote-port-forward/1.0.4:
    ssh-remote-port-forward: private
  /ssh2/1.16.0:
    ssh2: private
  /stack-generator/2.0.10:
    stack-generator: private
  /stack-trace/0.0.10:
    stack-trace: private
  /stack-utils/2.0.6:
    stack-utils: private
  /stackback/0.0.2:
    stackback: private
  /stackframe/1.3.4:
    stackframe: private
  /stacktrace-gps/3.1.2:
    stacktrace-gps: private
  /stacktrace-js/2.0.2:
    stacktrace-js: private
  /stacktrace-parser/0.1.11:
    stacktrace-parser: private
  /statuses/2.0.2:
    statuses: private
  /std-env/3.9.0:
    std-env: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /stream-chain/2.2.5:
    stream-chain: private
  /stream-json/1.9.1:
    stream-json: private
  /streamsearch/1.1.0:
    streamsearch: private
  /streamx/2.22.1:
    streamx: private
  /strict-event-emitter/0.5.1:
    strict-event-emitter: private
  /strict-uri-encode/2.0.0:
    strict-uri-encode: private
  /string-argv/0.3.2:
    string-argv: private
  /string-length/4.0.2:
    string-length: private
  /string-natural-compare/3.0.1:
    string-natural-compare: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string.prototype.matchall/4.0.12:
    string.prototype.matchall: private
  /string.prototype.repeat/1.0.0:
    string.prototype.repeat: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/4.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-indent/3.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strip-literal/3.0.0:
    strip-literal: private
  /strnum/1.1.2:
    strnum: private
  /sudo-prompt/9.2.1:
    sudo-prompt: private
  /superagent/10.2.1:
    superagent: private
  /superstruct/0.6.2:
    superstruct: private
  /supertest/7.1.1:
    supertest: private
  /supports-color/8.1.1:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /symbol-tree/3.2.4:
    symbol-tree: private
  /synckit/0.11.8:
    synckit: private
  /tailwindcss/4.1.11:
    tailwindcss: private
  /tar-fs/3.0.10:
    tar-fs: private
  /tar-stream/3.1.7:
    tar-stream: private
  /telnet-client/1.2.8:
    telnet-client: private
  /temp-dir/1.0.0:
    temp-dir: private
  /temp/0.8.4:
    temp: private
  /tempfile/2.0.0:
    tempfile: private
  /terser/5.43.1:
    terser: private
  /test-exclude/6.0.0:
    test-exclude: private
  /testcontainers/11.0.3:
    testcontainers: private
  /text-decoder/1.2.3:
    text-decoder: private
  /text-hex/1.0.0:
    text-hex: private
  /text-table/0.2.0:
    text-table: private
  /thirty-two/1.0.2:
    thirty-two: private
  /throat/5.0.0:
    throat: private
  /through2/2.0.5:
    through2: private
  /tiny-case/1.0.3:
    tiny-case: private
  /tiny-warning/1.0.3:
    tiny-warning: private
  /tinybench/2.9.0:
    tinybench: private
  /tinyexec/0.3.2:
    tinyexec: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /tinypool/1.1.1:
    tinypool: private
  /tinyrainbow/2.0.0:
    tinyrainbow: private
  /tinyspy/4.0.3:
    tinyspy: private
  /tldts-core/6.1.86:
    tldts-core: private
  /tldts/6.1.86:
    tldts: private
  /tmp/0.2.3:
    tmp: private
  /tmpl/1.0.5:
    tmpl: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /toposort/2.0.2:
    toposort: private
  /totalist/3.0.1:
    totalist: private
  /tough-cookie/5.1.2:
    tough-cookie: private
  /tr46/5.1.1:
    tr46: private
  /trace-event-lib/1.4.1:
    trace-event-lib: private
  /tree-kill/1.2.2:
    tree-kill: private
  /triple-beam/1.4.1:
    triple-beam: private
  /truncate-utf8-bytes/1.0.2:
    truncate-utf8-bytes: private
  /ts-api-utils/1.4.3(typescript@5.8.3):
    ts-api-utils: private
  /ts-jest/29.4.0(@babel/core@7.27.7)(jest@30.0.3)(typescript@5.8.3):
    ts-jest: private
  /tslib/2.8.1:
    tslib: private
  /tsutils/3.21.0(typescript@5.8.3):
    tsutils: private
  /tsx/4.20.3:
    tsx: private
  /tweetnacl/0.14.5:
    tweetnacl: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.0.8:
    type-detect: private
  /type-fest/4.41.0:
    type-fest: private
  /type-is/2.0.1:
    type-is: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /typedarray/0.0.6:
    typedarray: private
  /uglify-es/3.3.9:
    uglify-es: private
  /uid-safe/2.1.5:
    uid-safe: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /undici-types/7.8.0:
    undici-types: private
  /undici/7.11.0:
    undici: private
  /unicode-canonical-property-names-ecmascript/2.0.1:
    unicode-canonical-property-names-ecmascript: private
  /unicode-match-property-ecmascript/2.0.0:
    unicode-match-property-ecmascript: private
  /unicode-match-property-value-ecmascript/2.2.0:
    unicode-match-property-value-ecmascript: private
  /unicode-property-aliases-ecmascript/2.1.0:
    unicode-property-aliases-ecmascript: private
  /universalify/2.0.1:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /unrs-resolver/1.9.2:
    unrs-resolver: private
  /update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /url-parse/1.5.10:
    url-parse: private
  /use-callback-ref/1.3.3(@types/react@19.1.8)(react@19.1.0):
    use-callback-ref: private
  /use-latest-callback/0.2.4(react@18.2.0):
    use-latest-callback: private
  /use-sidecar/1.1.3(@types/react@19.1.8)(react@19.1.0):
    use-sidecar: private
  /use-sync-external-store/1.5.0(react@18.2.0):
    use-sync-external-store: private
  /utf8-byte-length/1.0.5:
    utf8-byte-length: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/11.1.0:
    uuid: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /validator/13.12.0:
    validator: private
  /vary/1.1.2:
    vary: private
  /vite-node/3.2.4:
    vite-node: private
  /vite/7.0.0:
    vite: private
  /vitest/3.2.4(@vitest/ui@3.2.4)(jsdom@26.1.0)(msw@2.10.2):
    vitest: private
  /vlq/1.0.1:
    vlq: private
  /w3c-xmlserializer/5.0.0:
    w3c-xmlserializer: private
  /walker/1.0.8:
    walker: private
  /warn-once/0.1.1:
    warn-once: private
  /wcwidth/1.0.1:
    wcwidth: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /whatwg-encoding/3.1.1:
    whatwg-encoding: private
  /whatwg-fetch/3.6.20:
    whatwg-fetch: private
  /whatwg-mimetype/4.0.0:
    whatwg-mimetype: private
  /whatwg-url/14.2.0:
    whatwg-url: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-module/2.0.1:
    which-module: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/1.3.1:
    which: private
  /why-is-node-running/2.3.0:
    why-is-node-running: private
  /winston-transport/4.9.0:
    winston-transport: private
  /winston/3.17.0:
    winston: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/9.0.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /ws/7.5.10:
    ws: private
  /xml-name-validator/5.0.0:
    xml-name-validator: private
  /xmlchars/2.2.0:
    xmlchars: private
  /xtend/4.0.2:
    xtend: private
  /y18n/4.0.3:
    y18n: private
  /yallist/3.1.1:
    yallist: private
  /yaml/2.8.0:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs-unparser/2.0.0:
    yargs-unparser: private
  /yargs/17.7.2:
    yargs: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /yoctocolors-cjs/2.1.2:
    yoctocolors-cjs: private
  /yup/1.6.1:
    yup: private
  /zip-stream/6.0.1:
    zip-stream: private
  /zod/3.25.67:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.0
pendingBuilds: []
prunedAt: Sun, 29 Jun 2025 08:30:45 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/core/1.4.3
  - /@emnapi/runtime/1.4.3
  - /@emnapi/wasi-threads/1.0.2
  - /@esbuild/aix-ppc64/0.25.5
  - /@esbuild/android-arm/0.25.5
  - /@esbuild/android-arm64/0.25.5
  - /@esbuild/android-x64/0.25.5
  - /@esbuild/darwin-x64/0.25.5
  - /@esbuild/freebsd-arm64/0.25.5
  - /@esbuild/freebsd-x64/0.25.5
  - /@esbuild/linux-arm/0.25.5
  - /@esbuild/linux-arm64/0.25.5
  - /@esbuild/linux-ia32/0.25.5
  - /@esbuild/linux-loong64/0.25.5
  - /@esbuild/linux-mips64el/0.25.5
  - /@esbuild/linux-ppc64/0.25.5
  - /@esbuild/linux-riscv64/0.25.5
  - /@esbuild/linux-s390x/0.25.5
  - /@esbuild/linux-x64/0.25.5
  - /@esbuild/netbsd-arm64/0.25.5
  - /@esbuild/netbsd-x64/0.25.5
  - /@esbuild/openbsd-arm64/0.25.5
  - /@esbuild/openbsd-x64/0.25.5
  - /@esbuild/sunos-x64/0.25.5
  - /@esbuild/win32-arm64/0.25.5
  - /@esbuild/win32-ia32/0.25.5
  - /@esbuild/win32-x64/0.25.5
  - /@napi-rs/wasm-runtime/0.2.11
  - /@rollup/rollup-android-arm-eabi/4.44.1
  - /@rollup/rollup-android-arm64/4.44.1
  - /@rollup/rollup-darwin-x64/4.44.1
  - /@rollup/rollup-freebsd-arm64/4.44.1
  - /@rollup/rollup-freebsd-x64/4.44.1
  - /@rollup/rollup-linux-arm-gnueabihf/4.44.1
  - /@rollup/rollup-linux-arm-musleabihf/4.44.1
  - /@rollup/rollup-linux-arm64-gnu/4.44.1
  - /@rollup/rollup-linux-arm64-musl/4.44.1
  - /@rollup/rollup-linux-loongarch64-gnu/4.44.1
  - /@rollup/rollup-linux-powerpc64le-gnu/4.44.1
  - /@rollup/rollup-linux-riscv64-gnu/4.44.1
  - /@rollup/rollup-linux-riscv64-musl/4.44.1
  - /@rollup/rollup-linux-s390x-gnu/4.44.1
  - /@rollup/rollup-linux-x64-gnu/4.44.1
  - /@rollup/rollup-linux-x64-musl/4.44.1
  - /@rollup/rollup-win32-arm64-msvc/4.44.1
  - /@rollup/rollup-win32-ia32-msvc/4.44.1
  - /@rollup/rollup-win32-x64-msvc/4.44.1
  - /@tybys/wasm-util/0.9.0
  - /@unrs/resolver-binding-android-arm-eabi/1.9.2
  - /@unrs/resolver-binding-android-arm64/1.9.2
  - /@unrs/resolver-binding-darwin-x64/1.9.2
  - /@unrs/resolver-binding-freebsd-x64/1.9.2
  - /@unrs/resolver-binding-linux-arm-gnueabihf/1.9.2
  - /@unrs/resolver-binding-linux-arm-musleabihf/1.9.2
  - /@unrs/resolver-binding-linux-arm64-gnu/1.9.2
  - /@unrs/resolver-binding-linux-arm64-musl/1.9.2
  - /@unrs/resolver-binding-linux-ppc64-gnu/1.9.2
  - /@unrs/resolver-binding-linux-riscv64-gnu/1.9.2
  - /@unrs/resolver-binding-linux-riscv64-musl/1.9.2
  - /@unrs/resolver-binding-linux-s390x-gnu/1.9.2
  - /@unrs/resolver-binding-linux-x64-gnu/1.9.2
  - /@unrs/resolver-binding-linux-x64-musl/1.9.2
  - /@unrs/resolver-binding-wasm32-wasi/1.9.2
  - /@unrs/resolver-binding-win32-arm64-msvc/1.9.2
  - /@unrs/resolver-binding-win32-ia32-msvc/1.9.2
  - /@unrs/resolver-binding-win32-x64-msvc/1.9.2
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
