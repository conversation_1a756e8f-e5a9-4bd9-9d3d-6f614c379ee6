{"name": "otplib", "description": "HMAC-based (HOTP) and Time-based (TOTP) One-Time Password library", "version": "12.0.1", "main": "./index.js", "publishConfig": {"access": "public"}, "scripts": {}, "dependencies": {"@otplib/core": "^12.0.1", "@otplib/preset-default": "^12.0.1", "@otplib/preset-v11": "^12.0.1"}, "otplib": {}, "keywords": ["totp", "hotp", "otp", "authenticator", "one time password", "google authenticator", "authentication", "2FA", "2 factor", "node", "browser"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://yeojz.otplib.dev", "repository": "https://github.com/yeojz/otplib", "types": "./index.d.ts"}