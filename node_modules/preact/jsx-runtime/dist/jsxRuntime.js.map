{"version": 3, "file": "jsxRuntime.js", "sources": ["../src/index.js"], "sourcesContent": ["import { options, Fragment } from 'preact';\n\n/** @typedef {import('preact').VNode} VNode */\n\nlet vnodeId = 0;\n\n/**\n * @fileoverview\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\n * - jsx(type, props, key)\n * - jsxs(type, props, key)\n * - jsxDEV(type, props, key, __source, __self)\n *\n * The implementation of createVNode here is optimized for performance.\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\n */\n\n/**\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\n * @param {VNode['type']} type\n * @param {VNode['props']} props\n * @param {VNode['key']} [key]\n * @param {string} [__self]\n * @param {string} [__source]\n */\nfunction createVNode(type, props, key, __self, __source) {\n\t// We'll want to preserve `ref` in props to get rid of the need for\n\t// forwardRef components in the future, but that should happen via\n\t// a separate PR.\n\tlet normalizedProps = {},\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'ref') {\n\t\t\tref = props[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tconst vnode = {\n\t\ttype,\n\t\tprops: normalizedProps,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\t_hydrating: null,\n\t\tconstructor: undefined,\n\t\t_original: --vnodeId,\n\t\t__source,\n\t\t__self\n\t};\n\n\t// If a Component VNode, check for and apply defaultProps.\n\t// Note: `type` is often a String, and can be `undefined` in development.\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\n\t\tfor (i in ref)\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\n\t\t\t\tnormalizedProps[i] = ref[i];\n\t\t\t}\n\t}\n\n\tif (options.vnode) options.vnode(vnode);\n\treturn vnode;\n}\n\nexport {\n\tcreateVNode as jsx,\n\tcreateVNode as jsxs,\n\tcreateVNode as jsxDEV,\n\tFragment\n};\n"], "names": ["vnodeId", "createVNode", "type", "props", "key", "__self", "__source", "ref", "i", "normalizedProps", "vnode", "__k", "__", "__b", "__e", "__d", "undefined", "__c", "__h", "constructor", "__v", "defaultProps", "options"], "mappings": "wBAIIA,EAAU,EAqBd,SAASC,EAAYC,EAAMC,EAAOC,EAAKC,EAAQC,GAI9C,IACCC,EACAC,EAFGC,EAAkB,CAAtB,EAGA,IAAKD,KAAKL,EACA,OAALK,EACHD,EAAMJ,EAAMK,GAEZC,EAAgBD,GAAKL,EAAMK,GAI7B,IAAME,EAAQ,CACbR,KAAAA,EACAC,MAAOM,EACPL,IAAAA,EACAG,IAAAA,EACAI,IAAW,KACXC,GAAS,KACTC,IAAQ,EACRC,IAAM,KACNC,SAAUC,EACVC,IAAY,KACZC,IAAY,KACZC,iBAAaH,EACbI,MAAapB,EACbM,SAAAA,EACAD,OAAAA,GAKD,GAAoB,mBAATH,IAAwBK,EAAML,EAAKmB,cAC7C,IAAKb,KAAKD,OACyB,IAAvBE,EAAgBD,KAC1BC,EAAgBD,GAAKD,EAAIC,IAK5B,OADIc,EAAOA,QAACZ,OAAOY,EAAAA,QAAQZ,MAAMA,GAC1BA,CACP"}