sudo: required

language: node_js

node_js:
  - "8"

before_script:
  - docker pull ubuntu

services:
  - docker

before_install:
  - curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
  - sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
  - sudo apt-get update
  - sudo apt-get -y install docker-ce

install:
  - npm install

script:
  - npm test
