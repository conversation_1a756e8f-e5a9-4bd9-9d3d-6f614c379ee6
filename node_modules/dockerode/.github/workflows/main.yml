name: CI

on:
  push:
  pull_request:
  workflow_dispatch:

jobs:
  build_nodejs:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [14.x, 16.x, 18.x, 20.x, 22.x]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Provisioning
        run: |
          docker --version
          node -v
          docker pull ubuntu

      - name: NPM install
        run: npm install
      
      - name: NPM test
        run: npm test

