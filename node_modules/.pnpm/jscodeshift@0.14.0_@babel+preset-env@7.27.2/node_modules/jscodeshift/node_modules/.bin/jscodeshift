#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/jscodeshift@0.14.0_@babel+preset-env@7.27.2/node_modules/jscodeshift/bin/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/jscodeshift@0.14.0_@babel+preset-env@7.27.2/node_modules/jscodeshift/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/jscodeshift@0.14.0_@babel+preset-env@7.27.2/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/jscodeshift@0.14.0_@babel+preset-env@7.27.2/node_modules/jscodeshift/bin/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/jscodeshift@0.14.0_@babel+preset-env@7.27.2/node_modules/jscodeshift/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/jscodeshift@0.14.0_@babel+preset-env@7.27.2/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/jscodeshift.js" "$@"
else
  exec node  "$basedir/../../bin/jscodeshift.js" "$@"
fi
