#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/terser@5.43.1/node_modules/terser/bin/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/terser@5.43.1/node_modules/terser/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/terser@5.43.1/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/terser@5.43.1/node_modules/terser/bin/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/terser@5.43.1/node_modules/terser/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/terser@5.43.1/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../terser@5.43.1/node_modules/terser/bin/terser" "$@"
else
  exec node  "$basedir/../../../../../terser@5.43.1/node_modules/terser/bin/terser" "$@"
fi
