#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/index.js" "$@"
else
  exec node  "$basedir/../../../../../../opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/index.js" "$@"
fi
