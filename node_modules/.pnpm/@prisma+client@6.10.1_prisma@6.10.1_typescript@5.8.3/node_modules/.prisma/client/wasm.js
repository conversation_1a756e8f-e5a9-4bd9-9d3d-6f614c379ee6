
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.TenantScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  domain: 'domain',
  subdomain: 'subdomain',
  description: 'description',
  logo: 'logo',
  website: 'website',
  industry: 'industry',
  size: 'size',
  status: 'status',
  plan: 'plan',
  trialEndsAt: 'trialEndsAt',
  settings: 'settings',
  features: 'features',
  limits: 'limits',
  branding: 'branding',
  billingEmail: 'billingEmail',
  subscriptionId: 'subscriptionId',
  customerId: 'customerId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deletedAt: 'deletedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  avatar: 'avatar',
  status: 'status',
  lastLoginAt: 'lastLoginAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  password: 'password',
  emailVerified: 'emailVerified'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  permissions: 'permissions',
  isSystem: 'isSystem',
  isDefault: 'isDefault',
  priority: 'priority',
  color: 'color',
  icon: 'icon',
  category: 'category',
  tags: 'tags',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  parentRoleId: 'parentRoleId',
  tenantId: 'tenantId'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  roleId: 'roleId',
  assignedBy: 'assignedBy',
  assignedAt: 'assignedAt',
  expiresAt: 'expiresAt',
  isActive: 'isActive',
  conditions: 'conditions',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.DealScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  status: 'status',
  stage: 'stage',
  dealValue: 'dealValue',
  currency: 'currency',
  priority: 'priority',
  dealType: 'dealType',
  dealSource: 'dealSource',
  confidentiality: 'confidentiality',
  tags: 'tags',
  targetCompany: 'targetCompany',
  targetIndustry: 'targetIndustry',
  targetRevenue: 'targetRevenue',
  targetEmployees: 'targetEmployees',
  targetLocation: 'targetLocation',
  targetWebsite: 'targetWebsite',
  targetDescription: 'targetDescription',
  targetCompanyId: 'targetCompanyId',
  enterpriseValue: 'enterpriseValue',
  equityValue: 'equityValue',
  ebitda: 'ebitda',
  revenue: 'revenue',
  multiple: 'multiple',
  expectedCloseDate: 'expectedCloseDate',
  actualCloseDate: 'actualCloseDate',
  firstContactDate: 'firstContactDate',
  loiSignedDate: 'loiSignedDate',
  ddStartDate: 'ddStartDate',
  ddEndDate: 'ddEndDate',
  currentStageId: 'currentStageId',
  stageEnteredAt: 'stageEnteredAt',
  daysInCurrentStage: 'daysInCurrentStage',
  totalDaysInPipeline: 'totalDaysInPipeline',
  probability: 'probability',
  weightedValue: 'weightedValue',
  forecastCategory: 'forecastCategory',
  healthScore: 'healthScore',
  riskLevel: 'riskLevel',
  lastActivityDate: 'lastActivityDate',
  competitiveProcess: 'competitiveProcess',
  competitors: 'competitors',
  internalNotes: 'internalNotes',
  nextSteps: 'nextSteps',
  keyRisks: 'keyRisks',
  keyOpportunities: 'keyOpportunities',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  createdBy: 'createdBy',
  assignedTo: 'assignedTo'
};

exports.Prisma.TargetCompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  website: 'website',
  industry: 'industry',
  sector: 'sector',
  subSector: 'subSector',
  country: 'country',
  region: 'region',
  city: 'city',
  address: 'address',
  revenue: 'revenue',
  ebitda: 'ebitda',
  employees: 'employees',
  foundedYear: 'foundedYear',
  companyType: 'companyType',
  status: 'status',
  primaryContact: 'primaryContact',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  opportunityScore: 'opportunityScore',
  strategicFit: 'strategicFit',
  financialHealth: 'financialHealth',
  marketCap: 'marketCap',
  enterpriseValue: 'enterpriseValue',
  tags: 'tags',
  source: 'source',
  sourceDetails: 'sourceDetails',
  lastContactDate: 'lastContactDate',
  nextFollowUp: 'nextFollowUp',
  notes: 'notes',
  researchNotes: 'researchNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId'
};

exports.Prisma.TargetCompanyInteractionScalarFieldEnum = {
  id: 'id',
  targetId: 'targetId',
  userId: 'userId',
  type: 'type',
  subject: 'subject',
  description: 'description',
  outcome: 'outcome',
  scheduledAt: 'scheduledAt',
  completedAt: 'completedAt',
  followUpRequired: 'followUpRequired',
  followUpDate: 'followUpDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TargetCompanyWatchlistScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isDefault: 'isDefault',
  isShared: 'isShared',
  criteria: 'criteria',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  createdBy: 'createdBy'
};

exports.Prisma.VirtualDataRoomScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  dealId: 'dealId',
  isActive: 'isActive',
  expiresAt: 'expiresAt',
  maxUsers: 'maxUsers',
  requiresApproval: 'requiresApproval',
  allowDownload: 'allowDownload',
  allowPrint: 'allowPrint',
  allowCopy: 'allowCopy',
  watermarkEnabled: 'watermarkEnabled',
  sessionTimeout: 'sessionTimeout',
  logoUrl: 'logoUrl',
  primaryColor: 'primaryColor',
  customDomain: 'customDomain',
  totalViews: 'totalViews',
  totalDownloads: 'totalDownloads',
  uniqueVisitors: 'uniqueVisitors',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  createdBy: 'createdBy'
};

exports.Prisma.VDRFolderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  path: 'path',
  parentId: 'parentId',
  vdrId: 'vdrId',
  isProtected: 'isProtected',
  order: 'order',
  accessLevel: 'accessLevel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VDRDocumentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  originalName: 'originalName',
  mimeType: 'mimeType',
  size: 'size',
  storageProvider: 'storageProvider',
  storagePath: 'storagePath',
  storageKey: 'storageKey',
  version: 'version',
  status: 'status',
  checksum: 'checksum',
  vdrId: 'vdrId',
  folderId: 'folderId',
  isEncrypted: 'isEncrypted',
  accessLevel: 'accessLevel',
  tags: 'tags',
  description: 'description',
  viewCount: 'viewCount',
  downloadCount: 'downloadCount',
  lastViewedAt: 'lastViewedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tenantId: 'tenantId',
  uploadedBy: 'uploadedBy',
  parentId: 'parentId'
};

exports.Prisma.VDRUserScalarFieldEnum = {
  id: 'id',
  vdrId: 'vdrId',
  userId: 'userId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  company: 'company',
  title: 'title',
  role: 'role',
  accessLevel: 'accessLevel',
  status: 'status',
  canDownload: 'canDownload',
  canPrint: 'canPrint',
  canComment: 'canComment',
  lastLoginAt: 'lastLoginAt',
  expiresAt: 'expiresAt',
  isActive: 'isActive',
  invitedBy: 'invitedBy',
  invitedAt: 'invitedAt',
  acceptedAt: 'acceptedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VDRPermissionScalarFieldEnum = {
  id: 'id',
  vdrId: 'vdrId',
  userId: 'userId',
  documentId: 'documentId',
  folderId: 'folderId',
  canView: 'canView',
  canDownload: 'canDownload',
  canComment: 'canComment',
  canShare: 'canShare',
  validFrom: 'validFrom',
  validUntil: 'validUntil',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VDRActivityScalarFieldEnum = {
  id: 'id',
  vdrId: 'vdrId',
  userId: 'userId',
  action: 'action',
  documentId: 'documentId',
  folderId: 'folderId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  location: 'location',
  duration: 'duration',
  createdAt: 'createdAt'
};

exports.Prisma.DueDiligenceItemScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  category: 'category',
  status: 'status',
  priority: 'priority',
  assignedToId: 'assignedToId',
  dueDate: 'dueDate',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  dealId: 'dealId'
};

exports.Prisma.ValuationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  methodology: 'methodology',
  assumptions: 'assumptions',
  calculations: 'calculations',
  results: 'results',
  discountRate: 'discountRate',
  terminalGrowthRate: 'terminalGrowthRate',
  multiples: 'multiples',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  dealId: 'dealId'
};

exports.Prisma.UserMfaScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  secret: 'secret',
  backupCodes: 'backupCodes',
  enabled: 'enabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  entity: 'entity',
  entityId: 'entityId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt',
  userId: 'userId'
};

exports.Prisma.TenantInvitationScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  email: 'email',
  role: 'role',
  token: 'token',
  status: 'status',
  expiresAt: 'expiresAt',
  invitedBy: 'invitedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TenantApiKeyScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  name: 'name',
  keyHash: 'keyHash',
  permissions: 'permissions',
  lastUsedAt: 'lastUsedAt',
  expiresAt: 'expiresAt',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MigrationRecordScalarFieldEnum = {
  id: 'id',
  migrationId: 'migrationId',
  name: 'name',
  version: 'version',
  tenantId: 'tenantId',
  executedAt: 'executedAt',
  executedBy: 'executedBy',
  success: 'success',
  error: 'error',
  rollbackAt: 'rollbackAt',
  rollbackBy: 'rollbackBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PermissionAuditScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  tenantId: 'tenantId',
  action: 'action',
  resourceType: 'resourceType',
  resourceId: 'resourceId',
  changes: 'changes',
  performedBy: 'performedBy',
  reason: 'reason',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.RoleAssignmentRuleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  conditions: 'conditions',
  targetRoleId: 'targetRoleId',
  isActive: 'isActive',
  priority: 'priority',
  tenantId: 'tenantId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PermissionTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  permissions: 'permissions',
  variables: 'variables',
  isSystem: 'isSystem',
  tenantId: 'tenantId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PolicyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  version: 'version',
  isActive: 'isActive',
  priority: 'priority',
  statements: 'statements',
  variables: 'variables',
  metadata: 'metadata',
  tenantId: 'tenantId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PolicySetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  policies: 'policies',
  combiningAlgorithm: 'combiningAlgorithm',
  isActive: 'isActive',
  priority: 'priority',
  tenantId: 'tenantId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PolicyAuditLogScalarFieldEnum = {
  id: 'id',
  policyId: 'policyId',
  action: 'action',
  userId: 'userId',
  tenantId: 'tenantId',
  changes: 'changes',
  reason: 'reason',
  metadata: 'metadata',
  timestamp: 'timestamp'
};

exports.Prisma.DealStageScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  order: 'order',
  isActive: 'isActive',
  color: 'color',
  isDefault: 'isDefault',
  isClosing: 'isClosing',
  autoAdvance: 'autoAdvance',
  requiredFields: 'requiredFields',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealStageHistoryScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  stageId: 'stageId',
  enteredAt: 'enteredAt',
  exitedAt: 'exitedAt',
  daysInStage: 'daysInStage',
  changedBy: 'changedBy',
  reason: 'reason',
  notes: 'notes'
};

exports.Prisma.DealActivityScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  type: 'type',
  subject: 'subject',
  description: 'description',
  startTime: 'startTime',
  endTime: 'endTime',
  location: 'location',
  isCompleted: 'isCompleted',
  createdBy: 'createdBy',
  attendees: 'attendees',
  externalAttendees: 'externalAttendees',
  followUpDate: 'followUpDate',
  followUpNotes: 'followUpNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealContactScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  title: 'title',
  company: 'company',
  role: 'role',
  isPrimary: 'isPrimary',
  isDecisionMaker: 'isDecisionMaker',
  notes: 'notes',
  linkedInUrl: 'linkedInUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealTeamMemberScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  userId: 'userId',
  role: 'role',
  canEdit: 'canEdit',
  canView: 'canView',
  createdAt: 'createdAt'
};

exports.Prisma.DealTaskScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  title: 'title',
  description: 'description',
  status: 'status',
  priority: 'priority',
  dueDate: 'dueDate',
  completedAt: 'completedAt',
  estimatedHours: 'estimatedHours',
  actualHours: 'actualHours',
  assignedTo: 'assignedTo',
  createdBy: 'createdBy',
  dependsOn: 'dependsOn',
  blockedBy: 'blockedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealNoteScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  content: 'content',
  isPrivate: 'isPrivate',
  tags: 'tags',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealMilestoneScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  name: 'name',
  description: 'description',
  status: 'status',
  targetDate: 'targetDate',
  actualDate: 'actualDate',
  isRequired: 'isRequired',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DealPipelineScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isDefault: 'isDefault',
  isActive: 'isActive',
  stages: 'stages',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceFrameworkScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  jurisdiction: 'jurisdiction',
  category: 'category',
  applicability: 'applicability',
  requirements: 'requirements',
  penalties: 'penalties',
  lastUpdated: 'lastUpdated',
  version: 'version',
  isActive: 'isActive',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceStatusScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  frameworkId: 'frameworkId',
  requirementId: 'requirementId',
  status: 'status',
  completionPercentage: 'completionPercentage',
  lastUpdated: 'lastUpdated',
  updatedBy: 'updatedBy',
  dueDate: 'dueDate',
  completedDate: 'completedDate',
  isOverdue: 'isOverdue',
  daysRemaining: 'daysRemaining',
  submittedDocuments: 'submittedDocuments',
  missingDocuments: 'missingDocuments',
  approvalStatus: 'approvalStatus',
  approvalDate: 'approvalDate',
  approvalReference: 'approvalReference',
  issues: 'issues',
  riskLevel: 'riskLevel',
  notes: 'notes',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceAlertScalarFieldEnum = {
  id: 'id',
  type: 'type',
  severity: 'severity',
  title: 'title',
  message: 'message',
  dealId: 'dealId',
  complianceStatusId: 'complianceStatusId',
  triggerDate: 'triggerDate',
  triggerEvent: 'triggerEvent',
  recipients: 'recipients',
  notificationChannels: 'notificationChannels',
  status: 'status',
  acknowledgedBy: 'acknowledgedBy',
  acknowledgedDate: 'acknowledgedDate',
  resolvedDate: 'resolvedDate',
  suggestedActions: 'suggestedActions',
  escalationRules: 'escalationRules',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceReportScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  dealId: 'dealId',
  frameworkIds: 'frameworkIds',
  dateRange: 'dateRange',
  includeCompleted: 'includeCompleted',
  includeInProgress: 'includeInProgress',
  includeOverdue: 'includeOverdue',
  groupBy: 'groupBy',
  generatedDate: 'generatedDate',
  reportUrl: 'reportUrl',
  format: 'format',
  isScheduled: 'isScheduled',
  schedule: 'schedule',
  visibility: 'visibility',
  allowedUsers: 'allowedUsers',
  createdBy: 'createdBy',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceDocumentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  description: 'description',
  fileUrl: 'fileUrl',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  version: 'version',
  status: 'status',
  complianceStatusId: 'complianceStatusId',
  submittedBy: 'submittedBy',
  submittedDate: 'submittedDate',
  reviewedBy: 'reviewedBy',
  reviewedDate: 'reviewedDate',
  approvedBy: 'approvedBy',
  approvedDate: 'approvedDate',
  rejectionReason: 'rejectionReason',
  metadata: 'metadata',
  retentionDate: 'retentionDate',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceAuditLogScalarFieldEnum = {
  id: 'id',
  dealId: 'dealId',
  entityType: 'entityType',
  entityId: 'entityId',
  action: 'action',
  oldValues: 'oldValues',
  newValues: 'newValues',
  userId: 'userId',
  userEmail: 'userEmail',
  timestamp: 'timestamp',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  tenantId: 'tenantId'
};

exports.Prisma.ValuationTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  methodology: 'methodology',
  configuration: 'configuration',
  isPublic: 'isPublic',
  createdBy: 'createdBy',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ScenarioModelScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  name: 'name',
  description: 'description',
  assumptions: 'assumptions',
  results: 'results',
  createdBy: 'createdBy',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ScenarioDefinitionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  templateId: 'templateId',
  baseAssumptions: 'baseAssumptions',
  scenarios: 'scenarios',
  sensitivityConfig: 'sensitivityConfig',
  monteCarloConfig: 'monteCarloConfig',
  createdBy: 'createdBy',
  tenantId: 'tenantId',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ExternalDataSourceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  apiEndpoint: 'apiEndpoint',
  apiKey: 'apiKey',
  isActive: 'isActive',
  rateLimits: 'rateLimits',
  supportedDataTypes: 'supportedDataTypes',
  configuration: 'configuration',
  tenantId: 'tenantId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyFinancialsScalarFieldEnum = {
  id: 'id',
  symbol: 'symbol',
  companyName: 'companyName',
  period: 'period',
  periodType: 'periodType',
  currency: 'currency',
  financialData: 'financialData',
  ratios: 'ratios',
  marketData: 'marketData',
  lastUpdated: 'lastUpdated',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MarketDataScalarFieldEnum = {
  id: 'id',
  symbol: 'symbol',
  price: 'price',
  change: 'change',
  changePercent: 'changePercent',
  volume: 'volume',
  marketCap: 'marketCap',
  pe: 'pe',
  eps: 'eps',
  high52Week: 'high52Week',
  low52Week: 'low52Week',
  dividendYield: 'dividendYield',
  beta: 'beta',
  lastUpdated: 'lastUpdated',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EconomicIndicatorScalarFieldEnum = {
  id: 'id',
  indicator: 'indicator',
  value: 'value',
  period: 'period',
  frequency: 'frequency',
  unit: 'unit',
  lastUpdated: 'lastUpdated',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DataSyncLogScalarFieldEnum = {
  id: 'id',
  dataSourceId: 'dataSourceId',
  dataType: 'dataType',
  recordsProcessed: 'recordsProcessed',
  recordsUpdated: 'recordsUpdated',
  recordsCreated: 'recordsCreated',
  errors: 'errors',
  syncDuration: 'syncDuration',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.TenantSize = exports.$Enums.TenantSize = {
  SMALL: 'SMALL',
  MEDIUM: 'MEDIUM',
  LARGE: 'LARGE',
  ENTERPRISE: 'ENTERPRISE'
};

exports.TenantStatus = exports.$Enums.TenantStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  TRIAL: 'TRIAL',
  EXPIRED: 'EXPIRED',
  DELETED: 'DELETED'
};

exports.TenantPlan = exports.$Enums.TenantPlan = {
  STARTER: 'STARTER',
  PROFESSIONAL: 'PROFESSIONAL',
  ENTERPRISE: 'ENTERPRISE',
  CUSTOM: 'CUSTOM'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED'
};

exports.DealStatus = exports.$Enums.DealStatus = {
  PIPELINE: 'PIPELINE',
  DUE_DILIGENCE: 'DUE_DILIGENCE',
  NEGOTIATION: 'NEGOTIATION',
  CLOSING: 'CLOSING',
  CLOSED: 'CLOSED',
  CANCELLED: 'CANCELLED'
};

exports.DealPriority = exports.$Enums.DealPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.DealType = exports.$Enums.DealType = {
  ACQUISITION: 'ACQUISITION',
  MERGER: 'MERGER',
  ASSET_PURCHASE: 'ASSET_PURCHASE',
  STOCK_PURCHASE: 'STOCK_PURCHASE',
  JOINT_VENTURE: 'JOINT_VENTURE',
  STRATEGIC_PARTNERSHIP: 'STRATEGIC_PARTNERSHIP',
  DIVESTITURE: 'DIVESTITURE',
  SPIN_OFF: 'SPIN_OFF',
  CARVE_OUT: 'CARVE_OUT',
  RECAPITALIZATION: 'RECAPITALIZATION'
};

exports.DealSource = exports.$Enums.DealSource = {
  DIRECT: 'DIRECT',
  INVESTMENT_BANK: 'INVESTMENT_BANK',
  BROKER: 'BROKER',
  REFERRAL: 'REFERRAL',
  COLD_OUTREACH: 'COLD_OUTREACH',
  INBOUND: 'INBOUND',
  CONFERENCE: 'CONFERENCE',
  NETWORK: 'NETWORK',
  EXISTING_RELATIONSHIP: 'EXISTING_RELATIONSHIP',
  AUCTION: 'AUCTION'
};

exports.ForecastCategory = exports.$Enums.ForecastCategory = {
  PIPELINE: 'PIPELINE',
  BEST_CASE: 'BEST_CASE',
  COMMIT: 'COMMIT',
  CLOSED: 'CLOSED'
};

exports.RiskLevel = exports.$Enums.RiskLevel = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.TargetCompanyStatus = exports.$Enums.TargetCompanyStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  CONTACTED: 'CONTACTED',
  IN_DISCUSSION: 'IN_DISCUSSION',
  QUALIFIED: 'QUALIFIED',
  DISQUALIFIED: 'DISQUALIFIED',
  ACQUIRED: 'ACQUIRED'
};

exports.InteractionType = exports.$Enums.InteractionType = {
  EMAIL: 'EMAIL',
  PHONE_CALL: 'PHONE_CALL',
  MEETING: 'MEETING',
  CONFERENCE: 'CONFERENCE',
  RESEARCH: 'RESEARCH',
  NOTE: 'NOTE',
  FOLLOW_UP: 'FOLLOW_UP'
};

exports.VDRAccessLevel = exports.$Enums.VDRAccessLevel = {
  PUBLIC: 'PUBLIC',
  INTERNAL: 'INTERNAL',
  RESTRICTED: 'RESTRICTED',
  CONFIDENTIAL: 'CONFIDENTIAL',
  ADMIN_ONLY: 'ADMIN_ONLY'
};

exports.DocumentStatus = exports.$Enums.DocumentStatus = {
  ACTIVE: 'ACTIVE',
  ARCHIVED: 'ARCHIVED',
  DELETED: 'DELETED'
};

exports.VDRRole = exports.$Enums.VDRRole = {
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  CONTRIBUTOR: 'CONTRIBUTOR',
  VIEWER: 'VIEWER'
};

exports.VDRUserStatus = exports.$Enums.VDRUserStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  SUSPENDED: 'SUSPENDED',
  EXPIRED: 'EXPIRED',
  REVOKED: 'REVOKED'
};

exports.VDRActivityType = exports.$Enums.VDRActivityType = {
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  VIEW_DOCUMENT: 'VIEW_DOCUMENT',
  DOWNLOAD_DOCUMENT: 'DOWNLOAD_DOCUMENT',
  UPLOAD_DOCUMENT: 'UPLOAD_DOCUMENT',
  DELETE_DOCUMENT: 'DELETE_DOCUMENT',
  CREATE_FOLDER: 'CREATE_FOLDER',
  DELETE_FOLDER: 'DELETE_FOLDER',
  SHARE_DOCUMENT: 'SHARE_DOCUMENT',
  COMMENT_DOCUMENT: 'COMMENT_DOCUMENT',
  SEARCH: 'SEARCH',
  EXPORT_DATA: 'EXPORT_DATA',
  PRINT_DOCUMENT: 'PRINT_DOCUMENT',
  COPY_DOCUMENT: 'COPY_DOCUMENT'
};

exports.DDItemStatus = exports.$Enums.DDItemStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  BLOCKED: 'BLOCKED',
  NOT_APPLICABLE: 'NOT_APPLICABLE'
};

exports.ValuationType = exports.$Enums.ValuationType = {
  DCF: 'DCF',
  COMPARABLE_COMPANY: 'COMPARABLE_COMPANY',
  PRECEDENT_TRANSACTION: 'PRECEDENT_TRANSACTION',
  ASSET_BASED: 'ASSET_BASED',
  SUM_OF_PARTS: 'SUM_OF_PARTS'
};

exports.InvitationStatus = exports.$Enums.InvitationStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED'
};

exports.ActivityType = exports.$Enums.ActivityType = {
  CALL: 'CALL',
  EMAIL: 'EMAIL',
  MEETING: 'MEETING',
  PRESENTATION: 'PRESENTATION',
  SITE_VISIT: 'SITE_VISIT',
  DUE_DILIGENCE: 'DUE_DILIGENCE',
  NEGOTIATION: 'NEGOTIATION',
  DOCUMENT_REVIEW: 'DOCUMENT_REVIEW',
  VALUATION: 'VALUATION',
  LEGAL_REVIEW: 'LEGAL_REVIEW',
  REGULATORY: 'REGULATORY',
  CLOSING: 'CLOSING',
  OTHER: 'OTHER'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  OVERDUE: 'OVERDUE'
};

exports.TaskPriority = exports.$Enums.TaskPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.MilestoneStatus = exports.$Enums.MilestoneStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  MISSED: 'MISSED',
  CANCELLED: 'CANCELLED'
};

exports.DataSourceType = exports.$Enums.DataSourceType = {
  FINANCIAL_DATA: 'FINANCIAL_DATA',
  MARKET_DATA: 'MARKET_DATA',
  ECONOMIC_DATA: 'ECONOMIC_DATA',
  COMPANY_DATA: 'COMPANY_DATA',
  TRANSACTION_DATA: 'TRANSACTION_DATA'
};

exports.Prisma.ModelName = {
  Tenant: 'Tenant',
  User: 'User',
  Role: 'Role',
  UserRole: 'UserRole',
  Session: 'Session',
  Deal: 'Deal',
  TargetCompany: 'TargetCompany',
  TargetCompanyInteraction: 'TargetCompanyInteraction',
  TargetCompanyWatchlist: 'TargetCompanyWatchlist',
  VirtualDataRoom: 'VirtualDataRoom',
  VDRFolder: 'VDRFolder',
  VDRDocument: 'VDRDocument',
  VDRUser: 'VDRUser',
  VDRPermission: 'VDRPermission',
  VDRActivity: 'VDRActivity',
  DueDiligenceItem: 'DueDiligenceItem',
  Valuation: 'Valuation',
  UserMfa: 'UserMfa',
  AuditLog: 'AuditLog',
  TenantInvitation: 'TenantInvitation',
  TenantApiKey: 'TenantApiKey',
  MigrationRecord: 'MigrationRecord',
  PermissionAudit: 'PermissionAudit',
  RoleAssignmentRule: 'RoleAssignmentRule',
  PermissionTemplate: 'PermissionTemplate',
  Policy: 'Policy',
  PolicySet: 'PolicySet',
  PolicyAuditLog: 'PolicyAuditLog',
  DealStage: 'DealStage',
  DealStageHistory: 'DealStageHistory',
  DealActivity: 'DealActivity',
  DealContact: 'DealContact',
  DealTeamMember: 'DealTeamMember',
  DealTask: 'DealTask',
  DealNote: 'DealNote',
  DealMilestone: 'DealMilestone',
  DealPipeline: 'DealPipeline',
  ComplianceFramework: 'ComplianceFramework',
  ComplianceStatus: 'ComplianceStatus',
  ComplianceAlert: 'ComplianceAlert',
  ComplianceReport: 'ComplianceReport',
  ComplianceDocument: 'ComplianceDocument',
  ComplianceAuditLog: 'ComplianceAuditLog',
  ValuationTemplate: 'ValuationTemplate',
  ScenarioModel: 'ScenarioModel',
  ScenarioDefinition: 'ScenarioDefinition',
  ExternalDataSource: 'ExternalDataSource',
  CompanyFinancials: 'CompanyFinancials',
  MarketData: 'MarketData',
  EconomicIndicator: 'EconomicIndicator',
  DataSyncLog: 'DataSyncLog'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
