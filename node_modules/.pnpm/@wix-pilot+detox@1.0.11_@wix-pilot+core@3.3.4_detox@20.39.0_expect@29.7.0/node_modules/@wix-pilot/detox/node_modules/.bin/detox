#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules/detox/local-cli/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules/detox/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules/detox/local-cli/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules/detox/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules/detox/local-cli/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../detox@20.39.0_expect@29.7.0_jest@29.7.0/node_modules/detox/local-cli/cli.js" "$@"
fi
