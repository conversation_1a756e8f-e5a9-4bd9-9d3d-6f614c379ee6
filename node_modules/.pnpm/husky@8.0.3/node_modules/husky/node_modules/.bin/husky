#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/husky@8.0.3/node_modules/husky/lib/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/husky@8.0.3/node_modules/husky/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/husky@8.0.3/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/husky@8.0.3/node_modules/husky/lib/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/husky@8.0.3/node_modules/husky/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/husky@8.0.3/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../lib/bin.js" "$@"
else
  exec node  "$basedir/../../lib/bin.js" "$@"
fi
