#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/prisma@6.10.1_typescript@5.8.3/node_modules/prisma/build/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/prisma@6.10.1_typescript@5.8.3/node_modules/prisma/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/prisma@6.10.1_typescript@5.8.3/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/prisma@6.10.1_typescript@5.8.3/node_modules/prisma/build/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/prisma@6.10.1_typescript@5.8.3/node_modules/prisma/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/prisma@6.10.1_typescript@5.8.3/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../build/index.js" "$@"
else
  exec node  "$basedir/../../build/index.js" "$@"
fi
