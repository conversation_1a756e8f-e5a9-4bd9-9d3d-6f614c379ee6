#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules/@react-native-community/cli/build/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules/@react-native-community/cli/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules/@react-native-community/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules/@react-native-community/cli/build/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules/@react-native-community/cli/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules/@react-native-community/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/@react-native-community+cli@11.3.5_@babel+core@7.27.7/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../build/bin.js" "$@"
else
  exec node  "$basedir/../../build/bin.js" "$@"
fi
