import { CacheService } from '@/services/cache.service'
import { FinancialCalculationEngine } from '@/services/financial/calculation-engine.service'
import { ValuationTemplateService } from '@/services/financial/valuation-template.service'
import { Logger } from '@/shared/utils/logger'
import { PrismaClient } from '@prisma/client'
import { Request, Response } from 'express'

export class FinancialModelingController {
  private calculationEngine: FinancialCalculationEngine
  private templateService: ValuationTemplateService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.calculationEngine = new FinancialCalculationEngine(prisma, cache)
    this.templateService = new ValuationTemplateService(prisma, cache)
    this.logger = new Logger('FinancialModelingController')
  }

  /**
   * Calculate DCF valuation
   */
  calculateDCF = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const dcfInputs = req.body

      if (!dcfInputs.baseRevenue || !dcfInputs.revenueGrowth || !dcfInputs.discountRate) {
        res.status(400).json({
          error: 'Missing required DCF inputs: baseRevenue, revenueGrowth, discountRate'
        })
        return
      }

      const result = this.calculationEngine.calculateDCF(dcfInputs)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      this.logger.error('Failed to calculate DCF', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate DCF',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Calculate Comparable Company Analysis
   */
  calculateCCA = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const ccaInputs = req.body

      if (!ccaInputs.targetMetrics || !ccaInputs.comparableCompanies) {
        res.status(400).json({
          error: 'Missing required CCA inputs: targetMetrics, comparableCompanies'
        })
        return
      }

      const result = this.calculationEngine.calculateCCA(ccaInputs)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      this.logger.error('Failed to calculate CCA', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate CCA',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Calculate Precedent Transaction Analysis
   */
  calculatePTA = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const ptaInputs = req.body

      if (!ptaInputs.targetMetrics || !ptaInputs.precedentTransactions) {
        res.status(400).json({
          error: 'Missing required PTA inputs: targetMetrics, precedentTransactions'
        })
        return
      }

      const result = this.calculationEngine.calculatePTA(ptaInputs)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      this.logger.error('Failed to calculate PTA', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate PTA',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Calculate LBO Analysis
   */
  calculateLBO = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const lboInputs = req.body

      if (!lboInputs.targetMetrics || !lboInputs.dealStructure || !lboInputs.assumptions) {
        res.status(400).json({
          error: 'Missing required LBO inputs: targetMetrics, dealStructure, assumptions'
        })
        return
      }

      const result = this.calculationEngine.calculateLBO(lboInputs)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      this.logger.error('Failed to calculate LBO', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate LBO',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Perform sensitivity analysis
   */
  performSensitivityAnalysis = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { baseInputs, variables } = req.body

      if (!baseInputs || !variables) {
        res.status(400).json({
          error: 'Missing required inputs: baseInputs, variables'
        })
        return
      }

      const result = this.calculationEngine.performSensitivityAnalysis(baseInputs, variables)

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      this.logger.error('Failed to perform sensitivity analysis', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to perform sensitivity analysis',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get valuation templates
   */
  getValuationTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { category, methodology } = req.query

      const templates = await this.templateService.getTemplates(
        tenantId,
        category as any,
        methodology as any
      )

      res.json({
        success: true,
        data: templates
      })
    } catch (error) {
      this.logger.error('Failed to get valuation templates', error, { query: req.query })
      res.status(500).json({
        error: 'Failed to get valuation templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get default templates
   */
  getDefaultTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const templates = await this.templateService.getDefaultTemplates()

      res.json({
        success: true,
        data: templates
      })
    } catch (error) {
      this.logger.error('Failed to get default templates', error)
      res.status(500).json({
        error: 'Failed to get default templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Create valuation template
   */
  createValuationTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const templateData = {
        ...req.body,
        tenantId
      }

      if (!templateData.name || !templateData.category || !templateData.configuration) {
        res.status(400).json({
          error: 'Missing required fields: name, category, configuration'
        })
        return
      }

      const template = await this.templateService.createTemplate(templateData, userId)

      res.status(201).json({
        success: true,
        data: template,
        message: 'Valuation template created successfully'
      })
    } catch (error) {
      this.logger.error('Failed to create valuation template', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to create valuation template',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Create scenario model
   */
  createScenarioModel = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const { templateId, name, assumptions } = req.body

      if (!templateId || !name || !assumptions) {
        res.status(400).json({
          error: 'Missing required fields: templateId, name, assumptions'
        })
        return
      }

      const scenarioModel = await this.templateService.createScenarioModel(
        templateId,
        name,
        assumptions,
        userId,
        tenantId
      )

      res.status(201).json({
        success: true,
        data: scenarioModel,
        message: 'Scenario model created successfully'
      })
    } catch (error) {
      this.logger.error('Failed to create scenario model', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to create scenario model',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Calculate financial ratios
   */
  calculateFinancialRatios = async (req: Request, res: Response): Promise<void> => {
    try {
      const { financialData } = req.body

      if (!financialData) {
        res.status(400).json({
          error: 'Missing required input: financialData'
        })
        return
      }

      // Calculate various financial ratios
      const ratios = {
        profitability: {
          grossMargin: financialData.grossProfit / financialData.revenue,
          operatingMargin: financialData.operatingIncome / financialData.revenue,
          netMargin: financialData.netIncome / financialData.revenue,
          roa: financialData.netIncome / financialData.totalAssets,
          roe: financialData.netIncome / financialData.shareholderEquity
        },
        liquidity: {
          currentRatio: financialData.currentAssets / financialData.currentLiabilities,
          quickRatio: (financialData.currentAssets - financialData.inventory) / financialData.currentLiabilities,
          cashRatio: financialData.cash / financialData.currentLiabilities
        },
        leverage: {
          debtToEquity: financialData.totalDebt / financialData.shareholderEquity,
          debtToAssets: financialData.totalDebt / financialData.totalAssets,
          interestCoverage: financialData.ebit / financialData.interestExpense
        },
        efficiency: {
          assetTurnover: financialData.revenue / financialData.totalAssets,
          inventoryTurnover: financialData.cogs / financialData.inventory,
          receivablesTurnover: financialData.revenue / financialData.accountsReceivable
        }
      }

      res.json({
        success: true,
        data: ratios
      })
    } catch (error) {
      this.logger.error('Failed to calculate financial ratios', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate financial ratios',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Calculate WACC
   */
  calculateWACC = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        marketValueEquity,
        marketValueDebt,
        costOfEquity,
        costOfDebt,
        taxRate
      } = req.body

      if (!marketValueEquity || !marketValueDebt || !costOfEquity || !costOfDebt || taxRate === undefined) {
        res.status(400).json({
          error: 'Missing required WACC inputs'
        })
        return
      }

      const wacc = this.calculationEngine.calculateWACC(
        marketValueEquity,
        marketValueDebt,
        costOfEquity,
        costOfDebt,
        taxRate
      )

      res.json({
        success: true,
        data: {
          wacc,
          components: {
            equityWeight: marketValueEquity / (marketValueEquity + marketValueDebt),
            debtWeight: marketValueDebt / (marketValueEquity + marketValueDebt),
            afterTaxCostOfDebt: costOfDebt * (1 - taxRate)
          }
        }
      })
    } catch (error) {
      this.logger.error('Failed to calculate WACC', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate WACC',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Calculate beta coefficient
   */
  calculateBeta = async (req: Request, res: Response): Promise<void> => {
    try {
      const { stockReturns, marketReturns } = req.body

      if (!stockReturns || !marketReturns) {
        res.status(400).json({
          error: 'Missing required inputs: stockReturns, marketReturns'
        })
        return
      }

      const beta = this.calculationEngine.calculateBeta(stockReturns, marketReturns)

      res.json({
        success: true,
        data: {
          beta,
          interpretation: beta > 1 ? 'More volatile than market' : 
                        beta < 1 ? 'Less volatile than market' : 
                        'Same volatility as market'
        }
      })
    } catch (error) {
      this.logger.error('Failed to calculate beta', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to calculate beta',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

export default FinancialModelingController
