import { Request, Response } from 'express'
import { PrismaClient, TargetCompanyStatus, InteractionType } from '@prisma/client'
import { 
  TargetCompanyService,
  CreateTargetCompanyRequest,
  UpdateTargetCompanyRequest,
  TargetCompanyFilters,
  TargetCompanySortOptions,
  CreateInteractionRequest
} from '@/services/target-company/target-company.service'
import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'

export class TargetCompanyController {
  private targetCompanyService: TargetCompanyService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.targetCompanyService = new TargetCompanyService(prisma, cache)
    this.logger = new Logger('TargetCompanyController')
  }

  /**
   * Create a new target company
   */
  createTargetCompany = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const data: CreateTargetCompanyRequest = req.body

      // Validate required fields
      if (!data.name) {
        res.status(400).json({
          error: 'Missing required field: name'
        })
        return
      }

      const targetCompany = await this.targetCompanyService.createTargetCompany(
        data, 
        userId, 
        tenantId
      )

      res.status(201).json({
        success: true,
        data: targetCompany,
        message: 'Target company created successfully'
      })
    } catch (error) {
      this.logger.error('Failed to create target company', error, { body: req.body })
      res.status(500).json({
        error: 'Failed to create target company',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get target companies with filtering and pagination
   */
  getTargetCompanies = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const {
        status,
        industry,
        country,
        minRevenue,
        maxRevenue,
        minEmployees,
        maxEmployees,
        minOpportunityScore,
        tags,
        search,
        sortField = 'createdAt',
        sortDirection = 'desc',
        page = '1',
        limit = '20'
      } = req.query

      const filters: TargetCompanyFilters = {
        ...(status && { status: Array.isArray(status) ? status as TargetCompanyStatus[] : [status as TargetCompanyStatus] }),
        ...(industry && { industry: Array.isArray(industry) ? industry as string[] : [industry as string] }),
        ...(country && { country: Array.isArray(country) ? country as string[] : [country as string] }),
        ...(minRevenue && { minRevenue: Number(minRevenue) }),
        ...(maxRevenue && { maxRevenue: Number(maxRevenue) }),
        ...(minEmployees && { minEmployees: Number(minEmployees) }),
        ...(maxEmployees && { maxEmployees: Number(maxEmployees) }),
        ...(minOpportunityScore && { minOpportunityScore: Number(minOpportunityScore) }),
        ...(tags && { tags: Array.isArray(tags) ? tags as string[] : [tags as string] }),
        ...(search && { search: search as string })
      }

      const sort: TargetCompanySortOptions = {
        field: sortField as any,
        direction: sortDirection as 'asc' | 'desc'
      }

      const result = await this.targetCompanyService.getTargetCompanies(
        tenantId,
        filters,
        sort,
        Number(page),
        Number(limit)
      )

      res.json({
        success: true,
        data: result.companies,
        pagination: result.pagination
      })
    } catch (error) {
      this.logger.error('Failed to get target companies', error, { query: req.query })
      res.status(500).json({
        error: 'Failed to get target companies',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get target company by ID
   */
  getTargetCompanyById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { id } = req.params

      const targetCompany = await this.targetCompanyService.getTargetCompanyById(
        id, 
        tenantId
      )

      res.json({
        success: true,
        data: targetCompany
      })
    } catch (error) {
      this.logger.error('Failed to get target company', error, { id: req.params.id })
      
      if (error instanceof Error && error.message === 'Target company not found') {
        res.status(404).json({
          error: 'Target company not found'
        })
      } else {
        res.status(500).json({
          error: 'Failed to get target company',
          details: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  }

  /**
   * Update target company
   */
  updateTargetCompany = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { id } = req.params
      const data: UpdateTargetCompanyRequest = req.body

      const targetCompany = await this.targetCompanyService.updateTargetCompany(
        id, 
        data, 
        tenantId
      )

      res.json({
        success: true,
        data: targetCompany,
        message: 'Target company updated successfully'
      })
    } catch (error) {
      this.logger.error('Failed to update target company', error, { 
        id: req.params.id, 
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to update target company',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Delete target company
   */
  deleteTargetCompany = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { id } = req.params

      await this.targetCompanyService.deleteTargetCompany(id, tenantId)

      res.json({
        success: true,
        message: 'Target company deleted successfully'
      })
    } catch (error) {
      this.logger.error('Failed to delete target company', error, { id: req.params.id })
      res.status(500).json({
        error: 'Failed to delete target company',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Create interaction with target company
   */
  createInteraction = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const data: CreateInteractionRequest = req.body

      // Validate required fields
      if (!data.targetId || !data.type) {
        res.status(400).json({
          error: 'Missing required fields: targetId, type'
        })
        return
      }

      const interaction = await this.targetCompanyService.createInteraction(
        data, 
        userId, 
        tenantId
      )

      res.status(201).json({
        success: true,
        data: interaction,
        message: 'Interaction created successfully'
      })
    } catch (error) {
      this.logger.error('Failed to create interaction', error, { body: req.body })
      
      if (error instanceof Error && error.message === 'Target company not found') {
        res.status(404).json({
          error: 'Target company not found'
        })
      } else {
        res.status(500).json({
          error: 'Failed to create interaction',
          details: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  }

  /**
   * Update opportunity scoring
   */
  updateScoring = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { id } = req.params
      const { opportunityScore, strategicFit, financialHealth } = req.body

      // Validate scores are within range
      const scores = { opportunityScore, strategicFit, financialHealth }
      for (const [key, value] of Object.entries(scores)) {
        if (value !== undefined && (value < 0 || value > 100)) {
          res.status(400).json({
            error: `${key} must be between 0 and 100`
          })
          return
        }
      }

      const targetCompany = await this.targetCompanyService.updateScoring(
        id, 
        scores, 
        tenantId
      )

      res.json({
        success: true,
        data: targetCompany,
        message: 'Scoring updated successfully'
      })
    } catch (error) {
      this.logger.error('Failed to update scoring', error, { 
        id: req.params.id, 
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to update scoring',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Search target companies
   */
  searchTargetCompanies = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { q: query, limit = '10', ...filterParams } = req.query

      if (!query) {
        res.status(400).json({
          error: 'Missing required parameter: q (query)'
        })
        return
      }

      const filters: TargetCompanyFilters = {
        ...(filterParams.status && { status: Array.isArray(filterParams.status) ? filterParams.status as TargetCompanyStatus[] : [filterParams.status as TargetCompanyStatus] }),
        ...(filterParams.industry && { industry: Array.isArray(filterParams.industry) ? filterParams.industry as string[] : [filterParams.industry as string] }),
        ...(filterParams.country && { country: Array.isArray(filterParams.country) ? filterParams.country as string[] : [filterParams.country as string] }),
        ...(filterParams.tags && { tags: Array.isArray(filterParams.tags) ? filterParams.tags as string[] : [filterParams.tags as string] })
      }

      const companies = await this.targetCompanyService.searchTargetCompanies(
        tenantId,
        query as string,
        filters,
        Number(limit)
      )

      res.json({
        success: true,
        data: companies
      })
    } catch (error) {
      this.logger.error('Failed to search target companies', error, { query: req.query })
      res.status(500).json({
        error: 'Failed to search target companies',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}
