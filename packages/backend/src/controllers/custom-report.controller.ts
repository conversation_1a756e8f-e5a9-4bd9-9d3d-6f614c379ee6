import { Request, Response } from 'express'
import { Logger } from '@/shared/utils/logger'
import { CustomReportBuilderService } from '@/services/reporting/custom-report-builder.service'
import { PrismaClient } from '@prisma/client'
import { CacheService } from '@/services/cache.service'

export class CustomReportController {
  private customReportService: CustomReportBuilderService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.customReportService = new CustomReportBuilderService(prisma, cache)
    this.logger = new Logger('CustomReportController')
  }

  /**
   * Get available report templates
   */
  getReportTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const templates = await this.customReportService.getReportTemplates()

      res.json({
        success: true,
        data: templates
      })
    } catch (error) {
      this.logger.error('Failed to get report templates', error)
      res.status(500).json({
        error: 'Failed to get report templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Create custom report definition
   */
  createReportDefinition = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const reportData = {
        ...req.body,
        tenantId,
        createdBy: userId
      }

      if (!reportData.name) {
        res.status(400).json({
          error: 'Missing required field: name'
        })
        return
      }

      if (!reportData.configuration) {
        res.status(400).json({
          error: 'Missing required field: configuration'
        })
        return
      }

      const reportDefinition = await this.customReportService.createReportDefinition(
        reportData,
        userId
      )

      res.status(201).json({
        success: true,
        data: reportDefinition,
        message: 'Custom report definition created successfully'
      })
    } catch (error) {
      this.logger.error('Failed to create report definition', error, { 
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to create report definition',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Execute custom report
   */
  executeReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { reportId } = req.params
      const parameters = req.body

      const result = await this.customReportService.executeReport(
        reportId,
        tenantId,
        parameters
      )

      res.json({
        success: true,
        data: result
      })
    } catch (error) {
      this.logger.error('Failed to execute custom report', error, { 
        reportId: req.params.reportId,
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to execute custom report',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Execute report template
   */
  executeTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId, userId } = req.user as any
      const { templateId } = req.params
      const parameters = req.body

      // Get template configuration
      const templates = await this.customReportService.getReportTemplates()
      const template = templates.find(t => t.id === templateId)

      if (!template) {
        res.status(404).json({
          error: 'Report template not found'
        })
        return
      }

      // Create temporary report definition
      const tempReportDef = await this.customReportService.createReportDefinition({
        name: `${template.name} - ${new Date().toISOString()}`,
        description: `Executed from template: ${template.name}`,
        tenantId,
        isPublic: false,
        configuration: template.configuration
      }, userId)

      // Execute the report
      const result = await this.customReportService.executeReport(
        tempReportDef.id,
        tenantId,
        parameters
      )

      res.json({
        success: true,
        data: {
          ...result,
          template: {
            id: template.id,
            name: template.name,
            category: template.category
          }
        }
      })
    } catch (error) {
      this.logger.error('Failed to execute report template', error, { 
        templateId: req.params.templateId,
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to execute report template',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Get report execution history
   */
  getReportHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { page = 1, limit = 20 } = req.query

      // This would typically fetch from a report execution history table
      // For now, return mock data
      const history = {
        reports: [],
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: 0,
          pages: 0
        }
      }

      res.json({
        success: true,
        data: history
      })
    } catch (error) {
      this.logger.error('Failed to get report history', error, { 
        query: req.query 
      })
      res.status(500).json({
        error: 'Failed to get report history',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Export report to various formats
   */
  exportReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { tenantId } = req.user as any
      const { reportId } = req.params
      const { format = 'csv', parameters } = req.body

      if (!['csv', 'xlsx', 'pdf'].includes(format)) {
        res.status(400).json({
          error: 'Invalid export format. Supported formats: csv, xlsx, pdf'
        })
        return
      }

      // Execute the report
      const result = await this.customReportService.executeReport(
        reportId,
        tenantId,
        parameters
      )

      // Generate export based on format
      const exportData = await this.generateExport(result, format)

      // Set appropriate headers
      const filename = `report-${reportId}-${Date.now()}.${format}`
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
      
      switch (format) {
        case 'csv':
          res.setHeader('Content-Type', 'text/csv')
          break
        case 'xlsx':
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
          break
        case 'pdf':
          res.setHeader('Content-Type', 'application/pdf')
          break
      }

      res.send(exportData)
    } catch (error) {
      this.logger.error('Failed to export report', error, { 
        reportId: req.params.reportId,
        body: req.body 
      })
      res.status(500).json({
        error: 'Failed to export report',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Generate export data in specified format
   */
  private async generateExport(result: any, format: string): Promise<Buffer | string> {
    switch (format) {
      case 'csv':
        return this.generateCSV(result.data)
      case 'xlsx':
        return this.generateXLSX(result.data)
      case 'pdf':
        return this.generatePDF(result.data)
      default:
        throw new Error(`Unsupported export format: ${format}`)
    }
  }

  /**
   * Generate CSV export
   */
  private generateCSV(data: any[]): string {
    if (data.length === 0) return ''

    const headers = Object.keys(data[0])
    const csvRows = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header]
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )
    ]

    return csvRows.join('\n')
  }

  /**
   * Generate XLSX export (simplified)
   */
  private generateXLSX(data: any[]): Buffer {
    // In a real implementation, you'd use a library like 'xlsx' or 'exceljs'
    // For now, return CSV data as buffer
    const csvData = this.generateCSV(data)
    return Buffer.from(csvData, 'utf-8')
  }

  /**
   * Generate PDF export (simplified)
   */
  private generatePDF(data: any[]): Buffer {
    // In a real implementation, you'd use a library like 'pdfkit' or 'puppeteer'
    // For now, return a simple text representation
    const textData = JSON.stringify(data, null, 2)
    return Buffer.from(textData, 'utf-8')
  }
}
