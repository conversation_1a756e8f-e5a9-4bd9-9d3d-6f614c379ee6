import { CacheService } from '@/services/cache.service'
import { PrismaClient } from '@prisma/client'
import { Router } from 'express'
import { authRoutes } from './auth.routes'
import { mfaRoutes } from './mfa.routes'
import { migrationRoutes } from './migration.routes'
import { sessionRoutes } from './session.routes'
import { ssoRoutes } from './sso.routes'
import { tenantConfigRoutes } from './tenant-config.routes'
import { tenantRoutes } from './tenant.routes'

// Initialize services
const prisma = new PrismaClient()
const cache = new CacheService()

const router = Router()

// API version
router.get('/', (req, res) => {
  res.json({
    message: 'M&A Enterprise Platform API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  })
})

// Health check
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
  })
})

// Authentication routes
router.use('/auth', authRoutes)

// SSO routes
router.use('/sso', ssoRoutes)

// MFA routes
router.use('/mfa', mfaRoutes)

// Session routes
router.use('/sessions', sessionRoutes)

// Tenant routes
router.use('/tenants', tenantRoutes)

// Tenant configuration routes
router.use('/tenant-config', tenantConfigRoutes)

// Migration routes
router.use('/migrations', migrationRoutes)

// Tenant provisioning routes
router.use('/tenant-provisioning', tenantProvisioningRoutes)

// TODO: Add other route modules
// router.use('/users', userRoutes)
// router.use('/tenants', tenantRoutes)
// router.use('/deals', dealRoutes)
// router.use('/documents', documentRoutes)

export { router as apiRoutes }
