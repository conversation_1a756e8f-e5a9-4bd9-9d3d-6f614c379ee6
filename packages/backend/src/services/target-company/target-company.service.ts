import { CacheService } from '@/services/cache.service'
import { Logger } from '@/shared/utils/logger'
import { InteractionType, PrismaClient, TargetCompanyStatus } from '@prisma/client'
import { OpportunityPipelineService } from './opportunity-pipeline.service'

export interface CreateTargetCompanyRequest {
  name: string
  description?: string
  website?: string
  industry?: string
  sector?: string
  subSector?: string
  country?: string
  region?: string
  city?: string
  address?: string
  revenue?: number
  ebitda?: number
  employees?: number
  foundedYear?: number
  companyType?: string
  primaryContact?: string
  contactEmail?: string
  contactPhone?: string
  source?: string
  sourceDetails?: string
  tags?: string[]
  notes?: string
  researchNotes?: string
}

export interface UpdateTargetCompanyRequest {
  name?: string
  description?: string
  website?: string
  industry?: string
  sector?: string
  subSector?: string
  country?: string
  region?: string
  city?: string
  address?: string
  revenue?: number
  ebitda?: number
  employees?: number
  foundedYear?: number
  companyType?: string
  status?: TargetCompanyStatus
  primaryContact?: string
  contactEmail?: string
  contactPhone?: string
  opportunityScore?: number
  strategicFit?: number
  financialHealth?: number
  marketCap?: number
  enterpriseValue?: number
  source?: string
  sourceDetails?: string
  lastContactDate?: Date
  nextFollowUp?: Date
  tags?: string[]
  notes?: string
  researchNotes?: string
}

export interface TargetCompanyFilters {
  status?: TargetCompanyStatus[]
  industry?: string[]
  country?: string[]
  minRevenue?: number
  maxRevenue?: number
  minEmployees?: number
  maxEmployees?: number
  minOpportunityScore?: number
  tags?: string[]
  search?: string
}

export interface TargetCompanySortOptions {
  field: 'name' | 'opportunityScore' | 'revenue' | 'employees' | 'createdAt' | 'lastContactDate'
  direction: 'asc' | 'desc'
}

export interface CreateInteractionRequest {
  targetId: string
  type: InteractionType
  subject?: string
  description?: string
  outcome?: string
  scheduledAt?: Date
  followUpRequired?: boolean
  followUpDate?: Date
}

export class TargetCompanyService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger
  private pipelineService: OpportunityPipelineService

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('TargetCompanyService')
    this.pipelineService = new OpportunityPipelineService(prisma, cache)
  }

  /**
   * Create a new target company
   */
  async createTargetCompany(
    data: CreateTargetCompanyRequest,
    userId: string,
    tenantId: string
  ) {
    try {
      this.logger.info('Creating new target company', { name: data.name, userId, tenantId })

      const targetCompany = await this.prisma.targetCompany.create({
        data: {
          ...data,
          revenue: data.revenue ? data.revenue.toString() : undefined,
          ebitda: data.ebitda ? data.ebitda.toString() : undefined,
          tenantId,
          status: TargetCompanyStatus.ACTIVE,
          opportunityScore: 0,
          strategicFit: 0,
          financialHealth: 0,
        },
        include: {
          interactions: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 5
          },
          deals: {
            select: {
              id: true,
              title: true,
              status: true,
              dealValue: true,
              currency: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.del(`target-companies:${tenantId}:*`)

      this.logger.info('Target company created successfully', {
        id: targetCompany.id,
        name: targetCompany.name
      })

      return targetCompany
    } catch (error) {
      this.logger.error('Failed to create target company', { error, data })
      throw error
    }
  }

  /**
   * Get target companies with filtering and pagination
   */
  async getTargetCompanies(
    tenantId: string,
    filters: TargetCompanyFilters = {},
    sort: TargetCompanySortOptions = { field: 'createdAt', direction: 'desc' },
    page: number = 1,
    limit: number = 20
  ) {
    try {
      const cacheKey = `target-companies:${tenantId}:${JSON.stringify({ filters, sort, page, limit })}`
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        return JSON.parse(cached)
      }

      const where: any = {
        tenantId,
        ...(filters.status && { status: { in: filters.status } }),
        ...(filters.industry && { industry: { in: filters.industry } }),
        ...(filters.country && { country: { in: filters.country } }),
        ...(filters.minRevenue && { revenue: { gte: filters.minRevenue.toString() } }),
        ...(filters.maxRevenue && { revenue: { lte: filters.maxRevenue.toString() } }),
        ...(filters.minEmployees && { employees: { gte: filters.minEmployees } }),
        ...(filters.maxEmployees && { employees: { lte: filters.maxEmployees } }),
        ...(filters.minOpportunityScore && { opportunityScore: { gte: filters.minOpportunityScore } }),
        ...(filters.tags && { tags: { hasSome: filters.tags } }),
        ...(filters.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            { description: { contains: filters.search, mode: 'insensitive' } },
            { industry: { contains: filters.search, mode: 'insensitive' } },
            { primaryContact: { contains: filters.search, mode: 'insensitive' } }
          ]
        })
      }

      const [companies, total] = await Promise.all([
        this.prisma.targetCompany.findMany({
          where,
          include: {
            interactions: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true
                  }
                }
              },
              orderBy: { createdAt: 'desc' },
              take: 3
            },
            deals: {
              select: {
                id: true,
                title: true,
                status: true,
                dealValue: true,
                currency: true
              }
            },
            _count: {
              select: {
                interactions: true,
                deals: true
              }
            }
          },
          orderBy: { [sort.field]: sort.direction },
          skip: (page - 1) * limit,
          take: limit
        }),
        this.prisma.targetCompany.count({ where })
      ])

      const result = {
        companies,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }

      // Cache for 5 minutes
      await this.cache.set(cacheKey, JSON.stringify(result), 300)

      return result
    } catch (error) {
      this.logger.error('Failed to get target companies', { error, tenantId, filters })
      throw error
    }
  }

  /**
   * Get target company by ID
   */
  async getTargetCompanyById(id: string, tenantId: string) {
    try {
      const cacheKey = `target-company:${id}`
      const cached = await this.cache.get(cacheKey)
      if (cached) {
        return JSON.parse(cached)
      }

      const company = await this.prisma.targetCompany.findFirst({
        where: { id, tenantId },
        include: {
          interactions: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            },
            orderBy: { createdAt: 'desc' }
          },
          deals: {
            include: {
              assignedUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          },
          watchlists: {
            include: {
              creator: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!company) {
        throw new Error('Target company not found')
      }

      // Cache for 10 minutes
      await this.cache.set(cacheKey, JSON.stringify(company), 600)

      return company
    } catch (error) {
      this.logger.error('Failed to get target company', { error, id, tenantId })
      throw error
    }
  }

  /**
   * Update target company
   */
  async updateTargetCompany(
    id: string,
    data: UpdateTargetCompanyRequest,
    tenantId: string
  ) {
    try {
      this.logger.info('Updating target company', { id, tenantId })

      const company = await this.prisma.targetCompany.update({
        where: { id, tenantId },
        data: {
          ...data,
          revenue: data.revenue ? data.revenue.toString() : undefined,
          ebitda: data.ebitda ? data.ebitda.toString() : undefined,
          marketCap: data.marketCap ? data.marketCap.toString() : undefined,
          enterpriseValue: data.enterpriseValue ? data.enterpriseValue.toString() : undefined,
        },
        include: {
          interactions: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 5
          },
          deals: {
            select: {
              id: true,
              title: true,
              status: true,
              dealValue: true,
              currency: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.del(`target-company:${id}`)
      await this.cache.del(`target-companies:${tenantId}:*`)

      this.logger.info('Target company updated successfully', { id })

      return company
    } catch (error) {
      this.logger.error('Failed to update target company', { error, id, tenantId })
      throw error
    }
  }

  /**
   * Delete target company
   */
  async deleteTargetCompany(id: string, tenantId: string) {
    try {
      this.logger.info('Deleting target company', { id, tenantId })

      await this.prisma.targetCompany.delete({
        where: { id, tenantId }
      })

      // Clear cache
      await this.cache.del(`target-company:${id}`)
      await this.cache.del(`target-companies:${tenantId}:*`)

      this.logger.info('Target company deleted successfully', { id })

      return { success: true }
    } catch (error) {
      this.logger.error('Failed to delete target company', { error, id, tenantId })
      throw error
    }
  }

  /**
   * Create interaction with target company
   */
  async createInteraction(
    data: CreateInteractionRequest,
    userId: string,
    tenantId: string
  ) {
    try {
      this.logger.info('Creating target company interaction', {
        targetId: data.targetId,
        type: data.type,
        userId
      })

      // Verify target company exists and belongs to tenant
      const targetCompany = await this.prisma.targetCompany.findFirst({
        where: { id: data.targetId, tenantId }
      })

      if (!targetCompany) {
        throw new Error('Target company not found')
      }

      const interaction = await this.prisma.targetCompanyInteraction.create({
        data: {
          ...data,
          userId,
          completedAt: data.type !== InteractionType.FOLLOW_UP ? new Date() : undefined
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          target: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Update last contact date on target company
      await this.prisma.targetCompany.update({
        where: { id: data.targetId },
        data: { lastContactDate: new Date() }
      })

      // Clear cache
      await this.cache.del(`target-company:${data.targetId}`)
      await this.cache.del(`target-companies:${tenantId}:*`)

      this.logger.info('Interaction created successfully', {
        id: interaction.id,
        targetId: data.targetId
      })

      return interaction
    } catch (error) {
      this.logger.error('Failed to create interaction', { error, data, userId })
      throw error
    }
  }

  /**
   * Update opportunity scoring
   */
  async updateScoring(
    id: string,
    scores: {
      opportunityScore?: number
      strategicFit?: number
      financialHealth?: number
    },
    tenantId: string
  ) {
    try {
      this.logger.info('Updating target company scoring', { id, scores, tenantId })

      const company = await this.prisma.targetCompany.update({
        where: { id, tenantId },
        data: scores
      })

      // Clear cache
      await this.cache.del(`target-company:${id}`)
      await this.cache.del(`target-companies:${tenantId}:*`)

      return company
    } catch (error) {
      this.logger.error('Failed to update scoring', { error, id, scores, tenantId })
      throw error
    }
  }

  /**
   * Search target companies with advanced filtering
   */
  async searchTargetCompanies(
    tenantId: string,
    query: string,
    filters: TargetCompanyFilters = {},
    limit: number = 10
  ) {
    try {
      const searchFilters = {
        ...filters,
        search: query
      }

      const result = await this.getTargetCompanies(
        tenantId,
        searchFilters,
        { field: 'opportunityScore', direction: 'desc' as const },
        1,
        limit
      )

      return result.companies
    } catch (error) {
      this.logger.error('Failed to search target companies', { error, query, tenantId })
      throw error
    }
  }

  /**
   * Get opportunity pipeline for tenant
   */
  async getOpportunityPipeline(tenantId: string) {
    try {
      return await this.pipelineService.getPipeline(tenantId)
    } catch (error) {
      this.logger.error('Failed to get opportunity pipeline', { error, tenantId })
      throw error
    }
  }

  /**
   * Move target company to next stage in pipeline
   */
  async moveToNextStage(
    targetId: string,
    userId: string,
    tenantId: string,
    reason?: string
  ) {
    try {
      return await this.pipelineService.moveToNextStage(targetId, userId, tenantId, reason)
    } catch (error) {
      this.logger.error('Failed to move target to next stage', { error, targetId, userId })
      throw error
    }
  }

  /**
   * Move target company to specific stage
   */
  async moveToStage(
    targetId: string,
    stageStatus: TargetCompanyStatus,
    userId: string,
    tenantId: string,
    reason?: string
  ) {
    try {
      return await this.pipelineService.moveToStage(targetId, stageStatus, userId, tenantId, reason)
    } catch (error) {
      this.logger.error('Failed to move target to stage', { error, targetId, stageStatus })
      throw error
    }
  }

  /**
   * Calculate opportunity scoring for target company
   */
  async calculateOpportunityScoring(
    targetId: string,
    tenantId: string,
    factors?: {
      marketSize?: number
      competitivePosition?: number
      financialStrength?: number
      strategicAlignment?: number
      managementQuality?: number
    }
  ) {
    try {
      return await this.pipelineService.calculateOpportunityScoring(targetId, tenantId, factors)
    } catch (error) {
      this.logger.error('Failed to calculate opportunity scoring', { error, targetId })
      throw error
    }
  }

  /**
   * Get pipeline metrics for analytics
   */
  async getPipelineMetrics(tenantId: string) {
    try {
      return await this.pipelineService.getPipelineMetrics(tenantId)
    } catch (error) {
      this.logger.error('Failed to get pipeline metrics', { error, tenantId })
      throw error
    }
  }
}
