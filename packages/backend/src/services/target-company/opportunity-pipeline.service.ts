import { PrismaClient, TargetCompanyStatus, InteractionType } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface OpportunityStage {
  id: string
  name: string
  description?: string
  order: number
  status: TargetCompanyStatus
  color?: string
  isDefault: boolean
  autoAdvance: boolean
  requiredActions: string[]
}

export interface OpportunityPipeline {
  id: string
  name: string
  description?: string
  stages: OpportunityStage[]
  isDefault: boolean
  isActive: boolean
  tenantId: string
}

export interface OpportunityMetrics {
  stageId: string
  stageName: string
  companyCount: number
  averageScore: number
  averageDaysInStage: number
  conversionRate: number
  dropOffRate: number
}

export interface OpportunityScoring {
  targetId: string
  opportunityScore: number
  strategicFit: number
  financialHealth: number
  marketPosition: number
  competitiveAdvantage: number
  totalScore: number
  lastUpdated: Date
}

export interface PipelineActivity {
  id: string
  targetId: string
  type: 'status_change' | 'score_update' | 'interaction' | 'note_added'
  description: string
  metadata?: Record<string, any>
  userId: string
  createdAt: Date
}

export class OpportunityPipelineService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  // Default opportunity pipeline stages
  private readonly DEFAULT_STAGES: Omit<OpportunityStage, 'id'>[] = [
    {
      name: 'Identified',
      description: 'Target company has been identified and added to the system',
      order: 1,
      status: TargetCompanyStatus.ACTIVE,
      color: '#6B7280',
      isDefault: true,
      autoAdvance: false,
      requiredActions: ['basic_research']
    },
    {
      name: 'Researching',
      description: 'Initial research and evaluation in progress',
      order: 2,
      status: TargetCompanyStatus.ACTIVE,
      color: '#3B82F6',
      isDefault: false,
      autoAdvance: false,
      requiredActions: ['market_analysis', 'financial_review']
    },
    {
      name: 'Qualified',
      description: 'Target meets initial criteria and warrants further investigation',
      order: 3,
      status: TargetCompanyStatus.QUALIFIED,
      color: '#10B981',
      isDefault: false,
      autoAdvance: false,
      requiredActions: ['scoring_complete', 'initial_contact_plan']
    },
    {
      name: 'Contacted',
      description: 'Initial contact has been made with the target company',
      order: 4,
      status: TargetCompanyStatus.CONTACTED,
      color: '#F59E0B',
      isDefault: false,
      autoAdvance: false,
      requiredActions: ['first_meeting_scheduled']
    },
    {
      name: 'In Discussion',
      description: 'Active discussions and negotiations are underway',
      order: 5,
      status: TargetCompanyStatus.IN_DISCUSSION,
      color: '#8B5CF6',
      isDefault: false,
      autoAdvance: false,
      requiredActions: ['nda_signed', 'preliminary_valuation']
    },
    {
      name: 'Disqualified',
      description: 'Target does not meet criteria or is not interested',
      order: 99,
      status: TargetCompanyStatus.DISQUALIFIED,
      color: '#EF4444',
      isDefault: false,
      autoAdvance: false,
      requiredActions: ['disqualification_reason']
    }
  ]

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('OpportunityPipelineService')
  }

  /**
   * Initialize default opportunity pipeline for a tenant
   */
  async initializeDefaultPipeline(tenantId: string): Promise<OpportunityPipeline> {
    try {
      this.logger.info('Initializing default opportunity pipeline', { tenantId })

      const pipeline: OpportunityPipeline = {
        id: `default-${tenantId}`,
        name: 'Default Opportunity Pipeline',
        description: 'Standard pipeline for target company evaluation and qualification',
        stages: this.DEFAULT_STAGES.map((stage, index) => ({
          ...stage,
          id: `stage-${index + 1}-${tenantId}`
        })),
        isDefault: true,
        isActive: true,
        tenantId
      }

      // Cache the pipeline
      await this.cache.set(
        `opportunity-pipeline:${tenantId}:default`,
        JSON.stringify(pipeline),
        3600 // 1 hour
      )

      this.logger.info('Default opportunity pipeline initialized', { tenantId })
      return pipeline
    } catch (error) {
      this.logger.error('Failed to initialize default pipeline', { error, tenantId })
      throw error
    }
  }

  /**
   * Get opportunity pipeline for tenant
   */
  async getPipeline(tenantId: string): Promise<OpportunityPipeline> {
    try {
      const cacheKey = `opportunity-pipeline:${tenantId}:default`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      // Initialize default pipeline if not exists
      return await this.initializeDefaultPipeline(tenantId)
    } catch (error) {
      this.logger.error('Failed to get pipeline', { error, tenantId })
      throw error
    }
  }

  /**
   * Move target company to next stage in pipeline
   */
  async moveToNextStage(
    targetId: string,
    userId: string,
    tenantId: string,
    reason?: string
  ) {
    try {
      this.logger.info('Moving target to next stage', { targetId, userId, tenantId })

      const [target, pipeline] = await Promise.all([
        this.prisma.targetCompany.findFirst({
          where: { id: targetId, tenantId }
        }),
        this.getPipeline(tenantId)
      ])

      if (!target) {
        throw new Error('Target company not found')
      }

      const currentStageIndex = pipeline.stages.findIndex(
        stage => stage.status === target.status
      )

      if (currentStageIndex === -1) {
        throw new Error('Current stage not found in pipeline')
      }

      const nextStageIndex = currentStageIndex + 1
      if (nextStageIndex >= pipeline.stages.length || 
          pipeline.stages[nextStageIndex].order === 99) {
        throw new Error('No next stage available')
      }

      const nextStage = pipeline.stages[nextStageIndex]

      // Update target company status
      const updatedTarget = await this.prisma.targetCompany.update({
        where: { id: targetId },
        data: { 
          status: nextStage.status,
          lastContactDate: new Date()
        }
      })

      // Create interaction record for stage change
      await this.prisma.targetCompanyInteraction.create({
        data: {
          targetId,
          userId,
          type: InteractionType.NOTE,
          subject: `Moved to ${nextStage.name}`,
          description: reason || `Target company moved to ${nextStage.name} stage`,
          completedAt: new Date()
        }
      })

      // Clear cache
      await this.cache.del(`target-company:${targetId}`)
      await this.cache.del(`target-companies:${tenantId}:*`)

      this.logger.info('Target moved to next stage successfully', { 
        targetId, 
        fromStage: pipeline.stages[currentStageIndex].name,
        toStage: nextStage.name
      })

      return updatedTarget
    } catch (error) {
      this.logger.error('Failed to move target to next stage', { error, targetId, userId })
      throw error
    }
  }

  /**
   * Move target company to specific stage
   */
  async moveToStage(
    targetId: string,
    stageStatus: TargetCompanyStatus,
    userId: string,
    tenantId: string,
    reason?: string
  ) {
    try {
      this.logger.info('Moving target to specific stage', { 
        targetId, 
        stageStatus, 
        userId, 
        tenantId 
      })

      const [target, pipeline] = await Promise.all([
        this.prisma.targetCompany.findFirst({
          where: { id: targetId, tenantId }
        }),
        this.getPipeline(tenantId)
      ])

      if (!target) {
        throw new Error('Target company not found')
      }

      const targetStage = pipeline.stages.find(stage => stage.status === stageStatus)
      if (!targetStage) {
        throw new Error('Target stage not found in pipeline')
      }

      // Update target company status
      const updatedTarget = await this.prisma.targetCompany.update({
        where: { id: targetId },
        data: { 
          status: stageStatus,
          lastContactDate: new Date()
        }
      })

      // Create interaction record for stage change
      await this.prisma.targetCompanyInteraction.create({
        data: {
          targetId,
          userId,
          type: InteractionType.NOTE,
          subject: `Moved to ${targetStage.name}`,
          description: reason || `Target company moved to ${targetStage.name} stage`,
          completedAt: new Date()
        }
      })

      // Clear cache
      await this.cache.del(`target-company:${targetId}`)
      await this.cache.del(`target-companies:${tenantId}:*`)

      this.logger.info('Target moved to stage successfully', { 
        targetId, 
        toStage: targetStage.name
      })

      return updatedTarget
    } catch (error) {
      this.logger.error('Failed to move target to stage', { error, targetId, stageStatus })
      throw error
    }
  }

  /**
   * Calculate opportunity scoring for target company
   */
  async calculateOpportunityScoring(
    targetId: string,
    tenantId: string,
    factors?: {
      marketSize?: number
      competitivePosition?: number
      financialStrength?: number
      strategicAlignment?: number
      managementQuality?: number
    }
  ): Promise<OpportunityScoring> {
    try {
      this.logger.info('Calculating opportunity scoring', { targetId, tenantId })

      const target = await this.prisma.targetCompany.findFirst({
        where: { id: targetId, tenantId }
      })

      if (!target) {
        throw new Error('Target company not found')
      }

      // Calculate scores based on available data and factors
      const opportunityScore = this.calculateOpportunityScore(target, factors)
      const strategicFit = this.calculateStrategicFit(target, factors)
      const financialHealth = this.calculateFinancialHealth(target, factors)
      const marketPosition = this.calculateMarketPosition(target, factors)
      const competitiveAdvantage = this.calculateCompetitiveAdvantage(target, factors)

      const totalScore = Math.round(
        (opportunityScore + strategicFit + financialHealth + marketPosition + competitiveAdvantage) / 5
      )

      // Update target company with calculated scores
      await this.prisma.targetCompany.update({
        where: { id: targetId },
        data: {
          opportunityScore,
          strategicFit,
          financialHealth
        }
      })

      const scoring: OpportunityScoring = {
        targetId,
        opportunityScore,
        strategicFit,
        financialHealth,
        marketPosition,
        competitiveAdvantage,
        totalScore,
        lastUpdated: new Date()
      }

      // Cache the scoring
      await this.cache.set(
        `opportunity-scoring:${targetId}`,
        JSON.stringify(scoring),
        1800 // 30 minutes
      )

      this.logger.info('Opportunity scoring calculated', { targetId, totalScore })
      return scoring
    } catch (error) {
      this.logger.error('Failed to calculate opportunity scoring', { error, targetId })
      throw error
    }
  }

  /**
   * Get pipeline metrics for analytics
   */
  async getPipelineMetrics(tenantId: string): Promise<OpportunityMetrics[]> {
    try {
      const cacheKey = `opportunity-metrics:${tenantId}`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      const pipeline = await this.getPipeline(tenantId)
      const metrics: OpportunityMetrics[] = []

      for (const stage of pipeline.stages) {
        const companies = await this.prisma.targetCompany.findMany({
          where: { tenantId, status: stage.status }
        })

        const companyCount = companies.length
        const averageScore = companyCount > 0 
          ? Math.round(companies.reduce((sum, c) => sum + (c.opportunityScore || 0), 0) / companyCount)
          : 0

        metrics.push({
          stageId: stage.id,
          stageName: stage.name,
          companyCount,
          averageScore,
          averageDaysInStage: 0, // TODO: Calculate based on interaction history
          conversionRate: 0, // TODO: Calculate based on stage transitions
          dropOffRate: 0 // TODO: Calculate based on disqualifications
        })
      }

      // Cache for 15 minutes
      await this.cache.set(cacheKey, JSON.stringify(metrics), 900)

      return metrics
    } catch (error) {
      this.logger.error('Failed to get pipeline metrics', { error, tenantId })
      throw error
    }
  }

  // Private helper methods for scoring calculations
  private calculateOpportunityScore(target: any, factors?: any): number {
    let score = 50 // Base score

    // Revenue-based scoring
    if (target.revenue) {
      const revenue = parseFloat(target.revenue.toString())
      if (revenue > *********) score += 20 // >$100M
      else if (revenue > 50000000) score += 15 // >$50M
      else if (revenue > 10000000) score += 10 // >$10M
      else if (revenue > 1000000) score += 5 // >$1M
    }

    // Employee count scoring
    if (target.employees) {
      if (target.employees > 1000) score += 15
      else if (target.employees > 500) score += 10
      else if (target.employees > 100) score += 5
    }

    // Industry alignment (simplified)
    if (target.industry) {
      const strategicIndustries = ['Technology', 'Healthcare', 'Financial Services']
      if (strategicIndustries.includes(target.industry)) {
        score += 10
      }
    }

    // Apply external factors if provided
    if (factors?.marketSize) {
      score += Math.round(factors.marketSize * 0.2)
    }

    return Math.min(Math.max(score, 0), 100)
  }

  private calculateStrategicFit(target: any, factors?: any): number {
    let score = 50

    // Geographic alignment
    if (target.country) {
      const strategicCountries = ['United States', 'Canada', 'United Kingdom', 'Germany']
      if (strategicCountries.includes(target.country)) {
        score += 15
      }
    }

    // Apply external factors
    if (factors?.strategicAlignment) {
      score = Math.round(factors.strategicAlignment)
    }

    return Math.min(Math.max(score, 0), 100)
  }

  private calculateFinancialHealth(target: any, factors?: any): number {
    let score = 50

    // EBITDA margin if available
    if (target.revenue && target.ebitda) {
      const revenue = parseFloat(target.revenue.toString())
      const ebitda = parseFloat(target.ebitda.toString())
      const margin = (ebitda / revenue) * 100

      if (margin > 20) score += 20
      else if (margin > 15) score += 15
      else if (margin > 10) score += 10
      else if (margin > 5) score += 5
    }

    // Apply external factors
    if (factors?.financialStrength) {
      score = Math.round(factors.financialStrength)
    }

    return Math.min(Math.max(score, 0), 100)
  }

  private calculateMarketPosition(target: any, factors?: any): number {
    let score = 50

    // Company age (maturity)
    if (target.foundedYear) {
      const age = new Date().getFullYear() - target.foundedYear
      if (age > 20) score += 15
      else if (age > 10) score += 10
      else if (age > 5) score += 5
    }

    // Apply external factors
    if (factors?.competitivePosition) {
      score = Math.round(factors.competitivePosition)
    }

    return Math.min(Math.max(score, 0), 100)
  }

  private calculateCompetitiveAdvantage(target: any, factors?: any): number {
    let score = 50

    // Technology/innovation indicators
    if (target.description) {
      const innovationKeywords = ['AI', 'machine learning', 'blockchain', 'cloud', 'SaaS', 'platform']
      const hasInnovation = innovationKeywords.some(keyword => 
        target.description.toLowerCase().includes(keyword.toLowerCase())
      )
      if (hasInnovation) score += 15
    }

    // Apply external factors
    if (factors?.managementQuality) {
      score += Math.round(factors.managementQuality * 0.3)
    }

    return Math.min(Math.max(score, 0), 100)
  }
}
