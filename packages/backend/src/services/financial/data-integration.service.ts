import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import axios, { AxiosInstance } from 'axios'

export interface ExternalDataSource {
  id: string
  name: string
  type: DataSourceType
  apiEndpoint: string
  apiKey?: string
  isActive: boolean
  rateLimits: {
    requestsPerMinute: number
    requestsPerDay: number
  }
  supportedDataTypes: string[]
  configuration: Record<string, any>
}

export enum DataSourceType {
  FINANCIAL_DATA = 'FINANCIAL_DATA',
  MARKET_DATA = 'MARKET_DATA',
  ECONOMIC_DATA = 'ECONOMIC_DATA',
  COMPANY_DATA = 'COMPANY_DATA',
  TRANSACTION_DATA = 'TRANSACTION_DATA'
}

export interface CompanyFinancials {
  symbol: string
  companyName: string
  period: string
  periodType: 'annual' | 'quarterly'
  currency: string
  financialData: {
    revenue: number
    grossProfit: number
    operatingIncome: number
    ebitda: number
    netIncome: number
    totalAssets: number
    totalLiabilities: number
    shareholderEquity: number
    totalDebt: number
    cash: number
    freeCashFlow: number
    capex: number
    workingCapital: number
  }
  ratios: {
    peRatio: number
    pbRatio: number
    evEbitda: number
    evRevenue: number
    debtToEquity: number
    currentRatio: number
    quickRatio: number
    roa: number
    roe: number
    grossMargin: number
    operatingMargin: number
    netMargin: number
  }
  marketData: {
    marketCap: number
    enterpriseValue: number
    sharesOutstanding: number
    bookValuePerShare: number
    priceToBook: number
    dividendYield: number
    beta: number
  }
  lastUpdated: Date
}

export interface MarketData {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap: number
  pe: number
  eps: number
  high52Week: number
  low52Week: number
  dividendYield: number
  beta: number
  lastUpdated: Date
}

export interface EconomicIndicator {
  indicator: string
  value: number
  period: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annual'
  unit: string
  lastUpdated: Date
}

export interface TransactionData {
  dealId: string
  announcementDate: Date
  targetCompany: string
  acquirerCompany: string
  dealValue: number
  dealType: string
  industry: string
  targetRevenue: number
  targetEbitda: number
  multiple: number
  premium: number
  status: string
  source: string
}

export interface DataSyncResult {
  source: string
  dataType: string
  recordsProcessed: number
  recordsUpdated: number
  recordsCreated: number
  errors: string[]
  syncDuration: number
  lastSyncAt: Date
}

export class DataIntegrationService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger
  private httpClient: AxiosInstance

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DataIntegrationService')
    this.httpClient = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'M&A-Platform/1.0'
      }
    })
  }

  /**
   * Sync company financials from external sources
   */
  async syncCompanyFinancials(
    symbols: string[],
    dataSource: ExternalDataSource
  ): Promise<DataSyncResult> {
    const startTime = Date.now()
    let recordsProcessed = 0
    let recordsUpdated = 0
    let recordsCreated = 0
    const errors: string[] = []

    try {
      this.logger.info('Starting company financials sync', { 
        symbols: symbols.length, 
        source: dataSource.name 
      })

      for (const symbol of symbols) {
        try {
          // Check rate limits
          await this.checkRateLimit(dataSource)

          // Fetch financial data
          const financialData = await this.fetchCompanyFinancials(symbol, dataSource)
          
          if (financialData) {
            // Store or update in database
            const existing = await this.prisma.companyFinancials.findFirst({
              where: { 
                symbol: financialData.symbol,
                period: financialData.period,
                periodType: financialData.periodType
              }
            })

            if (existing) {
              await this.prisma.companyFinancials.update({
                where: { id: existing.id },
                data: {
                  ...financialData,
                  financialData: financialData.financialData as any,
                  ratios: financialData.ratios as any,
                  marketData: financialData.marketData as any
                }
              })
              recordsUpdated++
            } else {
              await this.prisma.companyFinancials.create({
                data: {
                  ...financialData,
                  financialData: financialData.financialData as any,
                  ratios: financialData.ratios as any,
                  marketData: financialData.marketData as any
                }
              })
              recordsCreated++
            }

            // Cache the data
            await this.cache.set(
              `company-financials:${symbol}:${financialData.period}`,
              JSON.stringify(financialData),
              3600 // 1 hour
            )
          }

          recordsProcessed++
        } catch (error) {
          this.logger.error('Failed to sync financials for symbol', { error, symbol })
          errors.push(`${symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      const syncDuration = Date.now() - startTime

      this.logger.info('Company financials sync completed', {
        recordsProcessed,
        recordsUpdated,
        recordsCreated,
        errors: errors.length,
        duration: syncDuration
      })

      return {
        source: dataSource.name,
        dataType: 'company_financials',
        recordsProcessed,
        recordsUpdated,
        recordsCreated,
        errors,
        syncDuration,
        lastSyncAt: new Date()
      }
    } catch (error) {
      this.logger.error('Company financials sync failed', { error, dataSource: dataSource.name })
      throw error
    }
  }

  /**
   * Fetch company financials from external API
   */
  private async fetchCompanyFinancials(
    symbol: string,
    dataSource: ExternalDataSource
  ): Promise<CompanyFinancials | null> {
    try {
      // This is a mock implementation
      // In production, you would integrate with real financial data providers
      // like Alpha Vantage, Yahoo Finance, Bloomberg API, etc.

      const mockData: CompanyFinancials = {
        symbol,
        companyName: `${symbol} Inc.`,
        period: '2023',
        periodType: 'annual',
        currency: 'USD',
        financialData: {
          revenue: Math.random() * **********0,
          grossProfit: Math.random() * **********,
          operatingIncome: Math.random() * **********,
          ebitda: Math.random() * **********,
          netIncome: Math.random() * **********,
          totalAssets: Math.random() * **********0,
          totalLiabilities: Math.random() * **********0,
          shareholderEquity: Math.random() * **********0,
          totalDebt: Math.random() * **********,
          cash: Math.random() * **********,
          freeCashFlow: Math.random() * **********,
          capex: Math.random() * **********,
          workingCapital: Math.random() * **********
        },
        ratios: {
          peRatio: Math.random() * 30 + 10,
          pbRatio: Math.random() * 5 + 1,
          evEbitda: Math.random() * 20 + 5,
          evRevenue: Math.random() * 10 + 1,
          debtToEquity: Math.random() * 2,
          currentRatio: Math.random() * 3 + 1,
          quickRatio: Math.random() * 2 + 0.5,
          roa: Math.random() * 0.2,
          roe: Math.random() * 0.3,
          grossMargin: Math.random() * 0.5 + 0.2,
          operatingMargin: Math.random() * 0.3 + 0.1,
          netMargin: Math.random() * 0.2 + 0.05
        },
        marketData: {
          marketCap: Math.random() * **********00,
          enterpriseValue: Math.random() * 1**********0,
          sharesOutstanding: Math.random() * **********,
          bookValuePerShare: Math.random() * 100,
          priceToBook: Math.random() * 5 + 1,
          dividendYield: Math.random() * 0.05,
          beta: Math.random() * 2 + 0.5
        },
        lastUpdated: new Date()
      }

      return mockData
    } catch (error) {
      this.logger.error('Failed to fetch company financials', { error, symbol })
      return null
    }
  }

  /**
   * Sync market data
   */
  async syncMarketData(
    symbols: string[],
    dataSource: ExternalDataSource
  ): Promise<DataSyncResult> {
    const startTime = Date.now()
    let recordsProcessed = 0
    let recordsUpdated = 0
    let recordsCreated = 0
    const errors: string[] = []

    try {
      this.logger.info('Starting market data sync', { 
        symbols: symbols.length, 
        source: dataSource.name 
      })

      for (const symbol of symbols) {
        try {
          await this.checkRateLimit(dataSource)

          const marketData = await this.fetchMarketData(symbol, dataSource)
          
          if (marketData) {
            const existing = await this.prisma.marketData.findFirst({
              where: { symbol: marketData.symbol }
            })

            if (existing) {
              await this.prisma.marketData.update({
                where: { id: existing.id },
                data: marketData
              })
              recordsUpdated++
            } else {
              await this.prisma.marketData.create({
                data: marketData
              })
              recordsCreated++
            }

            // Cache the data
            await this.cache.set(
              `market-data:${symbol}`,
              JSON.stringify(marketData),
              300 // 5 minutes
            )
          }

          recordsProcessed++
        } catch (error) {
          this.logger.error('Failed to sync market data for symbol', { error, symbol })
          errors.push(`${symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      const syncDuration = Date.now() - startTime

      return {
        source: dataSource.name,
        dataType: 'market_data',
        recordsProcessed,
        recordsUpdated,
        recordsCreated,
        errors,
        syncDuration,
        lastSyncAt: new Date()
      }
    } catch (error) {
      this.logger.error('Market data sync failed', { error, dataSource: dataSource.name })
      throw error
    }
  }

  /**
   * Fetch market data from external API
   */
  private async fetchMarketData(
    symbol: string,
    dataSource: ExternalDataSource
  ): Promise<MarketData | null> {
    try {
      // Mock implementation
      const mockData: MarketData = {
        symbol,
        price: Math.random() * 500 + 50,
        change: (Math.random() - 0.5) * 20,
        changePercent: (Math.random() - 0.5) * 10,
        volume: Math.random() * 10000000,
        marketCap: Math.random() * **********00,
        pe: Math.random() * 30 + 10,
        eps: Math.random() * 20,
        high52Week: Math.random() * 600 + 100,
        low52Week: Math.random() * 200 + 20,
        dividendYield: Math.random() * 0.05,
        beta: Math.random() * 2 + 0.5,
        lastUpdated: new Date()
      }

      return mockData
    } catch (error) {
      this.logger.error('Failed to fetch market data', { error, symbol })
      return null
    }
  }

  /**
   * Sync economic indicators
   */
  async syncEconomicIndicators(
    indicators: string[],
    dataSource: ExternalDataSource
  ): Promise<DataSyncResult> {
    const startTime = Date.now()
    let recordsProcessed = 0
    let recordsUpdated = 0
    let recordsCreated = 0
    const errors: string[] = []

    try {
      this.logger.info('Starting economic indicators sync', { 
        indicators: indicators.length, 
        source: dataSource.name 
      })

      for (const indicator of indicators) {
        try {
          await this.checkRateLimit(dataSource)

          const economicData = await this.fetchEconomicIndicator(indicator, dataSource)
          
          if (economicData) {
            const existing = await this.prisma.economicIndicator.findFirst({
              where: { 
                indicator: economicData.indicator,
                period: economicData.period
              }
            })

            if (existing) {
              await this.prisma.economicIndicator.update({
                where: { id: existing.id },
                data: economicData
              })
              recordsUpdated++
            } else {
              await this.prisma.economicIndicator.create({
                data: economicData
              })
              recordsCreated++
            }
          }

          recordsProcessed++
        } catch (error) {
          this.logger.error('Failed to sync economic indicator', { error, indicator })
          errors.push(`${indicator}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      const syncDuration = Date.now() - startTime

      return {
        source: dataSource.name,
        dataType: 'economic_indicators',
        recordsProcessed,
        recordsUpdated,
        recordsCreated,
        errors,
        syncDuration,
        lastSyncAt: new Date()
      }
    } catch (error) {
      this.logger.error('Economic indicators sync failed', { error, dataSource: dataSource.name })
      throw error
    }
  }

  /**
   * Fetch economic indicator from external API
   */
  private async fetchEconomicIndicator(
    indicator: string,
    dataSource: ExternalDataSource
  ): Promise<EconomicIndicator | null> {
    try {
      // Mock implementation
      const mockData: EconomicIndicator = {
        indicator,
        value: Math.random() * 10,
        period: '2023-Q4',
        frequency: 'quarterly',
        unit: '%',
        lastUpdated: new Date()
      }

      return mockData
    } catch (error) {
      this.logger.error('Failed to fetch economic indicator', { error, indicator })
      return null
    }
  }

  /**
   * Check rate limits for data source
   */
  private async checkRateLimit(dataSource: ExternalDataSource): Promise<void> {
    const cacheKey = `rate-limit:${dataSource.id}`
    const currentCount = await this.cache.get(cacheKey)
    
    if (currentCount && parseInt(currentCount) >= dataSource.rateLimits.requestsPerMinute) {
      // Wait for rate limit reset
      await new Promise(resolve => setTimeout(resolve, 60000))
    }

    // Increment counter
    await this.cache.set(cacheKey, '1', 60, parseInt(currentCount || '0') + 1)
  }

  /**
   * Get cached company financials
   */
  async getCachedCompanyFinancials(symbol: string, period?: string): Promise<CompanyFinancials | null> {
    try {
      const cacheKey = period 
        ? `company-financials:${symbol}:${period}`
        : `company-financials:${symbol}:latest`
      
      const cached = await this.cache.get(cacheKey)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      this.logger.error('Failed to get cached company financials', { error, symbol })
      return null
    }
  }

  /**
   * Get cached market data
   */
  async getCachedMarketData(symbol: string): Promise<MarketData | null> {
    try {
      const cached = await this.cache.get(`market-data:${symbol}`)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      this.logger.error('Failed to get cached market data', { error, symbol })
      return null
    }
  }

  /**
   * Configure data source
   */
  async configureDataSource(
    dataSource: Omit<ExternalDataSource, 'id'>,
    tenantId: string
  ): Promise<ExternalDataSource> {
    try {
      const newDataSource = await this.prisma.externalDataSource.create({
        data: {
          ...dataSource,
          tenantId,
          rateLimits: dataSource.rateLimits as any,
          configuration: dataSource.configuration as any
        }
      })

      this.logger.info('Data source configured', { 
        dataSourceId: newDataSource.id,
        name: dataSource.name 
      })

      return newDataSource as ExternalDataSource
    } catch (error) {
      this.logger.error('Failed to configure data source', { error, dataSource })
      throw error
    }
  }

  /**
   * Test data source connection
   */
  async testDataSourceConnection(dataSourceId: string): Promise<boolean> {
    try {
      const dataSource = await this.prisma.externalDataSource.findUnique({
        where: { id: dataSourceId }
      })

      if (!dataSource) {
        throw new Error('Data source not found')
      }

      // Test connection with a simple request
      const response = await this.httpClient.get(dataSource.apiEndpoint, {
        headers: dataSource.apiKey ? { 'Authorization': `Bearer ${dataSource.apiKey}` } : {},
        timeout: 10000
      })

      return response.status === 200
    } catch (error) {
      this.logger.error('Data source connection test failed', { error, dataSourceId })
      return false
    }
  }
}
