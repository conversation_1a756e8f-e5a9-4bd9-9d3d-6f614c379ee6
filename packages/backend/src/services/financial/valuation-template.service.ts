import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface ValuationTemplate {
  id: string
  name: string
  description?: string
  category: ValuationCategory
  methodology: ValuationMethodology
  configuration: TemplateConfiguration
  isPublic: boolean
  createdBy: string
  tenantId: string
  createdAt: Date
  updatedAt: Date
}

export enum ValuationCategory {
  DCF = 'DCF',
  COMPARABLE_COMPANY = 'COMPARABLE_COMPANY',
  PRECEDENT_TRANSACTION = 'PRECEDENT_TRANSACTION',
  LBO = 'LBO',
  ASSET_BASED = 'ASSET_BASED',
  SUM_OF_PARTS = 'SUM_OF_PARTS'
}

export enum ValuationMethodology {
  UNLEVERED_DCF = 'UNLEVERED_DCF',
  LEVERED_DCF = 'LEVERED_DCF',
  DIVIDEND_DISCOUNT = 'DIVIDEND_DISCOUNT',
  TRADING_MULTIPLES = 'TRADING_MULTIPLES',
  TRANSACTION_MULTIPLES = 'TRANSACTION_MULTIPLES',
  LBO_ANALYSIS = 'LBO_ANALYSIS',
  NAV = 'NAV',
  LIQUIDATION = 'LIQUIDATION'
}

export interface TemplateConfiguration {
  assumptions: AssumptionSet[]
  calculations: CalculationStep[]
  outputs: OutputDefinition[]
  validationRules: ValidationRule[]
  formatting: FormattingOptions
}

export interface AssumptionSet {
  category: string
  assumptions: Array<{
    key: string
    label: string
    type: 'number' | 'percentage' | 'currency' | 'date' | 'boolean' | 'select'
    defaultValue?: any
    options?: string[]
    validation?: {
      min?: number
      max?: number
      required?: boolean
    }
    description?: string
    formula?: string
  }>
}

export interface CalculationStep {
  id: string
  name: string
  formula: string
  dependencies: string[]
  outputKey: string
  description?: string
}

export interface OutputDefinition {
  key: string
  label: string
  type: 'currency' | 'percentage' | 'multiple' | 'number'
  format?: string
  category: 'primary' | 'secondary' | 'detail'
  chartable?: boolean
}

export interface ValidationRule {
  field: string
  rule: 'required' | 'min' | 'max' | 'range' | 'custom'
  value?: any
  message: string
  formula?: string
}

export interface FormattingOptions {
  currency: string
  decimalPlaces: number
  thousandsSeparator: string
  percentageFormat: string
  dateFormat: string
}

export interface ScenarioModel {
  id: string
  templateId: string
  name: string
  description?: string
  assumptions: Record<string, any>
  results: Record<string, any>
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface SensitivityAnalysisConfig {
  variables: Array<{
    key: string
    label: string
    baseValue: number
    ranges: number[]
  }>
  outputMetrics: string[]
  chartType: 'tornado' | 'spider' | 'waterfall' | 'sensitivity_table'
}

export class ValuationTemplateService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('ValuationTemplateService')
  }

  /**
   * Get predefined valuation templates
   */
  async getTemplates(
    tenantId: string,
    category?: ValuationCategory,
    methodology?: ValuationMethodology
  ): Promise<ValuationTemplate[]> {
    try {
      const cacheKey = `valuation-templates:${tenantId}:${category}:${methodology}`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      const whereClause: any = {
        OR: [
          { tenantId, isPublic: false },
          { isPublic: true }
        ]
      }

      if (category) {
        whereClause.category = category
      }

      if (methodology) {
        whereClause.methodology = methodology
      }

      const templates = await this.prisma.valuationTemplate.findMany({
        where: whereClause,
        orderBy: [
          { isPublic: 'asc' },
          { name: 'asc' }
        ]
      })

      // Cache for 1 hour
      await this.cache.set(cacheKey, JSON.stringify(templates), 3600)

      return templates as ValuationTemplate[]
    } catch (error) {
      this.logger.error('Failed to get valuation templates', { error, tenantId })
      throw error
    }
  }

  /**
   * Create a new valuation template
   */
  async createTemplate(
    template: Omit<ValuationTemplate, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<ValuationTemplate> {
    try {
      this.logger.info('Creating valuation template', { name: template.name, userId })

      // Validate template configuration
      this.validateTemplateConfiguration(template.configuration)

      const newTemplate = await this.prisma.valuationTemplate.create({
        data: {
          ...template,
          createdBy: userId,
          configuration: template.configuration as any
        }
      })

      // Invalidate cache
      await this.cache.delete(`valuation-templates:${template.tenantId}:*`)

      this.logger.info('Valuation template created', { templateId: newTemplate.id })
      return newTemplate as ValuationTemplate
    } catch (error) {
      this.logger.error('Failed to create valuation template', { error, template })
      throw error
    }
  }

  /**
   * Create scenario model from template
   */
  async createScenarioModel(
    templateId: string,
    name: string,
    assumptions: Record<string, any>,
    userId: string,
    tenantId: string
  ): Promise<ScenarioModel> {
    try {
      this.logger.info('Creating scenario model', { templateId, name, userId })

      // Get template
      const template = await this.prisma.valuationTemplate.findFirst({
        where: { id: templateId, tenantId }
      })

      if (!template) {
        throw new Error('Template not found')
      }

      // Validate assumptions against template
      this.validateAssumptions(assumptions, template.configuration as TemplateConfiguration)

      // Calculate results
      const results = await this.calculateResults(assumptions, template.configuration as TemplateConfiguration)

      const scenarioModel = await this.prisma.scenarioModel.create({
        data: {
          templateId,
          name,
          assumptions: assumptions as any,
          results: results as any,
          createdBy: userId,
          tenantId
        }
      })

      this.logger.info('Scenario model created', { scenarioId: scenarioModel.id })
      return scenarioModel as ScenarioModel
    } catch (error) {
      this.logger.error('Failed to create scenario model', { error, templateId })
      throw error
    }
  }

  /**
   * Get default templates for each valuation method
   */
  async getDefaultTemplates(): Promise<ValuationTemplate[]> {
    return [
      this.createDCFTemplate(),
      this.createComparableCompanyTemplate(),
      this.createPrecedentTransactionTemplate(),
      this.createLBOTemplate()
    ]
  }

  /**
   * Create DCF template
   */
  private createDCFTemplate(): ValuationTemplate {
    return {
      id: 'dcf-default',
      name: 'Discounted Cash Flow (DCF)',
      description: 'Standard DCF valuation model with 5-year projections',
      category: ValuationCategory.DCF,
      methodology: ValuationMethodology.UNLEVERED_DCF,
      isPublic: true,
      createdBy: 'system',
      tenantId: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      configuration: {
        assumptions: [
          {
            category: 'Revenue Projections',
            assumptions: [
              {
                key: 'baseRevenue',
                label: 'Base Year Revenue',
                type: 'currency',
                defaultValue: *********,
                validation: { min: 0, required: true },
                description: 'Current year revenue'
              },
              {
                key: 'revenueGrowthY1',
                label: 'Year 1 Growth Rate',
                type: 'percentage',
                defaultValue: 0.15,
                validation: { min: -0.5, max: 2, required: true }
              },
              {
                key: 'revenueGrowthY2',
                label: 'Year 2 Growth Rate',
                type: 'percentage',
                defaultValue: 0.12,
                validation: { min: -0.5, max: 2, required: true }
              },
              {
                key: 'revenueGrowthY3',
                label: 'Year 3 Growth Rate',
                type: 'percentage',
                defaultValue: 0.10,
                validation: { min: -0.5, max: 2, required: true }
              },
              {
                key: 'revenueGrowthY4',
                label: 'Year 4 Growth Rate',
                type: 'percentage',
                defaultValue: 0.08,
                validation: { min: -0.5, max: 2, required: true }
              },
              {
                key: 'revenueGrowthY5',
                label: 'Year 5 Growth Rate',
                type: 'percentage',
                defaultValue: 0.06,
                validation: { min: -0.5, max: 2, required: true }
              }
            ]
          },
          {
            category: 'Profitability',
            assumptions: [
              {
                key: 'ebitdaMargin',
                label: 'EBITDA Margin',
                type: 'percentage',
                defaultValue: 0.25,
                validation: { min: 0, max: 1, required: true }
              },
              {
                key: 'taxRate',
                label: 'Tax Rate',
                type: 'percentage',
                defaultValue: 0.25,
                validation: { min: 0, max: 0.5, required: true }
              }
            ]
          },
          {
            category: 'Valuation Parameters',
            assumptions: [
              {
                key: 'discountRate',
                label: 'Discount Rate (WACC)',
                type: 'percentage',
                defaultValue: 0.10,
                validation: { min: 0.05, max: 0.25, required: true }
              },
              {
                key: 'terminalGrowthRate',
                label: 'Terminal Growth Rate',
                type: 'percentage',
                defaultValue: 0.025,
                validation: { min: 0, max: 0.05, required: true }
              }
            ]
          }
        ],
        calculations: [
          {
            id: 'revenue_projections',
            name: 'Revenue Projections',
            formula: 'baseRevenue * (1 + revenueGrowthY1) * (1 + revenueGrowthY2) * ...',
            dependencies: ['baseRevenue', 'revenueGrowthY1', 'revenueGrowthY2', 'revenueGrowthY3', 'revenueGrowthY4', 'revenueGrowthY5'],
            outputKey: 'projectedRevenues'
          },
          {
            id: 'ebitda_projections',
            name: 'EBITDA Projections',
            formula: 'projectedRevenues * ebitdaMargin',
            dependencies: ['projectedRevenues', 'ebitdaMargin'],
            outputKey: 'projectedEbitda'
          },
          {
            id: 'fcf_projections',
            name: 'Free Cash Flow Projections',
            formula: 'projectedEbitda * (1 - taxRate) + depreciation - capex - workingCapitalChange',
            dependencies: ['projectedEbitda', 'taxRate'],
            outputKey: 'projectedFCF'
          },
          {
            id: 'terminal_value',
            name: 'Terminal Value',
            formula: 'finalYearFCF * (1 + terminalGrowthRate) / (discountRate - terminalGrowthRate)',
            dependencies: ['projectedFCF', 'terminalGrowthRate', 'discountRate'],
            outputKey: 'terminalValue'
          },
          {
            id: 'enterprise_value',
            name: 'Enterprise Value',
            formula: 'NPV(projectedFCF) + NPV(terminalValue)',
            dependencies: ['projectedFCF', 'terminalValue', 'discountRate'],
            outputKey: 'enterpriseValue'
          }
        ],
        outputs: [
          {
            key: 'enterpriseValue',
            label: 'Enterprise Value',
            type: 'currency',
            category: 'primary',
            chartable: true
          },
          {
            key: 'equityValue',
            label: 'Equity Value',
            type: 'currency',
            category: 'primary',
            chartable: true
          },
          {
            key: 'impliedSharePrice',
            label: 'Implied Share Price',
            type: 'currency',
            category: 'primary',
            chartable: true
          }
        ],
        validationRules: [
          {
            field: 'discountRate',
            rule: 'custom',
            formula: 'discountRate > terminalGrowthRate',
            message: 'Discount rate must be greater than terminal growth rate'
          }
        ],
        formatting: {
          currency: 'USD',
          decimalPlaces: 0,
          thousandsSeparator: ',',
          percentageFormat: '0.0%',
          dateFormat: 'MM/DD/YYYY'
        }
      }
    }
  }

  /**
   * Create Comparable Company template
   */
  private createComparableCompanyTemplate(): ValuationTemplate {
    return {
      id: 'cca-default',
      name: 'Comparable Company Analysis',
      description: 'Trading multiples analysis using comparable public companies',
      category: ValuationCategory.COMPARABLE_COMPANY,
      methodology: ValuationMethodology.TRADING_MULTIPLES,
      isPublic: true,
      createdBy: 'system',
      tenantId: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      configuration: {
        assumptions: [
          {
            category: 'Target Metrics',
            assumptions: [
              {
                key: 'targetRevenue',
                label: 'Target Revenue (LTM)',
                type: 'currency',
                validation: { min: 0, required: true }
              },
              {
                key: 'targetEbitda',
                label: 'Target EBITDA (LTM)',
                type: 'currency',
                validation: { min: 0, required: true }
              },
              {
                key: 'targetNetIncome',
                label: 'Target Net Income (LTM)',
                type: 'currency',
                validation: { required: true }
              }
            ]
          },
          {
            category: 'Adjustments',
            assumptions: [
              {
                key: 'controlPremium',
                label: 'Control Premium',
                type: 'percentage',
                defaultValue: 0.25,
                validation: { min: 0, max: 1 }
              },
              {
                key: 'liquidityDiscount',
                label: 'Liquidity Discount',
                type: 'percentage',
                defaultValue: 0.10,
                validation: { min: 0, max: 0.5 }
              }
            ]
          }
        ],
        calculations: [],
        outputs: [
          {
            key: 'evRevenueValuation',
            label: 'EV/Revenue Valuation',
            type: 'currency',
            category: 'primary'
          },
          {
            key: 'evEbitdaValuation',
            label: 'EV/EBITDA Valuation',
            type: 'currency',
            category: 'primary'
          },
          {
            key: 'peValuation',
            label: 'P/E Valuation',
            type: 'currency',
            category: 'primary'
          }
        ],
        validationRules: [],
        formatting: {
          currency: 'USD',
          decimalPlaces: 0,
          thousandsSeparator: ',',
          percentageFormat: '0.0%',
          dateFormat: 'MM/DD/YYYY'
        }
      }
    }
  }

  /**
   * Create Precedent Transaction template
   */
  private createPrecedentTransactionTemplate(): ValuationTemplate {
    return {
      id: 'pta-default',
      name: 'Precedent Transaction Analysis',
      description: 'Transaction multiples analysis using comparable M&A deals',
      category: ValuationCategory.PRECEDENT_TRANSACTION,
      methodology: ValuationMethodology.TRANSACTION_MULTIPLES,
      isPublic: true,
      createdBy: 'system',
      tenantId: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      configuration: {
        assumptions: [
          {
            category: 'Target Metrics',
            assumptions: [
              {
                key: 'targetRevenue',
                label: 'Target Revenue (LTM)',
                type: 'currency',
                validation: { min: 0, required: true }
              },
              {
                key: 'targetEbitda',
                label: 'Target EBITDA (LTM)',
                type: 'currency',
                validation: { min: 0, required: true }
              }
            ]
          },
          {
            category: 'Transaction Filters',
            assumptions: [
              {
                key: 'timeRange',
                label: 'Transaction Time Range (Years)',
                type: 'number',
                defaultValue: 3,
                validation: { min: 1, max: 10 }
              },
              {
                key: 'minDealSize',
                label: 'Minimum Deal Size',
                type: 'currency',
                defaultValue: 50000000
              },
              {
                key: 'maxDealSize',
                label: 'Maximum Deal Size',
                type: 'currency',
                defaultValue: 5000000000
              }
            ]
          }
        ],
        calculations: [],
        outputs: [
          {
            key: 'transactionValuation',
            label: 'Transaction-based Valuation',
            type: 'currency',
            category: 'primary'
          }
        ],
        validationRules: [],
        formatting: {
          currency: 'USD',
          decimalPlaces: 0,
          thousandsSeparator: ',',
          percentageFormat: '0.0%',
          dateFormat: 'MM/DD/YYYY'
        }
      }
    }
  }

  /**
   * Create LBO template
   */
  private createLBOTemplate(): ValuationTemplate {
    return {
      id: 'lbo-default',
      name: 'Leveraged Buyout Analysis',
      description: 'LBO model with returns analysis',
      category: ValuationCategory.LBO,
      methodology: ValuationMethodology.LBO_ANALYSIS,
      isPublic: true,
      createdBy: 'system',
      tenantId: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
      configuration: {
        assumptions: [
          {
            category: 'Deal Structure',
            assumptions: [
              {
                key: 'purchasePrice',
                label: 'Purchase Price',
                type: 'currency',
                validation: { min: 0, required: true }
              },
              {
                key: 'debtFinancing',
                label: 'Debt Financing',
                type: 'currency',
                validation: { min: 0, required: true }
              },
              {
                key: 'equityFinancing',
                label: 'Equity Financing',
                type: 'currency',
                validation: { min: 0, required: true }
              }
            ]
          },
          {
            category: 'Exit Assumptions',
            assumptions: [
              {
                key: 'holdingPeriod',
                label: 'Holding Period (Years)',
                type: 'number',
                defaultValue: 5,
                validation: { min: 3, max: 10, required: true }
              },
              {
                key: 'exitMultiple',
                label: 'Exit EBITDA Multiple',
                type: 'number',
                defaultValue: 10,
                validation: { min: 5, max: 20, required: true }
              }
            ]
          }
        ],
        calculations: [],
        outputs: [
          {
            key: 'irr',
            label: 'Internal Rate of Return',
            type: 'percentage',
            category: 'primary'
          },
          {
            key: 'moic',
            label: 'Multiple of Invested Capital',
            type: 'multiple',
            category: 'primary'
          }
        ],
        validationRules: [],
        formatting: {
          currency: 'USD',
          decimalPlaces: 1,
          thousandsSeparator: ',',
          percentageFormat: '0.0%',
          dateFormat: 'MM/DD/YYYY'
        }
      }
    }
  }

  /**
   * Validate template configuration
   */
  private validateTemplateConfiguration(config: TemplateConfiguration): void {
    if (!config.assumptions || config.assumptions.length === 0) {
      throw new Error('Template must have at least one assumption category')
    }

    if (!config.outputs || config.outputs.length === 0) {
      throw new Error('Template must have at least one output definition')
    }

    // Validate assumption keys are unique
    const allKeys = config.assumptions.flatMap(cat => cat.assumptions.map(a => a.key))
    const uniqueKeys = new Set(allKeys)
    if (allKeys.length !== uniqueKeys.size) {
      throw new Error('Assumption keys must be unique')
    }
  }

  /**
   * Validate assumptions against template
   */
  private validateAssumptions(assumptions: Record<string, any>, config: TemplateConfiguration): void {
    for (const category of config.assumptions) {
      for (const assumption of category.assumptions) {
        const value = assumptions[assumption.key]
        
        if (assumption.validation?.required && (value === undefined || value === null)) {
          throw new Error(`Required assumption '${assumption.label}' is missing`)
        }

        if (value !== undefined && assumption.validation) {
          if (assumption.validation.min !== undefined && value < assumption.validation.min) {
            throw new Error(`${assumption.label} must be at least ${assumption.validation.min}`)
          }
          
          if (assumption.validation.max !== undefined && value > assumption.validation.max) {
            throw new Error(`${assumption.label} must be at most ${assumption.validation.max}`)
          }
        }
      }
    }
  }

  /**
   * Calculate results based on assumptions and template configuration
   */
  private async calculateResults(
    assumptions: Record<string, any>,
    config: TemplateConfiguration
  ): Promise<Record<string, any>> {
    const results: Record<string, any> = {}

    // This is a simplified calculation engine
    // In a real implementation, you would have a more sophisticated formula parser
    for (const calculation of config.calculations) {
      try {
        // Simple formula evaluation (would need a proper expression parser)
        results[calculation.outputKey] = this.evaluateFormula(calculation.formula, assumptions, results)
      } catch (error) {
        this.logger.error('Failed to calculate result', { 
          error, 
          calculationId: calculation.id,
          formula: calculation.formula 
        })
        results[calculation.outputKey] = 0
      }
    }

    return results
  }

  /**
   * Simple formula evaluator (placeholder)
   */
  private evaluateFormula(
    formula: string,
    assumptions: Record<string, any>,
    results: Record<string, any>
  ): number {
    // This is a very simplified implementation
    // In production, you'd use a proper expression parser like mathjs
    
    // For now, return a mock calculation
    return Math.random() * 1000000
  }
}
