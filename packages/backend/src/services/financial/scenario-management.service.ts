import { PrismaClient } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'
import { FinancialCalculationEngine } from './calculation-engine.service'

export interface ScenarioDefinition {
  id: string
  name: string
  description?: string
  templateId: string
  baseAssumptions: Record<string, any>
  scenarios: ScenarioVariant[]
  sensitivityConfig?: SensitivityConfig
  monteCarloConfig?: MonteCarloConfig
  createdBy: string
  tenantId: string
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ScenarioVariant {
  id: string
  name: string
  description?: string
  probability?: number
  assumptions: Record<string, any>
  results?: Record<string, any>
  calculatedAt?: Date
}

export interface SensitivityConfig {
  variables: Array<{
    key: string
    label: string
    baseValue: number
    ranges: number[]
    distribution?: 'normal' | 'uniform' | 'triangular' | 'lognormal'
    parameters?: Record<string, number>
  }>
  outputMetrics: string[]
  correlations?: Array<{
    variable1: string
    variable2: string
    correlation: number
  }>
}

export interface MonteCarloConfig {
  iterations: number
  confidenceIntervals: number[]
  randomSeed?: number
  variables: Array<{
    key: string
    distribution: 'normal' | 'uniform' | 'triangular' | 'lognormal'
    parameters: Record<string, number>
  }>
  outputMetrics: string[]
}

export interface ScenarioComparison {
  scenarios: ScenarioVariant[]
  metrics: Array<{
    key: string
    label: string
    values: Array<{
      scenarioId: string
      value: number
      variance?: number
    }>
  }>
  summary: {
    bestCase: ScenarioVariant
    worstCase: ScenarioVariant
    mostLikely: ScenarioVariant
    probabilityWeighted: Record<string, number>
  }
}

export interface MonteCarloResult {
  iterations: number
  results: Array<{
    iteration: number
    inputs: Record<string, number>
    outputs: Record<string, number>
  }>
  statistics: Record<string, {
    mean: number
    median: number
    standardDeviation: number
    variance: number
    skewness: number
    kurtosis: number
    confidenceIntervals: Array<{
      level: number
      lower: number
      upper: number
    }>
    percentiles: Record<string, number>
  }>
  riskMetrics: {
    valueAtRisk: Record<string, number>
    conditionalValueAtRisk: Record<string, number>
    probabilityOfLoss: number
    expectedShortfall: number
  }
}

export class ScenarioManagementService {
  private prisma: PrismaClient
  private cache: CacheService
  private calculationEngine: FinancialCalculationEngine
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.calculationEngine = new FinancialCalculationEngine(prisma, cache)
    this.logger = new Logger('ScenarioManagementService')
  }

  /**
   * Create scenario definition
   */
  async createScenarioDefinition(
    definition: Omit<ScenarioDefinition, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<ScenarioDefinition> {
    try {
      this.logger.info('Creating scenario definition', { name: definition.name, userId })

      // Validate template exists
      const template = await this.prisma.valuationTemplate.findFirst({
        where: { id: definition.templateId, tenantId: definition.tenantId }
      })

      if (!template) {
        throw new Error('Valuation template not found')
      }

      // Create scenario definition
      const scenarioDefinition = await this.prisma.scenarioDefinition.create({
        data: {
          ...definition,
          createdBy: userId,
          scenarios: definition.scenarios as any,
          sensitivityConfig: definition.sensitivityConfig as any,
          monteCarloConfig: definition.monteCarloConfig as any,
          baseAssumptions: definition.baseAssumptions as any
        }
      })

      // Calculate initial results for all scenarios
      await this.calculateScenarioResults(scenarioDefinition.id)

      this.logger.info('Scenario definition created', { scenarioId: scenarioDefinition.id })
      return scenarioDefinition as ScenarioDefinition
    } catch (error) {
      this.logger.error('Failed to create scenario definition', { error, definition })
      throw error
    }
  }

  /**
   * Calculate results for all scenarios in a definition
   */
  async calculateScenarioResults(scenarioDefinitionId: string): Promise<void> {
    try {
      const definition = await this.prisma.scenarioDefinition.findUnique({
        where: { id: scenarioDefinitionId },
        include: { template: true }
      })

      if (!definition) {
        throw new Error('Scenario definition not found')
      }

      const scenarios = definition.scenarios as ScenarioVariant[]
      const updatedScenarios: ScenarioVariant[] = []

      for (const scenario of scenarios) {
        try {
          // Merge base assumptions with scenario-specific assumptions
          const combinedAssumptions = {
            ...definition.baseAssumptions,
            ...scenario.assumptions
          }

          // Calculate results based on template methodology
          const results = await this.calculateScenarioResult(
            definition.template as any,
            combinedAssumptions
          )

          updatedScenarios.push({
            ...scenario,
            results,
            calculatedAt: new Date()
          })
        } catch (error) {
          this.logger.error('Failed to calculate scenario result', { 
            error, 
            scenarioId: scenario.id 
          })
          
          // Keep original scenario if calculation fails
          updatedScenarios.push(scenario)
        }
      }

      // Update scenario definition with calculated results
      await this.prisma.scenarioDefinition.update({
        where: { id: scenarioDefinitionId },
        data: {
          scenarios: updatedScenarios as any,
          updatedAt: new Date()
        }
      })

      this.logger.info('Scenario results calculated', { 
        scenarioDefinitionId,
        scenariosCalculated: updatedScenarios.length 
      })
    } catch (error) {
      this.logger.error('Failed to calculate scenario results', { error, scenarioDefinitionId })
      throw error
    }
  }

  /**
   * Calculate result for a single scenario
   */
  private async calculateScenarioResult(
    template: any,
    assumptions: Record<string, any>
  ): Promise<Record<string, any>> {
    const templateConfig = template.configuration

    switch (template.category) {
      case 'DCF':
        return this.calculationEngine.calculateDCF(assumptions)
      
      case 'COMPARABLE_COMPANY':
        return this.calculationEngine.calculateCCA(assumptions)
      
      case 'PRECEDENT_TRANSACTION':
        return this.calculationEngine.calculatePTA(assumptions)
      
      case 'LBO':
        return this.calculationEngine.calculateLBO(assumptions)
      
      default:
        // Generic calculation based on template configuration
        return this.calculateGenericResult(templateConfig, assumptions)
    }
  }

  /**
   * Generic calculation for custom templates
   */
  private calculateGenericResult(
    templateConfig: any,
    assumptions: Record<string, any>
  ): Record<string, any> {
    const results: Record<string, any> = {}

    // Simple formula evaluation for custom templates
    for (const calculation of templateConfig.calculations || []) {
      try {
        results[calculation.outputKey] = this.evaluateFormula(
          calculation.formula,
          assumptions,
          results
        )
      } catch (error) {
        this.logger.error('Failed to evaluate formula', { 
          error, 
          formula: calculation.formula 
        })
        results[calculation.outputKey] = 0
      }
    }

    return results
  }

  /**
   * Simple formula evaluator
   */
  private evaluateFormula(
    formula: string,
    assumptions: Record<string, any>,
    results: Record<string, any>
  ): number {
    // This is a simplified implementation
    // In production, you'd use a proper expression parser
    
    // For now, return a calculated value based on common patterns
    if (formula.includes('revenue') && formula.includes('growth')) {
      const baseRevenue = assumptions.baseRevenue || 100000000
      const growthRate = assumptions.revenueGrowthY1 || 0.1
      return baseRevenue * (1 + growthRate)
    }
    
    if (formula.includes('ebitda') && formula.includes('margin')) {
      const revenue = results.projectedRevenues || assumptions.baseRevenue || 100000000
      const margin = assumptions.ebitdaMargin || 0.25
      return revenue * margin
    }
    
    // Default calculation
    return Math.random() * 1000000
  }

  /**
   * Compare scenarios
   */
  async compareScenarios(scenarioDefinitionId: string): Promise<ScenarioComparison> {
    try {
      const definition = await this.prisma.scenarioDefinition.findUnique({
        where: { id: scenarioDefinitionId }
      })

      if (!definition) {
        throw new Error('Scenario definition not found')
      }

      const scenarios = definition.scenarios as ScenarioVariant[]
      
      // Extract metrics for comparison
      const metricKeys = new Set<string>()
      scenarios.forEach(scenario => {
        if (scenario.results) {
          Object.keys(scenario.results).forEach(key => metricKeys.add(key))
        }
      })

      const metrics = Array.from(metricKeys).map(key => ({
        key,
        label: this.formatMetricLabel(key),
        values: scenarios.map(scenario => ({
          scenarioId: scenario.id,
          value: scenario.results?.[key] || 0,
          variance: this.calculateVariance(scenario.results?.[key], scenarios, key)
        }))
      }))

      // Calculate summary statistics
      const summary = this.calculateScenarioSummary(scenarios)

      return {
        scenarios,
        metrics,
        summary
      }
    } catch (error) {
      this.logger.error('Failed to compare scenarios', { error, scenarioDefinitionId })
      throw error
    }
  }

  /**
   * Perform Monte Carlo simulation
   */
  async performMonteCarloSimulation(
    scenarioDefinitionId: string,
    config?: Partial<MonteCarloConfig>
  ): Promise<MonteCarloResult> {
    try {
      const definition = await this.prisma.scenarioDefinition.findUnique({
        where: { id: scenarioDefinitionId },
        include: { template: true }
      })

      if (!definition) {
        throw new Error('Scenario definition not found')
      }

      const monteCarloConfig = {
        ...definition.monteCarloConfig,
        ...config
      } as MonteCarloConfig

      if (!monteCarloConfig.variables || monteCarloConfig.variables.length === 0) {
        throw new Error('Monte Carlo configuration requires variables')
      }

      const results: MonteCarloResult['results'] = []
      
      // Set random seed for reproducibility
      if (monteCarloConfig.randomSeed) {
        Math.random = this.seededRandom(monteCarloConfig.randomSeed)
      }

      // Run Monte Carlo iterations
      for (let i = 0; i < monteCarloConfig.iterations; i++) {
        const inputs: Record<string, number> = {}
        
        // Generate random inputs based on distributions
        for (const variable of monteCarloConfig.variables) {
          inputs[variable.key] = this.generateRandomValue(
            variable.distribution,
            variable.parameters
          )
        }

        // Combine with base assumptions
        const combinedAssumptions = {
          ...definition.baseAssumptions,
          ...inputs
        }

        // Calculate outputs
        const outputs = await this.calculateScenarioResult(
          definition.template,
          combinedAssumptions
        )

        results.push({
          iteration: i + 1,
          inputs,
          outputs
        })
      }

      // Calculate statistics
      const statistics = this.calculateMonteCarloStatistics(
        results,
        monteCarloConfig.outputMetrics,
        monteCarloConfig.confidenceIntervals
      )

      // Calculate risk metrics
      const riskMetrics = this.calculateRiskMetrics(results, monteCarloConfig.outputMetrics)

      return {
        iterations: monteCarloConfig.iterations,
        results,
        statistics,
        riskMetrics
      }
    } catch (error) {
      this.logger.error('Failed to perform Monte Carlo simulation', { 
        error, 
        scenarioDefinitionId 
      })
      throw error
    }
  }

  /**
   * Generate random value based on distribution
   */
  private generateRandomValue(
    distribution: string,
    parameters: Record<string, number>
  ): number {
    switch (distribution) {
      case 'normal':
        return this.normalRandom(parameters.mean || 0, parameters.stdDev || 1)
      
      case 'uniform':
        return this.uniformRandom(parameters.min || 0, parameters.max || 1)
      
      case 'triangular':
        return this.triangularRandom(
          parameters.min || 0,
          parameters.max || 1,
          parameters.mode || 0.5
        )
      
      case 'lognormal':
        return Math.exp(this.normalRandom(parameters.mu || 0, parameters.sigma || 1))
      
      default:
        return Math.random()
    }
  }

  /**
   * Normal distribution random number generator
   */
  private normalRandom(mean: number, stdDev: number): number {
    // Box-Muller transformation
    const u1 = Math.random()
    const u2 = Math.random()
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2)
    return z0 * stdDev + mean
  }

  /**
   * Uniform distribution random number generator
   */
  private uniformRandom(min: number, max: number): number {
    return Math.random() * (max - min) + min
  }

  /**
   * Triangular distribution random number generator
   */
  private triangularRandom(min: number, max: number, mode: number): number {
    const u = Math.random()
    const f = (mode - min) / (max - min)
    
    if (u < f) {
      return min + Math.sqrt(u * (max - min) * (mode - min))
    } else {
      return max - Math.sqrt((1 - u) * (max - min) * (max - mode))
    }
  }

  /**
   * Seeded random number generator
   */
  private seededRandom(seed: number): () => number {
    let state = seed
    return () => {
      state = (state * 1664525 + 1013904223) % 4294967296
      return state / 4294967296
    }
  }

  /**
   * Calculate Monte Carlo statistics
   */
  private calculateMonteCarloStatistics(
    results: MonteCarloResult['results'],
    outputMetrics: string[],
    confidenceIntervals: number[]
  ): MonteCarloResult['statistics'] {
    const statistics: MonteCarloResult['statistics'] = {}

    for (const metric of outputMetrics) {
      const values = results.map(r => r.outputs[metric]).filter(v => !isNaN(v)).sort((a, b) => a - b)
      
      if (values.length === 0) continue

      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      const stdDev = Math.sqrt(variance)

      statistics[metric] = {
        mean,
        median: values[Math.floor(values.length / 2)],
        standardDeviation: stdDev,
        variance,
        skewness: this.calculateSkewness(values, mean, stdDev),
        kurtosis: this.calculateKurtosis(values, mean, stdDev),
        confidenceIntervals: confidenceIntervals.map(level => ({
          level,
          lower: values[Math.floor(values.length * (1 - level / 100) / 2)],
          upper: values[Math.floor(values.length * (1 + level / 100) / 2)]
        })),
        percentiles: {
          p5: values[Math.floor(values.length * 0.05)],
          p10: values[Math.floor(values.length * 0.10)],
          p25: values[Math.floor(values.length * 0.25)],
          p75: values[Math.floor(values.length * 0.75)],
          p90: values[Math.floor(values.length * 0.90)],
          p95: values[Math.floor(values.length * 0.95)]
        }
      }
    }

    return statistics
  }

  /**
   * Calculate risk metrics
   */
  private calculateRiskMetrics(
    results: MonteCarloResult['results'],
    outputMetrics: string[]
  ): MonteCarloResult['riskMetrics'] {
    // Simplified risk metrics calculation
    const primaryMetric = outputMetrics[0]
    const values = results.map(r => r.outputs[primaryMetric]).filter(v => !isNaN(v)).sort((a, b) => a - b)
    
    const var95 = values[Math.floor(values.length * 0.05)]
    const var99 = values[Math.floor(values.length * 0.01)]
    
    return {
      valueAtRisk: {
        '95%': var95,
        '99%': var99
      },
      conditionalValueAtRisk: {
        '95%': values.slice(0, Math.floor(values.length * 0.05)).reduce((sum, val) => sum + val, 0) / Math.floor(values.length * 0.05),
        '99%': values.slice(0, Math.floor(values.length * 0.01)).reduce((sum, val) => sum + val, 0) / Math.floor(values.length * 0.01)
      },
      probabilityOfLoss: values.filter(v => v < 0).length / values.length,
      expectedShortfall: values.filter(v => v < 0).reduce((sum, val) => sum + val, 0) / values.filter(v => v < 0).length || 0
    }
  }

  /**
   * Calculate skewness
   */
  private calculateSkewness(values: number[], mean: number, stdDev: number): number {
    const n = values.length
    const skewness = values.reduce((sum, val) => sum + Math.pow((val - mean) / stdDev, 3), 0) / n
    return skewness
  }

  /**
   * Calculate kurtosis
   */
  private calculateKurtosis(values: number[], mean: number, stdDev: number): number {
    const n = values.length
    const kurtosis = values.reduce((sum, val) => sum + Math.pow((val - mean) / stdDev, 4), 0) / n - 3
    return kurtosis
  }

  /**
   * Calculate variance for a metric across scenarios
   */
  private calculateVariance(value: number, scenarios: ScenarioVariant[], metricKey: string): number {
    const values = scenarios.map(s => s.results?.[metricKey] || 0).filter(v => !isNaN(v))
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    return ((value - mean) / mean) * 100
  }

  /**
   * Format metric label for display
   */
  private formatMetricLabel(key: string): string {
    return key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim()
  }

  /**
   * Calculate scenario summary
   */
  private calculateScenarioSummary(scenarios: ScenarioVariant[]): ScenarioComparison['summary'] {
    // Simplified summary calculation
    const primaryMetric = 'enterpriseValue' // Assume this is the primary metric
    
    let bestCase = scenarios[0]
    let worstCase = scenarios[0]
    let mostLikely = scenarios.find(s => s.name.toLowerCase().includes('base')) || scenarios[0]

    for (const scenario of scenarios) {
      const value = scenario.results?.[primaryMetric] || 0
      
      if (value > (bestCase.results?.[primaryMetric] || 0)) {
        bestCase = scenario
      }
      
      if (value < (worstCase.results?.[primaryMetric] || 0)) {
        worstCase = scenario
      }
    }

    // Calculate probability-weighted average
    const totalProbability = scenarios.reduce((sum, s) => sum + (s.probability || 1), 0)
    const probabilityWeighted: Record<string, number> = {}
    
    const metricKeys = new Set<string>()
    scenarios.forEach(scenario => {
      if (scenario.results) {
        Object.keys(scenario.results).forEach(key => metricKeys.add(key))
      }
    })

    for (const key of metricKeys) {
      probabilityWeighted[key] = scenarios.reduce((sum, scenario) => {
        const weight = (scenario.probability || 1) / totalProbability
        return sum + (scenario.results?.[key] || 0) * weight
      }, 0)
    }

    return {
      bestCase,
      worstCase,
      mostLikely,
      probabilityWeighted
    }
  }
}
