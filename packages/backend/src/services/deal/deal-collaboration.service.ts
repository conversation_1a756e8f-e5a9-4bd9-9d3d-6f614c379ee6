import { PrismaClient, TaskStatus, Task<PERSON><PERSON><PERSON>, MilestoneStatus } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface DealTeamMember {
  id: string
  dealId: string
  userId: string
  role: string
  permissions: string[]
  joinedAt: Date
  isActive: boolean
  user: {
    id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
  }
}

export interface DealTask {
  id: string
  dealId: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  assignedTo?: string
  createdBy: string
  dueDate?: Date
  completedAt?: Date
  estimatedHours?: number
  actualHours?: number
  tags: string[]
  dependencies: string[]
  attachments: string[]
  comments: TaskComment[]
  createdAt: Date
  updatedAt: Date
}

export interface TaskComment {
  id: string
  taskId: string
  userId: string
  content: string
  createdAt: Date
  user: {
    firstName: string
    lastName: string
    email: string
  }
}

export interface DealMilestone {
  id: string
  dealId: string
  title: string
  description?: string
  status: MilestoneStatus
  targetDate: Date
  completedDate?: Date
  progress: number
  tasks: string[]
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateTaskRequest {
  dealId: string
  title: string
  description?: string
  priority?: TaskPriority
  assignedTo?: string
  dueDate?: Date
  estimatedHours?: number
  tags?: string[]
  dependencies?: string[]
}

export interface CreateMilestoneRequest {
  dealId: string
  title: string
  description?: string
  targetDate: Date
  tasks?: string[]
}

export class DealCollaborationService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DealCollaborationService')
  }

  /**
   * Add team member to deal
   */
  async addTeamMember(
    dealId: string,
    userId: string,
    role: string,
    permissions: string[],
    addedBy: string,
    tenantId: string
  ): Promise<DealTeamMember> {
    try {
      this.logger.info('Adding team member to deal', { dealId, userId, role })

      // Verify deal exists
      const deal = await this.prisma.deal.findFirst({
        where: { id: dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found')
      }

      // Check if user is already a team member
      const existingMember = await this.prisma.dealTeamMember.findFirst({
        where: { dealId, userId }
      })

      if (existingMember) {
        throw new Error('User is already a team member')
      }

      const teamMember = await this.prisma.dealTeamMember.create({
        data: {
          dealId,
          userId,
          role,
          permissions,
          joinedAt: new Date(),
          isActive: true
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.del(`deal-team:${dealId}`)

      this.logger.info('Team member added successfully', { dealId, userId })
      return teamMember as DealTeamMember
    } catch (error) {
      this.logger.error('Failed to add team member', { error, dealId, userId })
      throw error
    }
  }

  /**
   * Get deal team members
   */
  async getDealTeam(dealId: string, tenantId: string): Promise<DealTeamMember[]> {
    try {
      const cacheKey = `deal-team:${dealId}`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      // Verify deal access
      const deal = await this.prisma.deal.findFirst({
        where: { id: dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found')
      }

      const teamMembers = await this.prisma.dealTeamMember.findMany({
        where: { dealId, isActive: true },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true
            }
          }
        },
        orderBy: { joinedAt: 'asc' }
      })

      // Cache for 10 minutes
      await this.cache.set(cacheKey, JSON.stringify(teamMembers), 600)

      return teamMembers as DealTeamMember[]
    } catch (error) {
      this.logger.error('Failed to get deal team', { error, dealId })
      throw error
    }
  }

  /**
   * Create a new task
   */
  async createTask(
    data: CreateTaskRequest,
    createdBy: string,
    tenantId: string
  ): Promise<DealTask> {
    try {
      this.logger.info('Creating deal task', { dealId: data.dealId, title: data.title })

      // Verify deal exists
      const deal = await this.prisma.deal.findFirst({
        where: { id: data.dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found')
      }

      const task = await this.prisma.dealTask.create({
        data: {
          dealId: data.dealId,
          title: data.title,
          description: data.description,
          status: TaskStatus.TODO,
          priority: data.priority || TaskPriority.MEDIUM,
          assignedTo: data.assignedTo,
          createdBy,
          dueDate: data.dueDate,
          estimatedHours: data.estimatedHours,
          tags: data.tags || [],
          dependencies: data.dependencies || []
        },
        include: {
          assignedToUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.del(`deal-tasks:${data.dealId}`)

      this.logger.info('Task created successfully', { taskId: task.id })
      return task as DealTask
    } catch (error) {
      this.logger.error('Failed to create task', { error, dealId: data.dealId })
      throw error
    }
  }

  /**
   * Get deal tasks
   */
  async getDealTasks(
    dealId: string,
    tenantId: string,
    filters?: {
      status?: TaskStatus[]
      assignedTo?: string
      priority?: TaskPriority[]
    }
  ): Promise<DealTask[]> {
    try {
      const cacheKey = `deal-tasks:${dealId}:${JSON.stringify(filters || {})}`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      // Verify deal access
      const deal = await this.prisma.deal.findFirst({
        where: { id: dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found')
      }

      const whereClause: any = { dealId }

      if (filters?.status?.length) {
        whereClause.status = { in: filters.status }
      }

      if (filters?.assignedTo) {
        whereClause.assignedTo = filters.assignedTo
      }

      if (filters?.priority?.length) {
        whereClause.priority = { in: filters.priority }
      }

      const tasks = await this.prisma.dealTask.findMany({
        where: whereClause,
        include: {
          assignedToUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          comments: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        },
        orderBy: [
          { priority: 'desc' },
          { dueDate: 'asc' },
          { createdAt: 'desc' }
        ]
      })

      // Cache for 5 minutes
      await this.cache.set(cacheKey, JSON.stringify(tasks), 300)

      return tasks as DealTask[]
    } catch (error) {
      this.logger.error('Failed to get deal tasks', { error, dealId })
      throw error
    }
  }

  /**
   * Update task status
   */
  async updateTaskStatus(
    taskId: string,
    status: TaskStatus,
    userId: string,
    tenantId: string
  ): Promise<DealTask> {
    try {
      const task = await this.prisma.dealTask.findFirst({
        where: {
          id: taskId,
          deal: { tenantId }
        }
      })

      if (!task) {
        throw new Error('Task not found')
      }

      const updateData: any = { status }

      if (status === TaskStatus.DONE) {
        updateData.completedAt = new Date()
      }

      const updatedTask = await this.prisma.dealTask.update({
        where: { id: taskId },
        data: updateData,
        include: {
          assignedToUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.del(`deal-tasks:${task.dealId}`)

      return updatedTask as DealTask
    } catch (error) {
      this.logger.error('Failed to update task status', { error, taskId })
      throw error
    }
  }

  /**
   * Create milestone
   */
  async createMilestone(
    data: CreateMilestoneRequest,
    createdBy: string,
    tenantId: string
  ): Promise<DealMilestone> {
    try {
      this.logger.info('Creating deal milestone', { dealId: data.dealId, title: data.title })

      // Verify deal exists
      const deal = await this.prisma.deal.findFirst({
        where: { id: data.dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found')
      }

      const milestone = await this.prisma.dealMilestone.create({
        data: {
          dealId: data.dealId,
          title: data.title,
          description: data.description,
          status: MilestoneStatus.PENDING,
          targetDate: data.targetDate,
          progress: 0,
          tasks: data.tasks || [],
          createdBy
        }
      })

      // Clear cache
      await this.cache.del(`deal-milestones:${data.dealId}`)

      this.logger.info('Milestone created successfully', { milestoneId: milestone.id })
      return milestone as DealMilestone
    } catch (error) {
      this.logger.error('Failed to create milestone', { error, dealId: data.dealId })
      throw error
    }
  }

  /**
   * Get deal milestones
   */
  async getDealMilestones(dealId: string, tenantId: string): Promise<DealMilestone[]> {
    try {
      const cacheKey = `deal-milestones:${dealId}`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      // Verify deal access
      const deal = await this.prisma.deal.findFirst({
        where: { id: dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found')
      }

      const milestones = await this.prisma.dealMilestone.findMany({
        where: { dealId },
        orderBy: { targetDate: 'asc' }
      })

      // Cache for 10 minutes
      await this.cache.set(cacheKey, JSON.stringify(milestones), 600)

      return milestones as DealMilestone[]
    } catch (error) {
      this.logger.error('Failed to get deal milestones', { error, dealId })
      throw error
    }
  }

  /**
   * Add comment to task
   */
  async addTaskComment(
    taskId: string,
    content: string,
    userId: string,
    tenantId: string
  ): Promise<TaskComment> {
    try {
      const task = await this.prisma.dealTask.findFirst({
        where: {
          id: taskId,
          deal: { tenantId }
        }
      })

      if (!task) {
        throw new Error('Task not found')
      }

      const comment = await this.prisma.taskComment.create({
        data: {
          taskId,
          userId,
          content
        },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // Clear cache
      await this.cache.del(`deal-tasks:${task.dealId}`)

      return comment as TaskComment
    } catch (error) {
      this.logger.error('Failed to add task comment', { error, taskId })
      throw error
    }
  }
}
