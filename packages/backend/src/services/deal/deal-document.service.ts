import { PrismaClient, DocumentType, DocumentStatus } from '@prisma/client'
import { Logger } from '@/shared/utils/logger'
import { CacheService } from '@/services/cache.service'

export interface DealDocument {
  id: string
  dealId: string
  name: string
  description?: string
  type: DocumentType
  status: DocumentStatus
  version: number
  fileSize: number
  mimeType: string
  filePath: string
  uploadedBy: string
  uploadedAt: Date
  isConfidential: boolean
  accessLevel: 'PUBLIC' | 'TEAM' | 'RESTRICTED'
  tags: string[]
  metadata?: Record<string, any>
  parentDocumentId?: string
  checksum: string
  expiresAt?: Date
  downloadCount: number
  lastAccessedAt?: Date
}

export interface CreateDocumentRequest {
  dealId: string
  name: string
  description?: string
  type: DocumentType
  fileSize: number
  mimeType: string
  filePath: string
  isConfidential?: boolean
  accessLevel?: 'PUBLIC' | 'TEAM' | 'RESTRICTED'
  tags?: string[]
  metadata?: Record<string, any>
  parentDocumentId?: string
  expiresAt?: Date
}

export interface DocumentVersion {
  id: string
  documentId: string
  version: number
  filePath: string
  fileSize: number
  checksum: string
  uploadedBy: string
  uploadedAt: Date
  changeLog?: string
}

export interface DocumentAccess {
  id: string
  documentId: string
  userId: string
  accessType: 'VIEW' | 'DOWNLOAD' | 'EDIT' | 'DELETE'
  accessedAt: Date
  ipAddress?: string
  userAgent?: string
}

export class DealDocumentService {
  private prisma: PrismaClient
  private cache: CacheService
  private logger: Logger

  constructor(prisma: PrismaClient, cache: CacheService) {
    this.prisma = prisma
    this.cache = cache
    this.logger = new Logger('DealDocumentService')
  }

  /**
   * Upload a new document to a deal
   */
  async uploadDocument(
    data: CreateDocumentRequest,
    userId: string,
    tenantId: string
  ): Promise<DealDocument> {
    try {
      this.logger.info('Uploading document to deal', { dealId: data.dealId, name: data.name })

      // Verify deal exists and user has access
      const deal = await this.prisma.deal.findFirst({
        where: { id: data.dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found or access denied')
      }

      // Check if this is a new version of existing document
      let version = 1
      if (data.parentDocumentId) {
        const parentDoc = await this.prisma.dealDocument.findUnique({
          where: { id: data.parentDocumentId }
        })
        if (parentDoc) {
          version = parentDoc.version + 1
        }
      }

      // Create document record
      const document = await this.prisma.dealDocument.create({
        data: {
          dealId: data.dealId,
          name: data.name,
          description: data.description,
          type: data.type,
          status: DocumentStatus.ACTIVE,
          version,
          fileSize: data.fileSize,
          mimeType: data.mimeType,
          filePath: data.filePath,
          uploadedBy: userId,
          isConfidential: data.isConfidential || false,
          accessLevel: data.accessLevel || 'TEAM',
          tags: data.tags || [],
          metadata: data.metadata || {},
          parentDocumentId: data.parentDocumentId,
          checksum: await this.calculateChecksum(data.filePath),
          expiresAt: data.expiresAt,
          downloadCount: 0
        }
      })

      // Create version history entry
      await this.prisma.documentVersion.create({
        data: {
          documentId: document.id,
          version,
          filePath: data.filePath,
          fileSize: data.fileSize,
          checksum: document.checksum,
          uploadedBy: userId,
          changeLog: version > 1 ? 'New version uploaded' : 'Initial upload'
        }
      })

      // Log document activity
      await this.logDocumentAccess(document.id, userId, 'UPLOAD')

      // Clear cache
      await this.cache.del(`deal-documents:${data.dealId}`)

      this.logger.info('Document uploaded successfully', { documentId: document.id })
      return document as DealDocument
    } catch (error) {
      this.logger.error('Failed to upload document', { error, dealId: data.dealId })
      throw error
    }
  }

  /**
   * Get all documents for a deal
   */
  async getDealDocuments(
    dealId: string,
    tenantId: string,
    filters?: {
      type?: DocumentType[]
      status?: DocumentStatus[]
      isConfidential?: boolean
      tags?: string[]
    }
  ): Promise<DealDocument[]> {
    try {
      const cacheKey = `deal-documents:${dealId}:${JSON.stringify(filters || {})}`
      const cached = await this.cache.get(cacheKey)
      
      if (cached) {
        return JSON.parse(cached)
      }

      // Verify deal access
      const deal = await this.prisma.deal.findFirst({
        where: { id: dealId, tenantId }
      })

      if (!deal) {
        throw new Error('Deal not found or access denied')
      }

      const whereClause: any = { dealId }

      if (filters?.type?.length) {
        whereClause.type = { in: filters.type }
      }

      if (filters?.status?.length) {
        whereClause.status = { in: filters.status }
      }

      if (filters?.isConfidential !== undefined) {
        whereClause.isConfidential = filters.isConfidential
      }

      if (filters?.tags?.length) {
        whereClause.tags = { hasSome: filters.tags }
      }

      const documents = await this.prisma.dealDocument.findMany({
        where: whereClause,
        include: {
          uploadedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          versions: {
            orderBy: { version: 'desc' },
            take: 5
          }
        },
        orderBy: { uploadedAt: 'desc' }
      })

      // Cache for 5 minutes
      await this.cache.set(cacheKey, JSON.stringify(documents), 300)

      return documents as DealDocument[]
    } catch (error) {
      this.logger.error('Failed to get deal documents', { error, dealId })
      throw error
    }
  }

  /**
   * Download a document
   */
  async downloadDocument(
    documentId: string,
    userId: string,
    tenantId: string
  ): Promise<{ filePath: string; document: DealDocument }> {
    try {
      this.logger.info('Downloading document', { documentId, userId })

      const document = await this.prisma.dealDocument.findFirst({
        where: {
          id: documentId,
          deal: { tenantId }
        },
        include: {
          deal: true
        }
      })

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      // Check if document has expired
      if (document.expiresAt && new Date() > document.expiresAt) {
        throw new Error('Document has expired')
      }

      // Update download count and last accessed
      await this.prisma.dealDocument.update({
        where: { id: documentId },
        data: {
          downloadCount: { increment: 1 },
          lastAccessedAt: new Date()
        }
      })

      // Log document access
      await this.logDocumentAccess(documentId, userId, 'DOWNLOAD')

      return {
        filePath: document.filePath,
        document: document as DealDocument
      }
    } catch (error) {
      this.logger.error('Failed to download document', { error, documentId })
      throw error
    }
  }

  /**
   * Update document metadata
   */
  async updateDocument(
    documentId: string,
    updates: {
      name?: string
      description?: string
      tags?: string[]
      isConfidential?: boolean
      accessLevel?: 'PUBLIC' | 'TEAM' | 'RESTRICTED'
      expiresAt?: Date
    },
    userId: string,
    tenantId: string
  ): Promise<DealDocument> {
    try {
      const document = await this.prisma.dealDocument.findFirst({
        where: {
          id: documentId,
          deal: { tenantId }
        }
      })

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      const updatedDocument = await this.prisma.dealDocument.update({
        where: { id: documentId },
        data: {
          ...updates,
          updatedAt: new Date()
        }
      })

      // Log document access
      await this.logDocumentAccess(documentId, userId, 'EDIT')

      // Clear cache
      await this.cache.del(`deal-documents:${document.dealId}`)

      return updatedDocument as DealDocument
    } catch (error) {
      this.logger.error('Failed to update document', { error, documentId })
      throw error
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(
    documentId: string,
    userId: string,
    tenantId: string
  ): Promise<void> {
    try {
      const document = await this.prisma.dealDocument.findFirst({
        where: {
          id: documentId,
          deal: { tenantId }
        }
      })

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      // Soft delete - mark as deleted
      await this.prisma.dealDocument.update({
        where: { id: documentId },
        data: {
          status: DocumentStatus.DELETED,
          deletedAt: new Date(),
          deletedBy: userId
        }
      })

      // Log document access
      await this.logDocumentAccess(documentId, userId, 'DELETE')

      // Clear cache
      await this.cache.del(`deal-documents:${document.dealId}`)

      this.logger.info('Document deleted successfully', { documentId })
    } catch (error) {
      this.logger.error('Failed to delete document', { error, documentId })
      throw error
    }
  }

  /**
   * Get document access history
   */
  async getDocumentAccessHistory(
    documentId: string,
    tenantId: string
  ): Promise<DocumentAccess[]> {
    try {
      const document = await this.prisma.dealDocument.findFirst({
        where: {
          id: documentId,
          deal: { tenantId }
        }
      })

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      const accessHistory = await this.prisma.documentAccess.findMany({
        where: { documentId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        },
        orderBy: { accessedAt: 'desc' },
        take: 100
      })

      return accessHistory as DocumentAccess[]
    } catch (error) {
      this.logger.error('Failed to get document access history', { error, documentId })
      throw error
    }
  }

  /**
   * Private helper methods
   */
  private async calculateChecksum(filePath: string): Promise<string> {
    // In a real implementation, this would calculate the actual file checksum
    // For now, return a placeholder
    return `checksum_${Date.now()}`
  }

  private async logDocumentAccess(
    documentId: string,
    userId: string,
    accessType: 'VIEW' | 'DOWNLOAD' | 'EDIT' | 'DELETE' | 'UPLOAD'
  ): Promise<void> {
    try {
      await this.prisma.documentAccess.create({
        data: {
          documentId,
          userId,
          accessType,
          accessedAt: new Date()
        }
      })
    } catch (error) {
      this.logger.error('Failed to log document access', { error, documentId, userId })
      // Don't throw error for logging failures
    }
  }
}
