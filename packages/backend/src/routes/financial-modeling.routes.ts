import { Router } from 'express'
import { body, query } from 'express-validator'
import { PrismaClient } from '@prisma/client'
import { CacheService } from '@/services/cache.service'
import { FinancialModelingController } from '@/controllers/financial-modeling.controller'
import { rbacMiddleware } from '@/middleware/rbac.middleware'
import { validateRequest } from '@/middleware/validation.middleware'

export function createFinancialModelingRoutes(prisma: PrismaClient, cache: CacheService): Router {
  const router = Router()
  const financialController = new FinancialModelingController(prisma, cache)

  // DCF Calculation
  router.post(
    '/dcf/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('baseRevenue')
        .isNumeric()
        .withMessage('Base revenue must be a number')
        .custom(value => value > 0)
        .withMessage('Base revenue must be positive'),
      body('revenueGrowth')
        .isArray({ min: 1 })
        .withMessage('Revenue growth must be an array with at least one value'),
      body('revenueGrowth.*')
        .isNumeric()
        .withMessage('Revenue growth rates must be numbers'),
      body('discountRate')
        .isNumeric()
        .withMessage('Discount rate must be a number')
        .custom(value => value > 0 && value < 1)
        .withMessage('Discount rate must be between 0 and 1'),
      body('terminalGrowthRate')
        .optional()
        .isNumeric()
        .withMessage('Terminal growth rate must be a number')
        .custom(value => value >= 0 && value < 0.1)
        .withMessage('Terminal growth rate must be between 0 and 0.1'),
      body('ebitdaMargin')
        .optional()
        .isNumeric()
        .withMessage('EBITDA margin must be a number'),
      body('taxRate')
        .optional()
        .isNumeric()
        .withMessage('Tax rate must be a number')
    ],
    validateRequest,
    financialController.calculateDCF
  )

  // Comparable Company Analysis
  router.post(
    '/cca/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('targetMetrics')
        .isObject()
        .withMessage('Target metrics must be an object'),
      body('targetMetrics.revenue')
        .isNumeric()
        .withMessage('Target revenue must be a number'),
      body('targetMetrics.ebitda')
        .isNumeric()
        .withMessage('Target EBITDA must be a number'),
      body('comparableCompanies')
        .isArray({ min: 1 })
        .withMessage('Must provide at least one comparable company'),
      body('adjustments')
        .optional()
        .isObject()
        .withMessage('Adjustments must be an object')
    ],
    validateRequest,
    financialController.calculateCCA
  )

  // Precedent Transaction Analysis
  router.post(
    '/pta/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('targetMetrics')
        .isObject()
        .withMessage('Target metrics must be an object'),
      body('targetMetrics.revenue')
        .isNumeric()
        .withMessage('Target revenue must be a number'),
      body('targetMetrics.ebitda')
        .isNumeric()
        .withMessage('Target EBITDA must be a number'),
      body('precedentTransactions')
        .isArray({ min: 1 })
        .withMessage('Must provide at least one precedent transaction'),
      body('filters')
        .optional()
        .isObject()
        .withMessage('Filters must be an object'),
      body('adjustments')
        .optional()
        .isObject()
        .withMessage('Adjustments must be an object')
    ],
    validateRequest,
    financialController.calculatePTA
  )

  // LBO Analysis
  router.post(
    '/lbo/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('targetMetrics')
        .isObject()
        .withMessage('Target metrics must be an object'),
      body('dealStructure')
        .isObject()
        .withMessage('Deal structure must be an object'),
      body('dealStructure.purchasePrice')
        .isNumeric()
        .withMessage('Purchase price must be a number'),
      body('dealStructure.debtFinancing')
        .isNumeric()
        .withMessage('Debt financing must be a number'),
      body('dealStructure.equityFinancing')
        .isNumeric()
        .withMessage('Equity financing must be a number'),
      body('assumptions')
        .isObject()
        .withMessage('Assumptions must be an object'),
      body('assumptions.exitMultiple')
        .isNumeric()
        .withMessage('Exit multiple must be a number'),
      body('assumptions.holdingPeriod')
        .isNumeric()
        .withMessage('Holding period must be a number'),
      body('financing')
        .isObject()
        .withMessage('Financing must be an object')
    ],
    validateRequest,
    financialController.calculateLBO
  )

  // Sensitivity Analysis
  router.post(
    '/sensitivity-analysis',
    rbacMiddleware(['financial:read']),
    [
      body('baseInputs')
        .isObject()
        .withMessage('Base inputs must be an object'),
      body('variables')
        .isArray({ min: 1 })
        .withMessage('Must provide at least one variable for sensitivity analysis'),
      body('variables.*.name')
        .notEmpty()
        .withMessage('Variable name is required'),
      body('variables.*.baseValue')
        .isNumeric()
        .withMessage('Variable base value must be a number'),
      body('variables.*.changes')
        .isArray()
        .withMessage('Variable changes must be an array')
    ],
    validateRequest,
    financialController.performSensitivityAnalysis
  )

  // Financial Ratios
  router.post(
    '/ratios/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('financialData')
        .isObject()
        .withMessage('Financial data must be an object'),
      body('financialData.revenue')
        .isNumeric()
        .withMessage('Revenue must be a number'),
      body('financialData.netIncome')
        .isNumeric()
        .withMessage('Net income must be a number'),
      body('financialData.totalAssets')
        .isNumeric()
        .withMessage('Total assets must be a number')
    ],
    validateRequest,
    financialController.calculateFinancialRatios
  )

  // WACC Calculation
  router.post(
    '/wacc/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('marketValueEquity')
        .isNumeric()
        .withMessage('Market value of equity must be a number'),
      body('marketValueDebt')
        .isNumeric()
        .withMessage('Market value of debt must be a number'),
      body('costOfEquity')
        .isNumeric()
        .withMessage('Cost of equity must be a number'),
      body('costOfDebt')
        .isNumeric()
        .withMessage('Cost of debt must be a number'),
      body('taxRate')
        .isNumeric()
        .withMessage('Tax rate must be a number')
        .custom(value => value >= 0 && value <= 1)
        .withMessage('Tax rate must be between 0 and 1')
    ],
    validateRequest,
    financialController.calculateWACC
  )

  // Beta Calculation
  router.post(
    '/beta/calculate',
    rbacMiddleware(['financial:read']),
    [
      body('stockReturns')
        .isArray({ min: 2 })
        .withMessage('Stock returns must be an array with at least 2 values'),
      body('stockReturns.*')
        .isNumeric()
        .withMessage('Stock returns must be numbers'),
      body('marketReturns')
        .isArray({ min: 2 })
        .withMessage('Market returns must be an array with at least 2 values'),
      body('marketReturns.*')
        .isNumeric()
        .withMessage('Market returns must be numbers')
    ],
    validateRequest,
    financialController.calculateBeta
  )

  // Valuation Templates
  router.get(
    '/templates',
    rbacMiddleware(['financial:read']),
    [
      query('category')
        .optional()
        .isIn(['DCF', 'COMPARABLE_COMPANY', 'PRECEDENT_TRANSACTION', 'LBO', 'ASSET_BASED', 'SUM_OF_PARTS'])
        .withMessage('Invalid category'),
      query('methodology')
        .optional()
        .isIn(['UNLEVERED_DCF', 'LEVERED_DCF', 'DIVIDEND_DISCOUNT', 'TRADING_MULTIPLES', 'TRANSACTION_MULTIPLES', 'LBO_ANALYSIS', 'NAV', 'LIQUIDATION'])
        .withMessage('Invalid methodology')
    ],
    validateRequest,
    financialController.getValuationTemplates
  )

  // Default Templates
  router.get(
    '/templates/defaults',
    rbacMiddleware(['financial:read']),
    financialController.getDefaultTemplates
  )

  // Create Valuation Template
  router.post(
    '/templates',
    rbacMiddleware(['financial:create']),
    [
      body('name')
        .notEmpty()
        .withMessage('Template name is required')
        .isLength({ max: 255 })
        .withMessage('Template name must be less than 255 characters'),
      body('category')
        .isIn(['DCF', 'COMPARABLE_COMPANY', 'PRECEDENT_TRANSACTION', 'LBO', 'ASSET_BASED', 'SUM_OF_PARTS'])
        .withMessage('Invalid category'),
      body('methodology')
        .isIn(['UNLEVERED_DCF', 'LEVERED_DCF', 'DIVIDEND_DISCOUNT', 'TRADING_MULTIPLES', 'TRANSACTION_MULTIPLES', 'LBO_ANALYSIS', 'NAV', 'LIQUIDATION'])
        .withMessage('Invalid methodology'),
      body('configuration')
        .isObject()
        .withMessage('Configuration must be an object'),
      body('configuration.assumptions')
        .isArray({ min: 1 })
        .withMessage('Must have at least one assumption category'),
      body('configuration.outputs')
        .isArray({ min: 1 })
        .withMessage('Must have at least one output definition'),
      body('isPublic')
        .optional()
        .isBoolean()
        .withMessage('isPublic must be a boolean')
    ],
    validateRequest,
    financialController.createValuationTemplate
  )

  // Create Scenario Model
  router.post(
    '/scenarios',
    rbacMiddleware(['financial:create']),
    [
      body('templateId')
        .isUUID()
        .withMessage('Template ID must be a valid UUID'),
      body('name')
        .notEmpty()
        .withMessage('Scenario name is required')
        .isLength({ max: 255 })
        .withMessage('Scenario name must be less than 255 characters'),
      body('assumptions')
        .isObject()
        .withMessage('Assumptions must be an object')
    ],
    validateRequest,
    financialController.createScenarioModel
  )

  return router
}
