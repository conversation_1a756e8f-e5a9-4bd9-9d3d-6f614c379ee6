import { Router } from 'express'
import { body, param, query } from 'express-validator'
import { PrismaClient } from '@prisma/client'
import { CacheService } from '@/services/cache.service'
import { CustomReportController } from '@/controllers/custom-report.controller'
import { rbacMiddleware } from '@/middleware/rbac.middleware'
import { validateRequest } from '@/middleware/validation.middleware'

export function createCustomReportRoutes(prisma: PrismaClient, cache: CacheService): Router {
  const router = Router()
  const customReportController = new CustomReportController(prisma, cache)

  // Validation middleware
  const reportIdValidation = [
    param('reportId')
      .isUUID()
      .withMessage('Report ID must be a valid UUID')
  ]

  const templateIdValidation = [
    param('templateId')
      .notEmpty()
      .withMessage('Template ID is required')
  ]

  const createReportValidation = [
    body('name')
      .notEmpty()
      .withMessage('Report name is required')
      .isLength({ max: 255 })
      .withMessage('Report name must be less than 255 characters'),
    body('description')
      .optional()
      .isString()
      .withMessage('Description must be a string'),
    body('configuration')
      .notEmpty()
      .withMessage('Report configuration is required')
      .isObject()
      .withMessage('Configuration must be an object'),
    body('configuration.dataSource')
      .notEmpty()
      .withMessage('Data source is required'),
    body('configuration.metrics')
      .isArray({ min: 1 })
      .withMessage('At least one metric is required'),
    body('configuration.visualization')
      .notEmpty()
      .withMessage('Visualization configuration is required'),
    body('isPublic')
      .optional()
      .isBoolean()
      .withMessage('isPublic must be a boolean')
  ]

  const executeReportValidation = [
    body('parameters')
      .optional()
      .isObject()
      .withMessage('Parameters must be an object')
  ]

  const exportReportValidation = [
    body('format')
      .optional()
      .isIn(['csv', 'xlsx', 'pdf'])
      .withMessage('Format must be one of: csv, xlsx, pdf'),
    body('parameters')
      .optional()
      .isObject()
      .withMessage('Parameters must be an object')
  ]

  const paginationValidation = [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ]

  // GET /api/reports/templates - Get available report templates
  router.get(
    '/templates',
    rbacMiddleware(['report:read']),
    customReportController.getReportTemplates
  )

  // POST /api/reports/templates/:templateId/execute - Execute a report template
  router.post(
    '/templates/:templateId/execute',
    rbacMiddleware(['report:read']),
    templateIdValidation.concat(executeReportValidation),
    validateRequest,
    customReportController.executeTemplate
  )

  // POST /api/reports - Create custom report definition
  router.post(
    '/',
    rbacMiddleware(['report:create']),
    createReportValidation,
    validateRequest,
    customReportController.createReportDefinition
  )

  // POST /api/reports/:reportId/execute - Execute custom report
  router.post(
    '/:reportId/execute',
    rbacMiddleware(['report:read']),
    reportIdValidation.concat(executeReportValidation),
    validateRequest,
    customReportController.executeReport
  )

  // POST /api/reports/:reportId/export - Export report
  router.post(
    '/:reportId/export',
    rbacMiddleware(['report:read']),
    reportIdValidation.concat(exportReportValidation),
    validateRequest,
    customReportController.exportReport
  )

  // GET /api/reports/history - Get report execution history
  router.get(
    '/history',
    rbacMiddleware(['report:read']),
    paginationValidation,
    validateRequest,
    customReportController.getReportHistory
  )

  return router
}
