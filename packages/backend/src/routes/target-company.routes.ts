import { Router } from 'express'
import { PrismaClient } from '@prisma/client'
import { TargetCompanyController } from '@/controllers/target-company.controller'
import { CacheService } from '@/services/cache.service'
import { authMiddleware } from '@/middleware/auth.middleware'
import { rbacMiddleware } from '@/middleware/rbac.middleware'
import { validateRequest } from '@/middleware/validation.middleware'
import { rateLimit } from 'express-rate-limit'
import { body, param, query } from 'express-validator'

export function createTargetCompanyRoutes(prisma: PrismaClient, cache: CacheService): Router {
  const router = Router()
  const targetCompanyController = new TargetCompanyController(prisma, cache)

  // Rate limiting
  const targetCompanyRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // limit each IP to 200 requests per windowMs (higher for search/filtering)
    message: 'Too many target company requests from this IP, please try again later.'
  })

  // Apply middleware to all routes
  router.use(authMiddleware)
  router.use(targetCompanyRateLimit)

  // Validation schemas
  const createTargetCompanyValidation = [
    body('name')
      .isString()
      .isLength({ min: 1, max: 255 })
      .withMessage('Name must be between 1 and 255 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('website')
      .optional()
      .isURL()
      .withMessage('Website must be a valid URL'),
    body('industry')
      .optional()
      .isString()
      .isLength({ max: 100 })
      .withMessage('Industry must be less than 100 characters'),
    body('sector')
      .optional()
      .isString()
      .isLength({ max: 100 })
      .withMessage('Sector must be less than 100 characters'),
    body('country')
      .optional()
      .isString()
      .isLength({ max: 100 })
      .withMessage('Country must be less than 100 characters'),
    body('revenue')
      .optional()
      .isNumeric()
      .withMessage('Revenue must be a number'),
    body('employees')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Employees must be a positive integer'),
    body('foundedYear')
      .optional()
      .isInt({ min: 1800, max: new Date().getFullYear() })
      .withMessage('Founded year must be between 1800 and current year'),
    body('contactEmail')
      .optional()
      .isEmail()
      .withMessage('Contact email must be valid'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array'),
    body('tags.*')
      .optional()
      .isString()
      .withMessage('Each tag must be a string')
  ]

  const updateTargetCompanyValidation = [
    body('name')
      .optional()
      .isString()
      .isLength({ min: 1, max: 255 })
      .withMessage('Name must be between 1 and 255 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('website')
      .optional()
      .isURL()
      .withMessage('Website must be a valid URL'),
    body('status')
      .optional()
      .isIn(['ACTIVE', 'INACTIVE', 'CONTACTED', 'IN_DISCUSSION', 'QUALIFIED', 'DISQUALIFIED', 'ACQUIRED'])
      .withMessage('Invalid status'),
    body('opportunityScore')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Opportunity score must be between 0 and 100'),
    body('strategicFit')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Strategic fit must be between 0 and 100'),
    body('financialHealth')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Financial health must be between 0 and 100'),
    body('revenue')
      .optional()
      .isNumeric()
      .withMessage('Revenue must be a number'),
    body('employees')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Employees must be a positive integer'),
    body('contactEmail')
      .optional()
      .isEmail()
      .withMessage('Contact email must be valid'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array')
  ]

  const createInteractionValidation = [
    body('targetId')
      .isString()
      .isLength({ min: 1 })
      .withMessage('Target ID is required'),
    body('type')
      .isIn(['EMAIL', 'PHONE_CALL', 'MEETING', 'CONFERENCE', 'RESEARCH', 'NOTE', 'FOLLOW_UP'])
      .withMessage('Invalid interaction type'),
    body('subject')
      .optional()
      .isString()
      .isLength({ max: 255 })
      .withMessage('Subject must be less than 255 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('scheduledAt')
      .optional()
      .isISO8601()
      .withMessage('Scheduled date must be valid ISO8601 date'),
    body('followUpDate')
      .optional()
      .isISO8601()
      .withMessage('Follow-up date must be valid ISO8601 date'),
    body('followUpRequired')
      .optional()
      .isBoolean()
      .withMessage('Follow-up required must be boolean')
  ]

  const idValidation = [
    param('id')
      .isString()
      .isLength({ min: 1 })
      .withMessage('ID parameter is required')
  ]

  const scoringValidation = [
    body('opportunityScore')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Opportunity score must be between 0 and 100'),
    body('strategicFit')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Strategic fit must be between 0 and 100'),
    body('financialHealth')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Financial health must be between 0 and 100')
  ]

  // Routes

  // GET /api/target-companies - Get all target companies with filtering
  router.get(
    '/',
    rbacMiddleware(['target_company:read']),
    targetCompanyController.getTargetCompanies
  )

  // GET /api/target-companies/search - Search target companies
  router.get(
    '/search',
    rbacMiddleware(['target_company:read']),
    query('q').isString().isLength({ min: 1 }).withMessage('Query parameter is required'),
    validateRequest,
    targetCompanyController.searchTargetCompanies
  )

  // POST /api/target-companies - Create new target company
  router.post(
    '/',
    rbacMiddleware(['target_company:create']),
    createTargetCompanyValidation,
    validateRequest,
    targetCompanyController.createTargetCompany
  )

  // GET /api/target-companies/:id - Get target company by ID
  router.get(
    '/:id',
    rbacMiddleware(['target_company:read']),
    idValidation,
    validateRequest,
    targetCompanyController.getTargetCompanyById
  )

  // PUT /api/target-companies/:id - Update target company
  router.put(
    '/:id',
    rbacMiddleware(['target_company:update']),
    idValidation,
    updateTargetCompanyValidation,
    validateRequest,
    targetCompanyController.updateTargetCompany
  )

  // DELETE /api/target-companies/:id - Delete target company
  router.delete(
    '/:id',
    rbacMiddleware(['target_company:delete']),
    idValidation,
    validateRequest,
    targetCompanyController.deleteTargetCompany
  )

  // POST /api/target-companies/interactions - Create interaction
  router.post(
    '/interactions',
    rbacMiddleware(['target_company:update']),
    createInteractionValidation,
    validateRequest,
    targetCompanyController.createInteraction
  )

  // PUT /api/target-companies/:id/scoring - Update scoring
  router.put(
    '/:id/scoring',
    rbacMiddleware(['target_company:update']),
    idValidation,
    scoringValidation,
    validateRequest,
    targetCompanyController.updateScoring
  )

  return router
}
