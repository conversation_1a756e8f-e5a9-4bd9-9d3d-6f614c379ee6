// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Tenant model for multi-tenancy
model Tenant {
  id        String  @id @default(cuid())
  name      String
  slug      String  @unique // URL-friendly identifier
  domain    String? @unique // Custom domain (optional)
  subdomain String  @unique // Subdomain for multi-tenant routing

  // Tenant metadata
  description String?
  logo        String?
  website     String?
  industry    String?
  size        TenantSize? @default(SMALL)

  // Status and lifecycle
  status      TenantStatus @default(ACTIVE)
  plan        TenantPlan   @default(STARTER)
  trialEndsAt DateTime?

  // Configuration
  settings Json? // Tenant-specific settings
  features Json? // Enabled features
  limits   Json? // Usage limits and quotas
  branding Json? // Custom branding configuration

  // Billing and subscription
  billingEmail   String?
  subscriptionId String?
  customerId     String?

  // Timestamps
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime? // Soft delete

  // Relations
  users                   User[]
  deals                   Deal[]
  vdrs                    VirtualDataRoom[]
  vdrDocuments            VDRDocument[]
  roles                   Role[]
  invitations             TenantInvitation[]
  apiKeys                 TenantApiKey[]
  permissionAudits        PermissionAudit[]
  roleAssignmentRules     RoleAssignmentRule[]
  permissionTemplates     PermissionTemplate[]
  policies                Policy[]
  policySets              PolicySet[]
  policyAuditLogs         PolicyAuditLog[]
  dealStages              DealStage[]
  dealPipelines           DealPipeline[]
  targetCompanies         TargetCompany[]
  targetCompanyWatchlists TargetCompanyWatchlist[]

  @@map("tenants")
}

enum TenantStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TRIAL
  EXPIRED
  DELETED
}

enum TenantSize {
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum TenantPlan {
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

// User model
model User {
  id          String     @id @default(cuid())
  email       String     @unique
  firstName   String
  lastName    String
  avatar      String?
  status      UserStatus @default(ACTIVE)
  lastLoginAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Authentication
  password      String?
  emailVerified Boolean @default(false)

  // Relations
  userRoles                  UserRole[]
  assignedUserRoles          UserRole[]           @relation("UserRoleAssignedBy")
  sessions                   Session[]
  createdDeals               Deal[]               @relation("DealCreator")
  assignedDeals              Deal[]               @relation("DealAssignee")
  vdrDocuments               VDRDocument[]        @relation("VDRDocumentUploader")
  auditLogs                  AuditLog[]
  DueDiligenceItem           DueDiligenceItem[]
  mfa                        UserMfa?
  sentInvitations            TenantInvitation[]   @relation("TenantInviter")
  permissionAuditsUser       PermissionAudit[]    @relation("PermissionAuditUser")
  permissionAuditsPerformer  PermissionAudit[]    @relation("PermissionAuditPerformer")
  createdRoleAssignmentRules RoleAssignmentRule[]
  createdPermissionTemplates PermissionTemplate[]
  createdPolicies            Policy[]
  createdPolicySets          PolicySet[]
  policyAuditLogs            PolicyAuditLog[]     @relation("PolicyAuditUser")

  // Deal relations
  dealStageChanges      DealStageHistory[] @relation("DealStageChanger")
  dealActivitiesCreated DealActivity[]     @relation("DealActivityCreator")
  dealTeamMemberships   DealTeamMember[]   @relation("DealTeamMember")
  assignedDealTasks     DealTask[]         @relation("DealTaskAssignee")
  createdDealTasks      DealTask[]         @relation("DealTaskCreator")
  dealNotesAuthored     DealNote[]         @relation("DealNoteAuthor")

  // VDR relations
  createdVDRs    VirtualDataRoom[] @relation("VDRCreator")
  vdrMemberships VDRUser[]         @relation("VDRInternalUser")
  vdrInvitations VDRUser[]         @relation("VDRInviter")

  // Target company relations
  targetCompanyInteractions TargetCompanyInteraction[] @relation("TargetCompanyInteractions")
  createdWatchlists         TargetCompanyWatchlist[]   @relation("CreatedWatchlists")

  @@map("users")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

// Role-based access control
model Role {
  id          String   @id @default(cuid())
  name        String
  description String?
  permissions Json // Store permissions as JSON array
  isSystem    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  priority    Int      @default(0)
  color       String?
  icon        String?
  category    String?
  tags        Json? // Array of tags
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Role hierarchy support
  parentRoleId String?
  parentRole   Role?   @relation("RoleHierarchy", fields: [parentRoleId], references: [id])
  childRoles   Role[]  @relation("RoleHierarchy")

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  userRoles           UserRole[]
  permissionAudits    PermissionAudit[]
  roleAssignmentRules RoleAssignmentRule[]

  @@unique([tenantId, name])
  @@index([tenantId, isSystem])
  @@index([tenantId, isDefault])
  @@index([parentRoleId])
  @@map("roles")
}

model UserRole {
  id         String    @id @default(cuid())
  userId     String
  roleId     String
  assignedBy String? // User ID who assigned this role
  assignedAt DateTime  @default(now())
  expiresAt  DateTime? // Optional expiration
  isActive   Boolean   @default(true)
  conditions Json? // Conditional access rules
  metadata   Json? // Additional metadata
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  user           User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)
  assignedByUser User? @relation("UserRoleAssignedBy", fields: [assignedBy], references: [id])

  @@unique([userId, roleId])
  @@index([userId, isActive])
  @@index([roleId, isActive])
  @@index([expiresAt])
  @@map("user_roles")
}

// Session management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Deal management
model Deal {
  id          String       @id @default(cuid())
  title       String
  description String?
  status      DealStatus   @default(PIPELINE)
  stage       String
  dealValue   Decimal?     @map("value") // Renamed for clarity
  currency    String       @default("USD")
  priority    DealPriority @default(MEDIUM)

  // Enhanced deal information
  dealType        DealType   @default(ACQUISITION)
  dealSource      DealSource @default(DIRECT)
  confidentiality String? // Confidentiality level
  tags            String[] // Deal tags for categorization

  // Target company information (legacy fields for backward compatibility)
  targetCompany     String
  targetIndustry    String?
  targetRevenue     Decimal?
  targetEmployees   Int?
  targetLocation    String?
  targetWebsite     String?
  targetDescription String?

  // New target company relation
  targetCompanyId  String?
  targetCompanyRef TargetCompany? @relation(fields: [targetCompanyId], references: [id])

  // Financial information
  enterpriseValue Decimal?
  equityValue     Decimal?
  ebitda          Decimal?
  revenue         Decimal?
  multiple        Decimal?

  // Timeline and milestones
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?
  firstContactDate  DateTime?
  loiSignedDate     DateTime?
  ddStartDate       DateTime?
  ddEndDate         DateTime?

  // Pipeline tracking
  currentStageId      String?
  stageEnteredAt      DateTime?
  daysInCurrentStage  Int?      @default(0)
  totalDaysInPipeline Int?      @default(0)

  // Probability and forecasting
  probability      Int?             @default(0) // 0-100%
  weightedValue    Decimal? // dealValue * probability
  forecastCategory ForecastCategory @default(PIPELINE)

  // Deal health and scoring
  healthScore      Int?      @default(50) // 0-100
  riskLevel        RiskLevel @default(MEDIUM)
  lastActivityDate DateTime?

  // Competitive information
  competitiveProcess Boolean  @default(false)
  competitors        String[] // Array of competitor names

  // Internal tracking
  internalNotes    String?
  nextSteps        String?
  keyRisks         String[]
  keyOpportunities String[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  createdBy String
  creator   User   @relation("DealCreator", fields: [createdBy], references: [id])

  assignedTo String?
  assignee   User?   @relation("DealAssignee", fields: [assignedTo], references: [id])

  // Enhanced relations
  currentStage      DealStage?         @relation("DealCurrentStage", fields: [currentStageId], references: [id])
  stageHistory      DealStageHistory[]
  activities        DealActivity[]
  contacts          DealContact[]
  team              DealTeamMember[]
  vdr               VirtualDataRoom?
  dueDiligenceItems DueDiligenceItem[]
  valuations        Valuation[]
  tasks             DealTask[]
  notes             DealNote[]
  milestones        DealMilestone[]

  // Compliance relations
  complianceStatuses  ComplianceStatus[]
  complianceAlerts    ComplianceAlert[]
  complianceReports   ComplianceReport[]
  complianceAuditLogs ComplianceAuditLog[]

  @@index([tenantId, status])
  @@index([tenantId, currentStageId])
  @@index([tenantId, assignedTo])
  @@index([tenantId, expectedCloseDate])
  @@index([tenantId, dealValue])
  @@index([tenantId, priority])
  @@index([tenantId, healthScore])
  @@map("deals")
}

// Target Company model for deal sourcing and identification
model TargetCompany {
  id          String  @id @default(cuid())
  name        String
  description String?
  website     String?

  // Company details
  industry  String?
  sector    String?
  subSector String?

  // Location information
  country String?
  region  String?
  city    String?
  address String?

  // Financial information
  revenue     Decimal?
  ebitda      Decimal?
  employees   Int?
  foundedYear Int?

  // Company status and type
  companyType String? // e.g., "Private", "Public", "Subsidiary"
  status      TargetCompanyStatus @default(ACTIVE)

  // Contact information
  primaryContact String?
  contactEmail   String?
  contactPhone   String?

  // Scoring and evaluation
  opportunityScore Int? @default(0) // 0-100
  strategicFit     Int? @default(0) // 0-100
  financialHealth  Int? @default(0) // 0-100

  // Market information
  marketCap       Decimal?
  enterpriseValue Decimal?

  // Tags and categorization
  tags String[]

  // Tracking and metadata
  source          String? // How this target was identified
  sourceDetails   String?
  lastContactDate DateTime?
  nextFollowUp    DateTime?

  // Notes and research
  notes         String?
  researchNotes String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  deals        Deal[]
  interactions TargetCompanyInteraction[]
  watchlists   TargetCompanyWatchlist[]

  @@index([tenantId, industry])
  @@index([tenantId, opportunityScore])
  @@index([tenantId, status])
  @@index([tenantId, name])
  @@map("target_companies")
}

enum TargetCompanyStatus {
  ACTIVE
  INACTIVE
  CONTACTED
  IN_DISCUSSION
  QUALIFIED
  DISQUALIFIED
  ACQUIRED
}

// Track interactions with target companies
model TargetCompanyInteraction {
  id       String @id @default(cuid())
  targetId String
  userId   String

  type        InteractionType
  subject     String?
  description String?
  outcome     String?

  // Scheduling
  scheduledAt DateTime?
  completedAt DateTime?

  // Follow-up
  followUpRequired Boolean   @default(false)
  followUpDate     DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  target TargetCompany @relation(fields: [targetId], references: [id], onDelete: Cascade)
  user   User          @relation("TargetCompanyInteractions", fields: [userId], references: [id])

  @@index([targetId])
  @@index([userId])
  @@index([type])
  @@map("target_company_interactions")
}

enum InteractionType {
  EMAIL
  PHONE_CALL
  MEETING
  CONFERENCE
  RESEARCH
  NOTE
  FOLLOW_UP
}

// Watchlists for target companies
model TargetCompanyWatchlist {
  id          String  @id @default(cuid())
  name        String
  description String?

  // Watchlist settings
  isDefault Boolean @default(false)
  isShared  Boolean @default(false)

  // Filters and criteria
  criteria Json? // Stored search criteria

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Creator
  createdBy String
  creator   User   @relation("CreatedWatchlists", fields: [createdBy], references: [id])

  // Relations
  targets TargetCompany[]

  @@index([tenantId, createdBy])
  @@map("target_company_watchlists")
}

enum DealStatus {
  PIPELINE
  DUE_DILIGENCE
  NEGOTIATION
  CLOSING
  CLOSED
  CANCELLED
}

enum DealPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum DealType {
  ACQUISITION
  MERGER
  ASSET_PURCHASE
  STOCK_PURCHASE
  JOINT_VENTURE
  STRATEGIC_PARTNERSHIP
  DIVESTITURE
  SPIN_OFF
  CARVE_OUT
  RECAPITALIZATION
}

enum DealSource {
  DIRECT
  INVESTMENT_BANK
  BROKER
  REFERRAL
  COLD_OUTREACH
  INBOUND
  CONFERENCE
  NETWORK
  EXISTING_RELATIONSHIP
  AUCTION
}

enum ForecastCategory {
  PIPELINE
  BEST_CASE
  COMMIT
  CLOSED
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ActivityType {
  CALL
  EMAIL
  MEETING
  PRESENTATION
  SITE_VISIT
  DUE_DILIGENCE
  NEGOTIATION
  DOCUMENT_REVIEW
  VALUATION
  LEGAL_REVIEW
  REGULATORY
  CLOSING
  OTHER
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  OVERDUE
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum MilestoneStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  MISSED
  CANCELLED
}

// Virtual Data Room (VDR) Management
model VirtualDataRoom {
  id          String  @id @default(cuid())
  name        String
  description String?
  dealId      String? @unique

  // VDR Configuration
  isActive  Boolean   @default(true)
  expiresAt DateTime?
  maxUsers  Int?      @default(50)

  // Security Settings
  requiresApproval Boolean @default(true)
  allowDownload    Boolean @default(false)
  allowPrint       Boolean @default(false)
  allowCopy        Boolean @default(false)
  watermarkEnabled Boolean @default(true)
  sessionTimeout   Int?    @default(30) // minutes

  // Branding
  logoUrl      String?
  primaryColor String?
  customDomain String?

  // Analytics
  totalViews     Int @default(0)
  totalDownloads Int @default(0)
  uniqueVisitors Int @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  createdBy String
  creator   User   @relation("VDRCreator", fields: [createdBy], references: [id])

  deal        Deal?           @relation(fields: [dealId], references: [id])
  folders     VDRFolder[]
  documents   VDRDocument[]
  users       VDRUser[]
  activities  VDRActivity[]
  permissions VDRPermission[]

  @@map("virtual_data_rooms")
}

model VDRFolder {
  id          String  @id @default(cuid())
  name        String
  description String?
  path        String // Full path for nested folders
  parentId    String?
  vdrId       String

  // Folder settings
  isProtected Boolean @default(false)
  order       Int     @default(0)

  // Access control
  accessLevel VDRAccessLevel @default(RESTRICTED)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  vdr         VirtualDataRoom @relation(fields: [vdrId], references: [id], onDelete: Cascade)
  parent      VDRFolder?      @relation("FolderHierarchy", fields: [parentId], references: [id])
  children    VDRFolder[]     @relation("FolderHierarchy")
  documents   VDRDocument[]
  permissions VDRPermission[]
  activities  VDRActivity[]

  @@unique([vdrId, path])
  @@map("vdr_folders")
}

model VDRDocument {
  id           String @id @default(cuid())
  name         String
  originalName String
  mimeType     String
  size         Int

  // File storage
  storageProvider String // 'local', 's3', 'azure', 'gcp'
  storagePath     String
  storageKey      String? // For cloud storage

  // Document properties
  version  Int            @default(1)
  status   DocumentStatus @default(ACTIVE)
  checksum String? // For integrity verification

  // VDR specific
  vdrId    String
  folderId String?

  // Security
  isEncrypted Boolean        @default(true)
  accessLevel VDRAccessLevel @default(RESTRICTED)

  // Document metadata
  tags        String[]
  description String?

  // Analytics
  viewCount     Int       @default(0)
  downloadCount Int       @default(0)
  lastViewedAt  DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  uploadedBy String
  uploader   User   @relation("VDRDocumentUploader", fields: [uploadedBy], references: [id])

  vdr    VirtualDataRoom @relation(fields: [vdrId], references: [id], onDelete: Cascade)
  folder VDRFolder?      @relation(fields: [folderId], references: [id])

  // Version control
  parentId String?       @map("parent_document_id")
  parent   VDRDocument?  @relation("DocumentVersions", fields: [parentId], references: [id])
  versions VDRDocument[] @relation("DocumentVersions")

  // Access tracking
  permissions VDRPermission[]
  activities  VDRActivity[]

  @@index([vdrId])
  @@index([folderId])
  @@index([tenantId])
  @@map("vdr_documents")
}

model VDRUser {
  id     String  @id @default(cuid())
  vdrId  String
  userId String? // Internal user
  email  String // External user email

  // User details for external users
  firstName String?
  lastName  String?
  company   String?
  title     String?

  // Access control
  role        VDRRole        @default(VIEWER)
  accessLevel VDRAccessLevel @default(RESTRICTED)
  status      VDRUserStatus  @default(PENDING)

  // Access settings
  canDownload Boolean @default(false)
  canPrint    Boolean @default(false)
  canComment  Boolean @default(false)

  // Session management
  lastLoginAt DateTime?
  expiresAt   DateTime?
  isActive    Boolean   @default(true)

  // Invitation
  invitedBy  String?
  invitedAt  DateTime?
  acceptedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  vdr     VirtualDataRoom @relation(fields: [vdrId], references: [id], onDelete: Cascade)
  user    User?           @relation("VDRInternalUser", fields: [userId], references: [id])
  inviter User?           @relation("VDRInviter", fields: [invitedBy], references: [id])

  permissions VDRPermission[]
  activities  VDRActivity[]

  @@unique([vdrId, email])
  @@map("vdr_users")
}

model VDRPermission {
  id     String @id @default(cuid())
  vdrId  String
  userId String // VDRUser ID

  // Resource permissions
  documentId String?
  folderId   String?

  // Permission types
  canView     Boolean @default(true)
  canDownload Boolean @default(false)
  canComment  Boolean @default(false)
  canShare    Boolean @default(false)

  // Time-based access
  validFrom  DateTime?
  validUntil DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  vdr      VirtualDataRoom @relation(fields: [vdrId], references: [id], onDelete: Cascade)
  vdrUser  VDRUser         @relation(fields: [userId], references: [id], onDelete: Cascade)
  document VDRDocument?    @relation(fields: [documentId], references: [id], onDelete: Cascade)
  folder   VDRFolder?      @relation(fields: [folderId], references: [id], onDelete: Cascade)

  @@unique([vdrId, userId, documentId])
  @@unique([vdrId, userId, folderId])
  @@map("vdr_permissions")
}

model VDRActivity {
  id     String          @id @default(cuid())
  vdrId  String
  userId String // VDRUser ID
  action VDRActivityType

  // Activity details
  documentId String?
  folderId   String?
  details    Json? // Additional activity data

  // Request information
  ipAddress String?
  userAgent String?
  location  String?

  // Timing
  duration Int? // For view activities (seconds)

  createdAt DateTime @default(now())

  // Relations
  vdr      VirtualDataRoom @relation(fields: [vdrId], references: [id], onDelete: Cascade)
  vdrUser  VDRUser         @relation(fields: [userId], references: [id], onDelete: Cascade)
  document VDRDocument?    @relation(fields: [documentId], references: [id])
  folder   VDRFolder?      @relation(fields: [folderId], references: [id])

  @@index([vdrId])
  @@index([userId])
  @@index([createdAt])
  @@map("vdr_activities")
}

enum DocumentStatus {
  ACTIVE
  ARCHIVED
  DELETED
}

enum DocumentAccessLevel {
  PUBLIC
  INTERNAL
  RESTRICTED
  CONFIDENTIAL
}

enum VDRAccessLevel {
  PUBLIC
  INTERNAL
  RESTRICTED
  CONFIDENTIAL
  ADMIN_ONLY
}

enum VDRRole {
  ADMIN
  MANAGER
  CONTRIBUTOR
  VIEWER
}

enum VDRUserStatus {
  PENDING
  ACTIVE
  SUSPENDED
  EXPIRED
  REVOKED
}

enum VDRActivityType {
  LOGIN
  LOGOUT
  VIEW_DOCUMENT
  DOWNLOAD_DOCUMENT
  UPLOAD_DOCUMENT
  DELETE_DOCUMENT
  CREATE_FOLDER
  DELETE_FOLDER
  SHARE_DOCUMENT
  COMMENT_DOCUMENT
  SEARCH
  EXPORT_DATA
  PRINT_DOCUMENT
  COPY_DOCUMENT
}

// Due Diligence Management
model DueDiligenceItem {
  id          String       @id @default(cuid())
  title       String
  description String?
  category    String
  status      DDItemStatus @default(PENDING)
  priority    DealPriority @default(MEDIUM)

  // Assignment
  assignedToId String?
  assignedTo   User?   @relation(fields: [assignedToId], references: [id])

  dueDate     DateTime?
  completedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("due_diligence_items")
}

enum DDItemStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  BLOCKED
  NOT_APPLICABLE
}

// Financial modeling and valuation
model Valuation {
  id          String        @id @default(cuid())
  name        String
  type        ValuationType
  methodology String

  // Financial data
  assumptions  Json
  calculations Json
  results      Json

  // DCF specific
  discountRate       Decimal?
  terminalGrowthRate Decimal?

  // Comparable analysis
  multiples Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dealId String
  deal   Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("valuations")
}

enum ValuationType {
  DCF
  COMPARABLE_COMPANY
  PRECEDENT_TRANSACTION
  ASSET_BASED
  SUM_OF_PARTS
}

// Multi-Factor Authentication
model UserMfa {
  id          String   @id @default(cuid())
  userId      String   @unique
  secret      String // Encrypted TOTP secret
  backupCodes Json // Array of encrypted backup codes
  enabled     Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_mfa")
}

// Audit logging
model AuditLog {
  id        String   @id @default(cuid())
  action    String
  entity    String
  entityId  String
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// Tenant invitations
model TenantInvitation {
  id        String           @id @default(cuid())
  tenantId  String
  email     String
  role      String           @default("User")
  token     String           @unique
  status    InvitationStatus @default(PENDING)
  expiresAt DateTime
  invitedBy String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant  Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inviter User   @relation("TenantInviter", fields: [invitedBy], references: [id])

  @@unique([tenantId, email])
  @@map("tenant_invitations")
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

// Tenant API keys
model TenantApiKey {
  id          String    @id @default(cuid())
  tenantId    String
  name        String
  keyHash     String    @unique
  permissions Json? // API permissions
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  isActive    Boolean   @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("tenant_api_keys")
}

// Migration tracking
model MigrationRecord {
  id          String    @id @default(cuid())
  migrationId String // ID of the migration script
  name        String // Human-readable name
  version     String // Migration version
  tenantId    String? // Null for global migrations
  executedAt  DateTime  @default(now())
  executedBy  String // User who executed the migration
  success     Boolean   @default(false)
  error       String? // Error message if failed
  rollbackAt  DateTime? // When migration was rolled back
  rollbackBy  String? // User who rolled back the migration

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([migrationId, tenantId])
  @@map("migration_records")
}

// Permission audit trail
model PermissionAudit {
  id           String   @id @default(cuid())
  userId       String
  tenantId     String
  action       String // 'grant', 'revoke', 'modify'
  resourceType String // 'role', 'permission', 'user_role'
  resourceId   String
  changes      Json // What changed
  performedBy  String // User ID who performed the action
  reason       String? // Optional reason for the change
  metadata     Json? // Additional context
  createdAt    DateTime @default(now())

  // Relations
  user      User   @relation("PermissionAuditUser", fields: [userId], references: [id])
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  performer User   @relation("PermissionAuditPerformer", fields: [performedBy], references: [id])
  role      Role?  @relation(fields: [resourceId], references: [id])

  @@index([userId, tenantId])
  @@index([tenantId, action])
  @@index([resourceType, resourceId])
  @@index([performedBy])
  @@index([createdAt])
  @@map("permission_audits")
}

// Role assignment rules for automatic role assignment
model RoleAssignmentRule {
  id           String   @id @default(cuid())
  name         String
  description  String?
  conditions   Json // Array of conditions
  targetRoleId String
  isActive     Boolean  @default(true)
  priority     Int      @default(0)
  tenantId     String
  createdBy    String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  tenant     Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  targetRole Role   @relation(fields: [targetRoleId], references: [id], onDelete: Cascade)
  creator    User   @relation(fields: [createdBy], references: [id])

  @@unique([tenantId, name])
  @@index([tenantId, isActive])
  @@index([priority])
  @@map("role_assignment_rules")
}

// Permission templates for common permission sets
model PermissionTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String
  permissions Json // Array of permission strings
  variables   Json? // Template variables
  isSystem    Boolean  @default(false)
  tenantId    String? // Null for system templates
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant  Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  creator User    @relation(fields: [createdBy], references: [id])

  @@unique([tenantId, name])
  @@index([category])
  @@index([isSystem])
  @@map("permission_templates")
}

// Policy engine models
model Policy {
  id          String   @id @default(cuid())
  name        String
  description String?
  version     String   @default("1.0.0")
  isActive    Boolean  @default(true)
  priority    Int      @default(1000)
  statements  Json // Array of policy statements
  variables   Json? // Policy variables
  metadata    Json? // Policy metadata
  tenantId    String
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant    Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  creator   User             @relation(fields: [createdBy], references: [id])
  auditLogs PolicyAuditLog[]

  @@unique([tenantId, name, version])
  @@index([tenantId, isActive])
  @@index([priority])
  @@map("policies")
}

model PolicySet {
  id                 String   @id @default(cuid())
  name               String
  description        String?
  policies           Json // Array of policy IDs
  combiningAlgorithm String   @default("deny_overrides")
  isActive           Boolean  @default(true)
  priority           Int      @default(1000)
  tenantId           String
  createdBy          String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  tenant  Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  creator User   @relation(fields: [createdBy], references: [id])

  @@unique([tenantId, name])
  @@index([tenantId, isActive])
  @@map("policy_sets")
}

model PolicyAuditLog {
  id        String   @id @default(cuid())
  policyId  String?
  action    String // 'created', 'updated', 'deleted', 'evaluated'
  userId    String
  tenantId  String
  changes   Json? // What changed
  reason    String?
  metadata  Json? // Additional context
  timestamp DateTime @default(now())

  // Relations
  policy Policy? @relation(fields: [policyId], references: [id], onDelete: SetNull)
  user   User    @relation("PolicyAuditUser", fields: [userId], references: [id])
  tenant Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([policyId])
  @@index([userId, tenantId])
  @@index([tenantId, action])
  @@index([timestamp])
  @@map("policy_audit_logs")
}

// Deal Pipeline Models
model DealStage {
  id          String  @id @default(cuid())
  name        String
  description String?
  order       Int
  isActive    Boolean @default(true)
  color       String? // Hex color for UI

  // Stage configuration
  isDefault Boolean @default(false)
  isClosing Boolean @default(false) // Indicates this is a closing stage

  // Automation settings
  autoAdvance    Boolean  @default(false)
  requiredFields String[] // Fields required to advance from this stage

  // Tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Relations
  deals        Deal[]             @relation("DealCurrentStage")
  stageHistory DealStageHistory[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, name])
  @@index([tenantId, order])
  @@map("deal_stages")
}

model DealStageHistory {
  id          String    @id @default(cuid())
  dealId      String
  stageId     String
  enteredAt   DateTime  @default(now())
  exitedAt    DateTime?
  daysInStage Int?

  // Change tracking
  changedBy String
  reason    String?
  notes     String?

  // Relations
  deal  Deal      @relation(fields: [dealId], references: [id], onDelete: Cascade)
  stage DealStage @relation(fields: [stageId], references: [id])
  user  User      @relation("DealStageChanger", fields: [changedBy], references: [id])

  @@index([dealId])
  @@index([stageId])
  @@map("deal_stage_history")
}

model DealActivity {
  id          String       @id @default(cuid())
  dealId      String
  type        ActivityType
  subject     String
  description String?

  // Activity details
  startTime   DateTime
  endTime     DateTime?
  location    String?
  isCompleted Boolean   @default(false)

  // Participants
  createdBy String
  attendees String[] // Array of user IDs

  // External participants
  externalAttendees Json? // Array of external contacts

  // Follow-up
  followUpDate  DateTime?
  followUpNotes String?

  // Relations
  deal    Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)
  creator User @relation("DealActivityCreator", fields: [createdBy], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([dealId])
  @@index([createdBy])
  @@index([startTime])
  @@map("deal_activities")
}

model DealContact {
  id     String @id @default(cuid())
  dealId String

  // Contact information
  firstName String
  lastName  String
  email     String?
  phone     String?
  title     String?
  company   String?

  // Contact role
  role            String? // e.g., "CEO", "CFO", "Legal Counsel"
  isPrimary       Boolean @default(false)
  isDecisionMaker Boolean @default(false)

  // Contact details
  notes       String?
  linkedInUrl String?

  // Relations
  deal Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([dealId])
  @@map("deal_contacts")
}

model DealTeamMember {
  id     String @id @default(cuid())
  dealId String
  userId String
  role   String // e.g., "Lead", "Analyst", "Legal", "Finance"

  // Permissions
  canEdit Boolean @default(false)
  canView Boolean @default(true)

  // Relations
  deal Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)
  user User @relation("DealTeamMember", fields: [userId], references: [id])

  createdAt DateTime @default(now())

  @@unique([dealId, userId])
  @@index([dealId])
  @@index([userId])
  @@map("deal_team_members")
}

model DealTask {
  id          String       @id @default(cuid())
  dealId      String
  title       String
  description String?
  status      TaskStatus   @default(PENDING)
  priority    TaskPriority @default(MEDIUM)

  // Task details
  dueDate        DateTime?
  completedAt    DateTime?
  estimatedHours Int?
  actualHours    Int?

  // Assignment
  assignedTo String?
  createdBy  String

  // Task dependencies
  dependsOn String[] // Array of task IDs
  blockedBy String[] // Array of task IDs that block this task

  // Relations
  deal     Deal  @relation(fields: [dealId], references: [id], onDelete: Cascade)
  assignee User? @relation("DealTaskAssignee", fields: [assignedTo], references: [id])
  creator  User  @relation("DealTaskCreator", fields: [createdBy], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([dealId])
  @@index([assignedTo])
  @@index([status])
  @@index([dueDate])
  @@map("deal_tasks")
}

model DealNote {
  id        String  @id @default(cuid())
  dealId    String
  content   String
  isPrivate Boolean @default(false)

  // Note metadata
  tags String[]

  // Relations
  deal      Deal   @relation(fields: [dealId], references: [id], onDelete: Cascade)
  createdBy String
  author    User   @relation("DealNoteAuthor", fields: [createdBy], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([dealId])
  @@index([createdBy])
  @@map("deal_notes")
}

model DealMilestone {
  id          String          @id @default(cuid())
  dealId      String
  name        String
  description String?
  status      MilestoneStatus @default(PENDING)

  // Milestone timing
  targetDate DateTime
  actualDate DateTime?

  // Milestone details
  isRequired Boolean @default(false)
  order      Int

  // Relations
  deal Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([dealId])
  @@index([targetDate])
  @@map("deal_milestones")
}

model DealPipeline {
  id          String  @id @default(cuid())
  name        String
  description String?
  isDefault   Boolean @default(false)
  isActive    Boolean @default(true)

  // Pipeline configuration
  stages Json // Array of stage configurations

  // Tenant relation
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, name])
  @@map("deal_pipelines")
}

// Compliance Management Models
model ComplianceFramework {
  id            String   @id @default(cuid())
  name          String
  description   String?
  jurisdiction  String
  category      String
  applicability Json
  requirements  Json
  penalties     Json
  lastUpdated   DateTime
  version       String
  isActive      Boolean  @default(true)
  tenantId      String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("compliance_frameworks")
}

model ComplianceStatus {
  id                   String    @id @default(cuid())
  dealId               String
  frameworkId          String
  requirementId        String
  status               String
  completionPercentage Int       @default(0)
  lastUpdated          DateTime
  updatedBy            String
  dueDate              DateTime?
  completedDate        DateTime?
  isOverdue            Boolean   @default(false)
  daysRemaining        Int?
  submittedDocuments   Json      @default("[]")
  missingDocuments     Json      @default("[]")
  approvalStatus       String?
  approvalDate         DateTime?
  approvalReference    String?
  issues               Json      @default("[]")
  riskLevel            String
  notes                Json      @default("[]")
  tenantId             String
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  deal Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("compliance_statuses")
}

model ComplianceAlert {
  id                   String    @id @default(cuid())
  type                 String
  severity             String
  title                String
  message              String
  dealId               String
  complianceStatusId   String?
  triggerDate          DateTime
  triggerEvent         String
  recipients           Json      @default("[]")
  notificationChannels Json      @default("[]")
  status               String
  acknowledgedBy       String?
  acknowledgedDate     DateTime?
  resolvedDate         DateTime?
  suggestedActions     Json      @default("[]")
  escalationRules      Json      @default("[]")
  tenantId             String
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  deal Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("compliance_alerts")
}

model ComplianceReport {
  id                String    @id @default(cuid())
  name              String
  type              String
  dealId            String?
  frameworkIds      Json      @default("[]")
  dateRange         Json?
  includeCompleted  Boolean   @default(true)
  includeInProgress Boolean   @default(true)
  includeOverdue    Boolean   @default(true)
  groupBy           Json      @default("[]")
  generatedDate     DateTime?
  reportUrl         String?
  format            String
  isScheduled       Boolean   @default(false)
  schedule          Json?
  visibility        String
  allowedUsers      Json      @default("[]")
  createdBy         String
  tenantId          String
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  deal Deal? @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("compliance_reports")
}

model ComplianceDocument {
  id                 String    @id @default(cuid())
  name               String
  type               String
  description        String?
  fileUrl            String
  fileSize           Int
  mimeType           String
  version            Int       @default(1)
  status             String
  complianceStatusId String
  submittedBy        String
  submittedDate      DateTime
  reviewedBy         String?
  reviewedDate       DateTime?
  approvedBy         String?
  approvedDate       DateTime?
  rejectionReason    String?
  metadata           Json      @default("{}")
  retentionDate      DateTime?
  tenantId           String
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  @@map("compliance_documents")
}

model ComplianceAuditLog {
  id         String   @id @default(cuid())
  dealId     String
  entityType String // ComplianceStatus, ComplianceDocument, etc.
  entityId   String
  action     String // CREATE, UPDATE, DELETE, APPROVE, REJECT
  oldValues  Json?
  newValues  Json?
  userId     String
  userEmail  String
  timestamp  DateTime @default(now())
  ipAddress  String?
  userAgent  String?
  tenantId   String

  deal Deal @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("compliance_audit_logs")
}

// Financial Modeling and Valuation Templates
model ValuationTemplate {
  id            String   @id @default(cuid())
  name          String
  description   String?
  category      String
  methodology   String
  configuration Json
  isPublic      Boolean  @default(false)
  createdBy     String
  tenantId      String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  scenarioDefinitions ScenarioDefinition[]

  @@index([tenantId])
  @@index([category])
  @@map("valuation_templates")
}

model ScenarioModel {
  id          String   @id @default(cuid())
  templateId  String
  name        String
  description String?
  assumptions Json
  results     Json
  createdBy   String
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([templateId])
  @@index([tenantId])
  @@index([createdBy])
  @@map("scenario_models")
}

model ScenarioDefinition {
  id                 String   @id @default(cuid())
  name               String
  description        String?
  templateId         String
  baseAssumptions    Json
  scenarios          Json
  sensitivityConfig  Json?
  monteCarloConfig   Json?
  createdBy          String
  tenantId           String
  isPublic           Boolean  @default(false)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  template ValuationTemplate @relation(fields: [templateId], references: [id])

  @@index([templateId])
  @@index([tenantId])
  @@index([createdBy])
  @@map("scenario_definitions")
}

enum DataSourceType {
  FINANCIAL_DATA
  MARKET_DATA
  ECONOMIC_DATA
  COMPANY_DATA
  TRANSACTION_DATA
}

model ExternalDataSource {
  id                 String         @id @default(cuid())
  name               String
  type               DataSourceType
  apiEndpoint        String
  apiKey             String?
  isActive           Boolean        @default(true)
  rateLimits         Json
  supportedDataTypes String[]
  configuration      Json
  tenantId           String
  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @updatedAt

  syncLogs DataSyncLog[]

  @@index([tenantId])
  @@index([type])
  @@map("external_data_sources")
}

model CompanyFinancials {
  id            String   @id @default(cuid())
  symbol        String
  companyName   String
  period        String
  periodType    String
  currency      String
  financialData Json
  ratios        Json
  marketData    Json
  lastUpdated   DateTime
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([symbol, period, periodType])
  @@index([symbol])
  @@index([period])
  @@map("company_financials")
}

model MarketData {
  id            String   @id @default(cuid())
  symbol        String   @unique
  price         Float
  change        Float
  changePercent Float
  volume        BigInt
  marketCap     Float
  pe            Float?
  eps           Float?
  high52Week    Float?
  low52Week     Float?
  dividendYield Float?
  beta          Float?
  lastUpdated   DateTime
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([symbol])
  @@map("market_data")
}

model EconomicIndicator {
  id          String   @id @default(cuid())
  indicator   String
  value       Float
  period      String
  frequency   String
  unit        String
  lastUpdated DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([indicator, period])
  @@index([indicator])
  @@index([period])
  @@map("economic_indicators")
}

model DataSyncLog {
  id               String   @id @default(cuid())
  dataSourceId     String
  dataType         String
  recordsProcessed Int
  recordsUpdated   Int
  recordsCreated   Int
  errors           String[]
  syncDuration     Int
  status           String
  startedAt        DateTime
  completedAt      DateTime
  createdAt        DateTime @default(now())

  dataSource ExternalDataSource @relation(fields: [dataSourceId], references: [id])

  @@index([dataSourceId])
  @@index([dataType])
  @@index([startedAt])
  @@map("data_sync_logs")
}
