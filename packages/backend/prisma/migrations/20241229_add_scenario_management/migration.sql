-- CreateEnum
CREATE TYPE "DataSourceType" AS ENUM ('FINANCIAL_DATA', 'MARKET_DATA', 'ECONOMIC_DATA', 'COMPANY_DATA', 'TRANSACTION_DATA');

-- CreateTable
CREATE TABLE "ScenarioDefinition" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "templateId" TEXT NOT NULL,
    "baseAssumptions" JSONB NOT NULL,
    "scenarios" JSONB NOT NULL,
    "sensitivityConfig" JSONB,
    "monteCarloConfig" JSONB,
    "createdBy" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ScenarioDefinition_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ExternalDataSource" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "DataSourceType" NOT NULL,
    "apiEndpoint" TEXT NOT NULL,
    "apiKey" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "rateLimits" JSONB NOT NULL,
    "supportedDataTypes" TEXT[],
    "configuration" JSONB NOT NULL,
    "tenantId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ExternalDataSource_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyFinancials" (
    "id" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "period" TEXT NOT NULL,
    "periodType" TEXT NOT NULL,
    "currency" TEXT NOT NULL,
    "financialData" JSONB NOT NULL,
    "ratios" JSONB NOT NULL,
    "marketData" JSONB NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CompanyFinancials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketData" (
    "id" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "change" DOUBLE PRECISION NOT NULL,
    "changePercent" DOUBLE PRECISION NOT NULL,
    "volume" BIGINT NOT NULL,
    "marketCap" DOUBLE PRECISION NOT NULL,
    "pe" DOUBLE PRECISION,
    "eps" DOUBLE PRECISION,
    "high52Week" DOUBLE PRECISION,
    "low52Week" DOUBLE PRECISION,
    "dividendYield" DOUBLE PRECISION,
    "beta" DOUBLE PRECISION,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MarketData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EconomicIndicator" (
    "id" TEXT NOT NULL,
    "indicator" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "period" TEXT NOT NULL,
    "frequency" TEXT NOT NULL,
    "unit" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EconomicIndicator_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DataSyncLog" (
    "id" TEXT NOT NULL,
    "dataSourceId" TEXT NOT NULL,
    "dataType" TEXT NOT NULL,
    "recordsProcessed" INTEGER NOT NULL,
    "recordsUpdated" INTEGER NOT NULL,
    "recordsCreated" INTEGER NOT NULL,
    "errors" TEXT[],
    "syncDuration" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DataSyncLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ScenarioDefinition_templateId_idx" ON "ScenarioDefinition"("templateId");

-- CreateIndex
CREATE INDEX "ScenarioDefinition_tenantId_idx" ON "ScenarioDefinition"("tenantId");

-- CreateIndex
CREATE INDEX "ScenarioDefinition_createdBy_idx" ON "ScenarioDefinition"("createdBy");

-- CreateIndex
CREATE INDEX "ExternalDataSource_tenantId_idx" ON "ExternalDataSource"("tenantId");

-- CreateIndex
CREATE INDEX "ExternalDataSource_type_idx" ON "ExternalDataSource"("type");

-- CreateIndex
CREATE UNIQUE INDEX "CompanyFinancials_symbol_period_periodType_key" ON "CompanyFinancials"("symbol", "period", "periodType");

-- CreateIndex
CREATE INDEX "CompanyFinancials_symbol_idx" ON "CompanyFinancials"("symbol");

-- CreateIndex
CREATE INDEX "CompanyFinancials_period_idx" ON "CompanyFinancials"("period");

-- CreateIndex
CREATE UNIQUE INDEX "MarketData_symbol_key" ON "MarketData"("symbol");

-- CreateIndex
CREATE INDEX "MarketData_symbol_idx" ON "MarketData"("symbol");

-- CreateIndex
CREATE UNIQUE INDEX "EconomicIndicator_indicator_period_key" ON "EconomicIndicator"("indicator", "period");

-- CreateIndex
CREATE INDEX "EconomicIndicator_indicator_idx" ON "EconomicIndicator"("indicator");

-- CreateIndex
CREATE INDEX "EconomicIndicator_period_idx" ON "EconomicIndicator"("period");

-- CreateIndex
CREATE INDEX "DataSyncLog_dataSourceId_idx" ON "DataSyncLog"("dataSourceId");

-- CreateIndex
CREATE INDEX "DataSyncLog_dataType_idx" ON "DataSyncLog"("dataType");

-- CreateIndex
CREATE INDEX "DataSyncLog_startedAt_idx" ON "DataSyncLog"("startedAt");

-- AddForeignKey
ALTER TABLE "ScenarioDefinition" ADD CONSTRAINT "ScenarioDefinition_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "ValuationTemplate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataSyncLog" ADD CONSTRAINT "DataSyncLog_dataSourceId_fkey" FOREIGN KEY ("dataSourceId") REFERENCES "ExternalDataSource"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
