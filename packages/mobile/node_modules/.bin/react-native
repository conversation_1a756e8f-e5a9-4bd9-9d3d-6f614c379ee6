#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/react-native@0.72.3_@babel+core@7.27.7_@babel+preset-env@7.27.2_react@18.2.0/node_modules/react-native/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/react-native@0.72.3_@babel+core@7.27.7_@babel+preset-env@7.27.2_react@18.2.0/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/react-native@0.72.3_@babel+core@7.27.7_@babel+preset-env@7.27.2_react@18.2.0/node_modules/react-native/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/react-native@0.72.3_@babel+core@7.27.7_@babel+preset-env@7.27.2_react@18.2.0/node_modules:/Users/<USER>/my-saas/gojuris/bmad-gojuris/mnaproXXX/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../react-native/cli.js" "$@"
else
  exec node  "$basedir/../react-native/cli.js" "$@"
fi
