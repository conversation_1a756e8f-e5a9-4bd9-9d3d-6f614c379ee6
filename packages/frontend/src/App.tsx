import { AuthProvider } from '@/components/auth-provider'
import { Layout } from '@/components/layout'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { AnalyticsPage } from '@/pages/analytics'
import { LoginPage } from '@/pages/auth/login'
import { DashboardPage } from '@/pages/dashboard'
import { DealsPage } from '@/pages/deals'
import { DueDiligencePage } from '@/pages/due-diligence'
import FinancialModelingPage from '@/pages/financial-modeling'
import { HomePage } from '@/pages/home'
import { SettingsPage } from '@/pages/settings'
import { TargetCompaniesPage } from '@/pages/target-companies'
import { VDRPage } from '@/pages/vdr'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light" storageKey="ma-platform-theme">
        <AuthProvider>
          <Router>
            <div className="min-h-screen bg-background">
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<HomePage />} />
                <Route path="/login" element={<LoginPage />} />

                {/* Protected routes */}
                <Route path="/app" element={<Layout />}>
                  <Route index element={<DashboardPage />} />
                  <Route path="dashboard" element={<DashboardPage />} />
                  <Route path="deals/*" element={<DealsPage />} />
                  <Route path="target-companies/*" element={<TargetCompaniesPage />} />
                  <Route path="vdr/*" element={<VDRPage />} />
                  <Route path="due-diligence/*" element={<DueDiligencePage />} />
                  <Route path="financial-modeling" element={<FinancialModelingPage />} />
                  <Route path="analytics" element={<AnalyticsPage />} />
                  <Route path="settings/*" element={<SettingsPage />} />
                </Route>
              </Routes>
              <Toaster />
            </div>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export default App
