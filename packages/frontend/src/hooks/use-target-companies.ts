import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/components/ui/use-toast'
import {
  targetCompanyService,
  TargetCompany,
  TargetCompanyFilters,
  CreateTargetCompanyRequest,
  UpdateTargetCompanyRequest,
  CreateInteractionRequest,
  TargetCompanyStatus,
  OpportunityPipeline,
  OpportunityMetrics,
  OpportunityScoring
} from '@/services/target-company.service'

// Query keys
export const targetCompanyKeys = {
  all: ['target-companies'] as const,
  lists: () => [...targetCompanyKeys.all, 'list'] as const,
  list: (filters: TargetCompanyFilters, sort: string, direction: string, page: number, limit: number) =>
    [...targetCompanyKeys.lists(), { filters, sort, direction, page, limit }] as const,
  details: () => [...targetCompanyKeys.all, 'detail'] as const,
  detail: (id: string) => [...targetCompanyKeys.details(), id] as const,
  search: (query: string, filters: TargetCompanyFilters) =>
    [...targetCompanyKeys.all, 'search', { query, filters }] as const,
  pipeline: () => [...targetCompanyKeys.all, 'pipeline'] as const,
  metrics: () => [...targetCompanyKeys.all, 'metrics'] as const,
}

// Hooks for target companies list
export function useTargetCompanies(
  filters: TargetCompanyFilters = {},
  sortField: string = 'createdAt',
  sortDirection: 'asc' | 'desc' = 'desc',
  page: number = 1,
  limit: number = 20
) {
  return useQuery({
    queryKey: targetCompanyKeys.list(filters, sortField, sortDirection, page, limit),
    queryFn: () => targetCompanyService.getTargetCompanies(filters, sortField, sortDirection, page, limit),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Hook for single target company
export function useTargetCompany(id: string) {
  return useQuery({
    queryKey: targetCompanyKeys.detail(id),
    queryFn: () => targetCompanyService.getTargetCompanyById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Hook for target company search
export function useTargetCompanySearch(
  query: string,
  filters: TargetCompanyFilters = {},
  limit: number = 10
) {
  return useQuery({
    queryKey: targetCompanyKeys.search(query, filters),
    queryFn: () => targetCompanyService.searchTargetCompanies(query, filters, limit),
    enabled: query.length >= 2, // Only search when query is at least 2 characters
    staleTime: 1000 * 60 * 2, // 2 minutes
  })
}

// Hook for opportunity pipeline
export function useOpportunityPipeline() {
  return useQuery({
    queryKey: targetCompanyKeys.pipeline(),
    queryFn: () => targetCompanyService.getOpportunityPipeline(),
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}

// Hook for pipeline metrics
export function usePipelineMetrics() {
  return useQuery({
    queryKey: targetCompanyKeys.metrics(),
    queryFn: () => targetCompanyService.getPipelineMetrics(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Mutation hooks
export function useCreateTargetCompany() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateTargetCompanyRequest) => targetCompanyService.createTargetCompany(data),
    onSuccess: (newCompany) => {
      // Invalidate and refetch target companies list
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: `Target company "${newCompany.name}" created successfully.`,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create target company.',
        variant: 'destructive',
      })
    },
  })
}

export function useUpdateTargetCompany() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTargetCompanyRequest }) =>
      targetCompanyService.updateTargetCompany(id, data),
    onSuccess: (updatedCompany) => {
      // Update the specific target company in cache
      queryClient.setQueryData(
        targetCompanyKeys.detail(updatedCompany.id),
        updatedCompany
      )
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: `Target company "${updatedCompany.name}" updated successfully.`,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update target company.',
        variant: 'destructive',
      })
    },
  })
}

export function useDeleteTargetCompany() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => targetCompanyService.deleteTargetCompany(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: targetCompanyKeys.detail(deletedId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: 'Target company deleted successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete target company.',
        variant: 'destructive',
      })
    },
  })
}

export function useCreateInteraction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateInteractionRequest) => targetCompanyService.createInteraction(data),
    onSuccess: (newInteraction) => {
      // Invalidate the specific target company to refresh interactions
      queryClient.invalidateQueries({ 
        queryKey: targetCompanyKeys.detail(newInteraction.targetId) 
      })
      
      // Invalidate lists to update last contact date
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      
      toast({
        title: 'Success',
        description: 'Interaction recorded successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create interaction.',
        variant: 'destructive',
      })
    },
  })
}

export function useUpdateScoring() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      id, 
      scores 
    }: { 
      id: string; 
      scores: {
        opportunityScore?: number
        strategicFit?: number
        financialHealth?: number
      }
    }) => targetCompanyService.updateScoring(id, scores),
    onSuccess: (updatedCompany) => {
      // Update the specific target company in cache
      queryClient.setQueryData(
        targetCompanyKeys.detail(updatedCompany.id),
        updatedCompany
      )
      
      // Invalidate lists and metrics
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: 'Scoring updated successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update scoring.',
        variant: 'destructive',
      })
    },
  })
}

export function useMoveToNextStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      targetCompanyService.moveToNextStage(id, reason),
    onSuccess: (updatedCompany) => {
      // Update the specific target company in cache
      queryClient.setQueryData(
        targetCompanyKeys.detail(updatedCompany.id),
        updatedCompany
      )
      
      // Invalidate lists and metrics
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: 'Target company moved to next stage successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to move to next stage.',
        variant: 'destructive',
      })
    },
  })
}

export function useMoveToStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      id, 
      status, 
      reason 
    }: { 
      id: string; 
      status: TargetCompanyStatus; 
      reason?: string 
    }) => targetCompanyService.moveToStage(id, status, reason),
    onSuccess: (updatedCompany) => {
      // Update the specific target company in cache
      queryClient.setQueryData(
        targetCompanyKeys.detail(updatedCompany.id),
        updatedCompany
      )
      
      // Invalidate lists and metrics
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: 'Target company moved to stage successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to move to stage.',
        variant: 'destructive',
      })
    },
  })
}

export function useCalculateOpportunityScoring() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      id, 
      factors 
    }: { 
      id: string; 
      factors?: {
        marketSize?: number
        competitivePosition?: number
        financialStrength?: number
        strategicAlignment?: number
        managementQuality?: number
      }
    }) => targetCompanyService.calculateOpportunityScoring(id, factors),
    onSuccess: (scoring) => {
      // Invalidate the specific target company to refresh with new scores
      queryClient.invalidateQueries({ 
        queryKey: targetCompanyKeys.detail(scoring.targetId) 
      })
      
      // Invalidate lists and metrics
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.lists() })
      queryClient.invalidateQueries({ queryKey: targetCompanyKeys.metrics() })
      
      toast({
        title: 'Success',
        description: 'Opportunity scoring calculated successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to calculate opportunity scoring.',
        variant: 'destructive',
      })
    },
  })
}
