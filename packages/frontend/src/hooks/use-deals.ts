import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/components/ui/use-toast'
import {
  dealService,
  Deal,
  DealFilters,
  CreateDealRequest,
  UpdateDealRequest,
  DealDocument,
  DealTeamMember,
  DealTask,
  TaskStatus
} from '@/services/deal.service'

// Query keys
export const dealKeys = {
  all: ['deals'] as const,
  lists: () => [...dealKeys.all, 'list'] as const,
  list: (filters: DealFilters, sort: string, direction: string, page: number, limit: number) =>
    [...dealKeys.lists(), { filters, sort, direction, page, limit }] as const,
  details: () => [...dealKeys.all, 'detail'] as const,
  detail: (id: string) => [...dealKeys.details(), id] as const,
  documents: (dealId: string) => [...dealKeys.all, 'documents', dealId] as const,
  team: (dealId: string) => [...dealKeys.all, 'team', dealId] as const,
  tasks: (dealId: string) => [...dealKeys.all, 'tasks', dealId] as const,
  analytics: () => [...dealKeys.all, 'analytics'] as const,
}

// Hooks for deals list
export function useDeals(
  filters: DealFilters = {},
  sortField: string = 'createdAt',
  sortDirection: 'asc' | 'desc' = 'desc',
  page: number = 1,
  limit: number = 20
) {
  return useQuery({
    queryKey: dealKeys.list(filters, sortField, sortDirection, page, limit),
    queryFn: () => dealService.getDeals(filters, sortField, sortDirection, page, limit),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Hook for single deal
export function useDeal(id: string) {
  return useQuery({
    queryKey: dealKeys.detail(id),
    queryFn: () => dealService.getDealById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Hook for deal documents
export function useDealDocuments(dealId: string, filters?: any) {
  return useQuery({
    queryKey: dealKeys.documents(dealId),
    queryFn: () => dealService.getDealDocuments(dealId, filters),
    enabled: !!dealId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  })
}

// Hook for deal team
export function useDealTeam(dealId: string) {
  return useQuery({
    queryKey: dealKeys.team(dealId),
    queryFn: () => dealService.getDealTeam(dealId),
    enabled: !!dealId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Hook for deal tasks
export function useDealTasks(dealId: string, filters?: any) {
  return useQuery({
    queryKey: dealKeys.tasks(dealId),
    queryFn: () => dealService.getDealTasks(dealId, filters),
    enabled: !!dealId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  })
}

// Hook for advanced analytics
export function useAdvancedAnalytics(dateFrom?: string, dateTo?: string) {
  return useQuery({
    queryKey: dealKeys.analytics(),
    queryFn: () => dealService.getAdvancedAnalytics(dateFrom, dateTo),
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}

// Mutation hooks
export function useCreateDeal() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateDealRequest) => dealService.createDeal(data),
    onSuccess: (newDeal) => {
      // Invalidate and refetch deals list
      queryClient.invalidateQueries({ queryKey: dealKeys.lists() })
      
      toast({
        title: 'Success',
        description: `Deal "${newDeal.title}" created successfully.`,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create deal.',
        variant: 'destructive',
      })
    },
  })
}

export function useUpdateDeal() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateDealRequest }) =>
      dealService.updateDeal(id, data),
    onSuccess: (updatedDeal) => {
      // Update the specific deal in cache
      queryClient.setQueryData(
        dealKeys.detail(updatedDeal.id),
        updatedDeal
      )
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: dealKeys.lists() })
      
      toast({
        title: 'Success',
        description: `Deal "${updatedDeal.title}" updated successfully.`,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update deal.',
        variant: 'destructive',
      })
    },
  })
}

export function useDeleteDeal() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => dealService.deleteDeal(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: dealKeys.detail(deletedId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: dealKeys.lists() })
      
      toast({
        title: 'Success',
        description: 'Deal deleted successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete deal.',
        variant: 'destructive',
      })
    },
  })
}

export function useUploadDocument() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ dealId, documentData }: { dealId: string; documentData: FormData }) =>
      dealService.uploadDocument(dealId, documentData),
    onSuccess: (newDocument) => {
      // Invalidate documents for this deal
      queryClient.invalidateQueries({ 
        queryKey: dealKeys.documents(newDocument.dealId) 
      })
      
      // Invalidate the deal detail to refresh document count
      queryClient.invalidateQueries({ 
        queryKey: dealKeys.detail(newDocument.dealId) 
      })
      
      toast({
        title: 'Success',
        description: 'Document uploaded successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to upload document.',
        variant: 'destructive',
      })
    },
  })
}

export function useAddTeamMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      dealId, 
      data 
    }: { 
      dealId: string; 
      data: {
        userId: string
        role: string
        permissions?: string[]
      }
    }) => dealService.addTeamMember(dealId, data),
    onSuccess: (newMember) => {
      // Invalidate team for this deal
      queryClient.invalidateQueries({ 
        queryKey: dealKeys.team(newMember.dealId) 
      })
      
      toast({
        title: 'Success',
        description: 'Team member added successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add team member.',
        variant: 'destructive',
      })
    },
  })
}

export function useCreateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      dealId, 
      data 
    }: { 
      dealId: string; 
      data: {
        title: string
        description?: string
        priority?: any
        assignedTo?: string
        dueDate?: string
        estimatedHours?: number
        tags?: string[]
        dependencies?: string[]
      }
    }) => dealService.createTask(dealId, data),
    onSuccess: (newTask) => {
      // Invalidate tasks for this deal
      queryClient.invalidateQueries({ 
        queryKey: dealKeys.tasks(newTask.dealId) 
      })
      
      toast({
        title: 'Success',
        description: 'Task created successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create task.',
        variant: 'destructive',
      })
    },
  })
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      dealId, 
      taskId, 
      status 
    }: { 
      dealId: string; 
      taskId: string; 
      status: TaskStatus 
    }) => dealService.updateTaskStatus(dealId, taskId, status),
    onSuccess: (updatedTask) => {
      // Invalidate tasks for this deal
      queryClient.invalidateQueries({ 
        queryKey: dealKeys.tasks(updatedTask.dealId) 
      })
      
      toast({
        title: 'Success',
        description: 'Task status updated successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update task status.',
        variant: 'destructive',
      })
    },
  })
}

export function useMoveToNextStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ dealId, reason }: { dealId: string; reason?: string }) =>
      dealService.moveToNextStage(dealId, reason),
    onSuccess: (updatedDeal) => {
      // Update the specific deal in cache
      queryClient.setQueryData(
        dealKeys.detail(updatedDeal.id),
        updatedDeal
      )
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: dealKeys.lists() })
      
      toast({
        title: 'Success',
        description: 'Deal moved to next stage successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to move to next stage.',
        variant: 'destructive',
      })
    },
  })
}

export function useMoveToStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      dealId, 
      stageId, 
      reason 
    }: { 
      dealId: string; 
      stageId: string; 
      reason?: string 
    }) => dealService.moveToStage(dealId, stageId, reason),
    onSuccess: (updatedDeal) => {
      // Update the specific deal in cache
      queryClient.setQueryData(
        dealKeys.detail(updatedDeal.id),
        updatedDeal
      )
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: dealKeys.lists() })
      
      toast({
        title: 'Success',
        description: 'Deal moved to stage successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to move to stage.',
        variant: 'destructive',
      })
    },
  })
}
