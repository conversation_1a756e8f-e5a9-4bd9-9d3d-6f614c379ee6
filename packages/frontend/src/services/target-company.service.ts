import { ApiClient } from './api.client'

export interface TargetCompany {
  id: string
  name: string
  description?: string
  website?: string
  industry?: string
  sector?: string
  subSector?: string
  country?: string
  region?: string
  city?: string
  address?: string
  revenue?: number
  ebitda?: number
  employees?: number
  foundedYear?: number
  companyType?: string
  status: TargetCompanyStatus
  primaryContact?: string
  contactEmail?: string
  contactPhone?: string
  opportunityScore?: number
  strategicFit?: number
  financialHealth?: number
  marketCap?: number
  enterpriseValue?: number
  source?: string
  sourceDetails?: string
  lastContactDate?: string
  nextFollowUp?: string
  tags: string[]
  notes?: string
  researchNotes?: string
  createdAt: string
  updatedAt: string
  interactions?: TargetCompanyInteraction[]
  deals?: Deal[]
  _count?: {
    interactions: number
    deals: number
  }
}

export enum TargetCompanyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CONTACTED = 'CONTACTED',
  IN_DISCUSSION = 'IN_DISCUSSION',
  QUALIFIED = 'QUALIFIED',
  DISQUALIFIED = 'DISQUALIFIED',
  ACQUIRED = 'ACQUIRED'
}

export interface TargetCompanyInteraction {
  id: string
  targetId: string
  userId: string
  type: InteractionType
  subject?: string
  description?: string
  outcome?: string
  scheduledAt?: string
  completedAt?: string
  followUpRequired: boolean
  followUpDate?: string
  createdAt: string
  updatedAt: string
  user: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
}

export enum InteractionType {
  EMAIL = 'EMAIL',
  PHONE_CALL = 'PHONE_CALL',
  MEETING = 'MEETING',
  CONFERENCE = 'CONFERENCE',
  RESEARCH = 'RESEARCH',
  NOTE = 'NOTE',
  FOLLOW_UP = 'FOLLOW_UP'
}

export interface Deal {
  id: string
  title: string
  status: string
  dealValue?: number
  currency: string
}

export interface CreateTargetCompanyRequest {
  name: string
  description?: string
  website?: string
  industry?: string
  sector?: string
  subSector?: string
  country?: string
  region?: string
  city?: string
  address?: string
  revenue?: number
  ebitda?: number
  employees?: number
  foundedYear?: number
  companyType?: string
  primaryContact?: string
  contactEmail?: string
  contactPhone?: string
  source?: string
  sourceDetails?: string
  tags?: string[]
  notes?: string
  researchNotes?: string
}

export interface UpdateTargetCompanyRequest {
  name?: string
  description?: string
  website?: string
  industry?: string
  sector?: string
  subSector?: string
  country?: string
  region?: string
  city?: string
  address?: string
  revenue?: number
  ebitda?: number
  employees?: number
  foundedYear?: number
  companyType?: string
  status?: TargetCompanyStatus
  primaryContact?: string
  contactEmail?: string
  contactPhone?: string
  opportunityScore?: number
  strategicFit?: number
  financialHealth?: number
  marketCap?: number
  enterpriseValue?: number
  source?: string
  sourceDetails?: string
  lastContactDate?: string
  nextFollowUp?: string
  tags?: string[]
  notes?: string
  researchNotes?: string
}

export interface TargetCompanyFilters {
  status?: TargetCompanyStatus[]
  industry?: string[]
  country?: string[]
  minRevenue?: number
  maxRevenue?: number
  minEmployees?: number
  maxEmployees?: number
  minOpportunityScore?: number
  tags?: string[]
  search?: string
}

export interface TargetCompanyListResponse {
  companies: TargetCompany[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface CreateInteractionRequest {
  targetId: string
  type: InteractionType
  subject?: string
  description?: string
  outcome?: string
  scheduledAt?: string
  followUpRequired?: boolean
  followUpDate?: string
}

export interface OpportunityPipeline {
  id: string
  name: string
  description?: string
  stages: OpportunityStage[]
  isDefault: boolean
  isActive: boolean
  tenantId: string
}

export interface OpportunityStage {
  id: string
  name: string
  description?: string
  order: number
  status: TargetCompanyStatus
  color?: string
  isDefault: boolean
  autoAdvance: boolean
  requiredActions: string[]
}

export interface OpportunityMetrics {
  stageId: string
  stageName: string
  companyCount: number
  averageScore: number
  averageDaysInStage: number
  conversionRate: number
  dropOffRate: number
}

export interface OpportunityScoring {
  targetId: string
  opportunityScore: number
  strategicFit: number
  financialHealth: number
  marketPosition: number
  competitiveAdvantage: number
  totalScore: number
  lastUpdated: string
}

class TargetCompanyService {
  private apiClient: ApiClient

  constructor() {
    this.apiClient = new ApiClient()
  }

  // Target Company CRUD operations
  async getTargetCompanies(
    filters: TargetCompanyFilters = {},
    sortField: string = 'createdAt',
    sortDirection: 'asc' | 'desc' = 'desc',
    page: number = 1,
    limit: number = 20
  ): Promise<TargetCompanyListResponse> {
    const params = new URLSearchParams({
      sortField,
      sortDirection,
      page: page.toString(),
      limit: limit.toString()
    })

    // Add filters to params
    if (filters.status?.length) {
      filters.status.forEach(status => params.append('status', status))
    }
    if (filters.industry?.length) {
      filters.industry.forEach(industry => params.append('industry', industry))
    }
    if (filters.country?.length) {
      filters.country.forEach(country => params.append('country', country))
    }
    if (filters.tags?.length) {
      filters.tags.forEach(tag => params.append('tags', tag))
    }
    if (filters.search) params.append('search', filters.search)
    if (filters.minRevenue) params.append('minRevenue', filters.minRevenue.toString())
    if (filters.maxRevenue) params.append('maxRevenue', filters.maxRevenue.toString())
    if (filters.minEmployees) params.append('minEmployees', filters.minEmployees.toString())
    if (filters.maxEmployees) params.append('maxEmployees', filters.maxEmployees.toString())
    if (filters.minOpportunityScore) params.append('minOpportunityScore', filters.minOpportunityScore.toString())

    const response = await this.apiClient.get<TargetCompanyListResponse>(`/target-companies?${params}`)
    return response.data
  }

  async getTargetCompanyById(id: string): Promise<TargetCompany> {
    const response = await this.apiClient.get<TargetCompany>(`/target-companies/${id}`)
    return response.data
  }

  async createTargetCompany(data: CreateTargetCompanyRequest): Promise<TargetCompany> {
    const response = await this.apiClient.post<TargetCompany>('/target-companies', data)
    return response.data
  }

  async updateTargetCompany(id: string, data: UpdateTargetCompanyRequest): Promise<TargetCompany> {
    const response = await this.apiClient.put<TargetCompany>(`/target-companies/${id}`, data)
    return response.data
  }

  async deleteTargetCompany(id: string): Promise<void> {
    await this.apiClient.delete(`/target-companies/${id}`)
  }

  // Search functionality
  async searchTargetCompanies(
    query: string,
    filters: TargetCompanyFilters = {},
    limit: number = 10
  ): Promise<TargetCompany[]> {
    const params = new URLSearchParams({
      q: query,
      limit: limit.toString()
    })

    // Add filters
    if (filters.status?.length) {
      filters.status.forEach(status => params.append('status', status))
    }
    if (filters.industry?.length) {
      filters.industry.forEach(industry => params.append('industry', industry))
    }
    if (filters.country?.length) {
      filters.country.forEach(country => params.append('country', country))
    }
    if (filters.tags?.length) {
      filters.tags.forEach(tag => params.append('tags', tag))
    }

    const response = await this.apiClient.get<TargetCompany[]>(`/target-companies/search?${params}`)
    return response.data
  }

  // Interaction management
  async createInteraction(data: CreateInteractionRequest): Promise<TargetCompanyInteraction> {
    const response = await this.apiClient.post<TargetCompanyInteraction>('/target-companies/interactions', data)
    return response.data
  }

  // Scoring and pipeline management
  async updateScoring(
    id: string,
    scores: {
      opportunityScore?: number
      strategicFit?: number
      financialHealth?: number
    }
  ): Promise<TargetCompany> {
    const response = await this.apiClient.put<TargetCompany>(`/target-companies/${id}/scoring`, scores)
    return response.data
  }

  async getOpportunityPipeline(): Promise<OpportunityPipeline> {
    const response = await this.apiClient.get<OpportunityPipeline>('/target-companies/pipeline')
    return response.data
  }

  async getPipelineMetrics(): Promise<OpportunityMetrics[]> {
    const response = await this.apiClient.get<OpportunityMetrics[]>('/target-companies/pipeline/metrics')
    return response.data
  }

  async moveToNextStage(id: string, reason?: string): Promise<TargetCompany> {
    const response = await this.apiClient.post<TargetCompany>(`/target-companies/${id}/move-to-next-stage`, { reason })
    return response.data
  }

  async moveToStage(id: string, status: TargetCompanyStatus, reason?: string): Promise<TargetCompany> {
    const response = await this.apiClient.post<TargetCompany>(`/target-companies/${id}/move-to-stage`, { status, reason })
    return response.data
  }

  async calculateOpportunityScoring(
    id: string,
    factors?: {
      marketSize?: number
      competitivePosition?: number
      financialStrength?: number
      strategicAlignment?: number
      managementQuality?: number
    }
  ): Promise<OpportunityScoring> {
    const response = await this.apiClient.post<OpportunityScoring>(`/target-companies/${id}/calculate-scoring`, factors)
    return response.data
  }
}

export const targetCompanyService = new TargetCompanyService()
export default targetCompanyService
