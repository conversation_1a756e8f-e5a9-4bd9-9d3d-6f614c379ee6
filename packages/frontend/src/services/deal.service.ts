import { ApiClient } from './api.client'

export interface Deal {
  id: string
  title: string
  description?: string
  dealType: DealType
  dealSource: DealSource
  targetCompany: string
  targetIndustry?: string
  dealValue?: number
  currency: string
  priority: DealPriority
  status: DealStatus
  stage: string
  probability?: number
  weightedValue?: number
  forecastCategory: ForecastCategory
  healthScore?: number
  riskLevel: RiskLevel
  expectedCloseDate?: string
  actualCloseDate?: string
  firstContactDate?: string
  loiSignedDate?: string
  ddStartDate?: string
  ddEndDate?: string
  currentStageId?: string
  stageEnteredAt?: string
  daysInCurrentStage?: number
  totalDaysInPipeline?: number
  lastActivityDate?: string
  competitiveProcess: boolean
  competitors: string[]
  internalNotes?: string
  nextSteps?: string
  keyRisks: string[]
  keyOpportunities: string[]
  tags: string[]
  confidentiality: ConfidentialityLevel
  tenantId: string
  createdBy: string
  assignedTo?: string
  createdAt: string
  updatedAt: string
  currentStage?: DealStage
  stageHistory?: DealStageHistory[]
  activities?: DealActivity[]
  contacts?: DealContact[]
  team?: DealTeamMember[]
  tasks?: DealTask[]
  notes?: DealNote[]
  milestones?: DealMilestone[]
  documents?: DealDocument[]
  creator?: User
  assignee?: User
}

export enum DealType {
  ACQUISITION = 'ACQUISITION',
  MERGER = 'MERGER',
  JOINT_VENTURE = 'JOINT_VENTURE',
  STRATEGIC_PARTNERSHIP = 'STRATEGIC_PARTNERSHIP',
  ASSET_PURCHASE = 'ASSET_PURCHASE',
  DIVESTITURE = 'DIVESTITURE'
}

export enum DealSource {
  INBOUND = 'INBOUND',
  OUTBOUND = 'OUTBOUND',
  REFERRAL = 'REFERRAL',
  INVESTMENT_BANK = 'INVESTMENT_BANK',
  BROKER = 'BROKER',
  DIRECT = 'DIRECT',
  CONFERENCE = 'CONFERENCE',
  OTHER = 'OTHER'
}

export enum DealPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum DealStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

export enum ForecastCategory {
  BEST_CASE = 'BEST_CASE',
  COMMIT = 'COMMIT',
  PIPELINE = 'PIPELINE',
  CLOSED = 'CLOSED'
}

export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ConfidentialityLevel {
  PUBLIC = 'PUBLIC',
  INTERNAL = 'INTERNAL',
  CONFIDENTIAL = 'CONFIDENTIAL',
  HIGHLY_CONFIDENTIAL = 'HIGHLY_CONFIDENTIAL'
}

export interface DealStage {
  id: string
  name: string
  description?: string
  order: number
  isActive: boolean
  color?: string
  isDefault: boolean
  isClosing: boolean
  autoAdvance: boolean
  requiredFields: string[]
  tenantId: string
  createdAt: string
  updatedAt: string
}

export interface DealDocument {
  id: string
  dealId: string
  name: string
  description?: string
  type: DocumentType
  status: DocumentStatus
  version: number
  fileSize: number
  mimeType: string
  filePath: string
  uploadedBy: string
  uploadedAt: string
  isConfidential: boolean
  accessLevel: 'PUBLIC' | 'TEAM' | 'RESTRICTED'
  tags: string[]
  metadata?: Record<string, any>
  parentDocumentId?: string
  checksum: string
  expiresAt?: string
  downloadCount: number
  lastAccessedAt?: string
}

export enum DocumentType {
  CONTRACT = 'CONTRACT',
  FINANCIAL = 'FINANCIAL',
  LEGAL = 'LEGAL',
  TECHNICAL = 'TECHNICAL',
  OTHER = 'OTHER'
}

export enum DocumentStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED'
}

export interface DealTeamMember {
  id: string
  dealId: string
  userId: string
  role: string
  permissions: string[]
  joinedAt: string
  isActive: boolean
  user: {
    id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
  }
}

export interface DealTask {
  id: string
  dealId: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  assignedTo?: string
  createdBy: string
  dueDate?: string
  completedAt?: string
  estimatedHours?: number
  actualHours?: number
  tags: string[]
  dependencies: string[]
  attachments: string[]
  comments: TaskComment[]
  createdAt: string
  updatedAt: string
}

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  REVIEW = 'REVIEW',
  DONE = 'DONE',
  CANCELLED = 'CANCELLED'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export interface TaskComment {
  id: string
  taskId: string
  userId: string
  content: string
  createdAt: string
  user: {
    firstName: string
    lastName: string
    email: string
  }
}

export interface CreateDealRequest {
  title: string
  description?: string
  dealType: DealType
  dealSource: DealSource
  targetCompany: string
  targetIndustry?: string
  dealValue?: number
  currency?: string
  priority?: DealPriority
  expectedCloseDate?: string
  assignedTo?: string
  tags?: string[]
  confidentiality?: ConfidentialityLevel
}

export interface UpdateDealRequest {
  title?: string
  description?: string
  dealType?: DealType
  dealSource?: DealSource
  targetCompany?: string
  targetIndustry?: string
  dealValue?: number
  currency?: string
  priority?: DealPriority
  status?: DealStatus
  stage?: string
  probability?: number
  expectedCloseDate?: string
  actualCloseDate?: string
  healthScore?: number
  riskLevel?: RiskLevel
  competitiveProcess?: boolean
  competitors?: string[]
  internalNotes?: string
  nextSteps?: string
  keyRisks?: string[]
  keyOpportunities?: string[]
  tags?: string[]
  confidentiality?: ConfidentialityLevel
  assignedTo?: string
}

export interface DealFilters {
  status?: DealStatus[]
  stage?: string[]
  priority?: DealPriority[]
  riskLevel?: RiskLevel[]
  assignedTo?: string[]
  dealType?: DealType[]
  dealSource?: DealSource[]
  minValue?: number
  maxValue?: number
  expectedCloseDateFrom?: string
  expectedCloseDateTo?: string
  tags?: string[]
  search?: string
}

export interface DealListResponse {
  deals: Deal[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

class DealService {
  private apiClient: ApiClient

  constructor() {
    this.apiClient = new ApiClient()
  }

  // Deal CRUD operations
  async getDeals(
    filters: DealFilters = {},
    sortField: string = 'createdAt',
    sortDirection: 'asc' | 'desc' = 'desc',
    page: number = 1,
    limit: number = 20
  ): Promise<DealListResponse> {
    const params = new URLSearchParams({
      sortField,
      sortDirection,
      page: page.toString(),
      limit: limit.toString()
    })

    // Add filters to params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()))
        } else {
          params.append(key, value.toString())
        }
      }
    })

    const response = await this.apiClient.get<DealListResponse>(`/deals?${params}`)
    return response.data
  }

  async getDealById(id: string): Promise<Deal> {
    const response = await this.apiClient.get<Deal>(`/deals/${id}`)
    return response.data
  }

  async createDeal(data: CreateDealRequest): Promise<Deal> {
    const response = await this.apiClient.post<Deal>('/deals', data)
    return response.data
  }

  async updateDeal(id: string, data: UpdateDealRequest): Promise<Deal> {
    const response = await this.apiClient.put<Deal>(`/deals/${id}`, data)
    return response.data
  }

  async deleteDeal(id: string): Promise<void> {
    await this.apiClient.delete(`/deals/${id}`)
  }

  // Document management
  async uploadDocument(dealId: string, documentData: FormData): Promise<DealDocument> {
    const response = await this.apiClient.post<DealDocument>(`/deals/${dealId}/documents`, documentData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    return response.data
  }

  async getDealDocuments(dealId: string, filters?: any): Promise<DealDocument[]> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString())
        }
      })
    }

    const response = await this.apiClient.get<DealDocument[]>(`/deals/${dealId}/documents?${params}`)
    return response.data
  }

  async downloadDocument(dealId: string, documentId: string): Promise<Blob> {
    const response = await this.apiClient.get(`/deals/${dealId}/documents/${documentId}/download`, {
      responseType: 'blob'
    })
    return response.data
  }

  // Team management
  async addTeamMember(dealId: string, data: {
    userId: string
    role: string
    permissions?: string[]
  }): Promise<DealTeamMember> {
    const response = await this.apiClient.post<DealTeamMember>(`/deals/${dealId}/team`, data)
    return response.data
  }

  async getDealTeam(dealId: string): Promise<DealTeamMember[]> {
    const response = await this.apiClient.get<DealTeamMember[]>(`/deals/${dealId}/team`)
    return response.data
  }

  // Task management
  async createTask(dealId: string, data: {
    title: string
    description?: string
    priority?: TaskPriority
    assignedTo?: string
    dueDate?: string
    estimatedHours?: number
    tags?: string[]
    dependencies?: string[]
  }): Promise<DealTask> {
    const response = await this.apiClient.post<DealTask>(`/deals/${dealId}/tasks`, data)
    return response.data
  }

  async getDealTasks(dealId: string, filters?: any): Promise<DealTask[]> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString())
        }
      })
    }

    const response = await this.apiClient.get<DealTask[]>(`/deals/${dealId}/tasks?${params}`)
    return response.data
  }

  async updateTaskStatus(dealId: string, taskId: string, status: TaskStatus): Promise<DealTask> {
    const response = await this.apiClient.patch<DealTask>(`/deals/${dealId}/tasks/${taskId}`, { status })
    return response.data
  }

  // Stage management
  async moveToNextStage(dealId: string, reason?: string): Promise<Deal> {
    const response = await this.apiClient.post<Deal>(`/deals/${dealId}/move-to-next-stage`, { reason })
    return response.data
  }

  async moveToStage(dealId: string, stageId: string, reason?: string): Promise<Deal> {
    const response = await this.apiClient.post<Deal>(`/deals/${dealId}/move-to-stage`, { stageId, reason })
    return response.data
  }

  // Analytics
  async getAdvancedAnalytics(dateFrom?: string, dateTo?: string): Promise<any> {
    const params = new URLSearchParams()
    if (dateFrom) params.append('dateFrom', dateFrom)
    if (dateTo) params.append('dateTo', dateTo)

    const response = await this.apiClient.get<any>(`/deals/analytics/advanced?${params}`)
    return response.data
  }
}

export const dealService = new DealService()
export default dealService
