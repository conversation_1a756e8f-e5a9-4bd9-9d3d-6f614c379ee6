import React from 'react'
import { TargetCompanySearch } from '@/components/target-companies'
import { TargetCompany } from '@/services/target-company.service'

export function TargetCompaniesPage() {
  const handleCompanySelect = (company: TargetCompany) => {
    // Navigate to company detail page or open detail modal
    console.log('Selected company:', company)
    // TODO: Implement navigation to company detail page
  }

  return (
    <div className="container mx-auto py-6">
      <TargetCompanySearch 
        onCompanySelect={handleCompanySelect}
        showCreateButton={true}
      />
    </div>
  )
}

export default TargetCompaniesPage
