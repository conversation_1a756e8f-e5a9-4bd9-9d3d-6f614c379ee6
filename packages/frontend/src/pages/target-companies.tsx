import {
    CreateTargetCompanyDialog,
    OpportunityPipelineDashboard,
    TargetCompanySearch
} from '@/components/target-companies'
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { TargetCompany } from '@/services/target-company.service'
import { useState } from 'react'

export function TargetCompaniesPage() {
  const [selectedCompany, setSelectedCompany] = useState<TargetCompany | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  const handleCompanySelect = (company: TargetCompany) => {
    setSelectedCompany(company)
    // TODO: Navigate to company detail page or open detail modal
    console.log('Selected company:', company)
  }

  const handleCreateCompany = () => {
    setShowCreateDialog(true)
  }

  return (
    <div className="container mx-auto py-6">
      <Tabs defaultValue="search" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="search">Search & Discovery</TabsTrigger>
          <TabsTrigger value="pipeline">Pipeline Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          <TargetCompanySearch
            onCompanySelect={handleCompanySelect}
            showCreateButton={true}
          />
        </TabsContent>

        <TabsContent value="pipeline" className="space-y-6">
          <OpportunityPipelineDashboard
            onCompanySelect={handleCompanySelect}
            onCreateCompany={handleCreateCompany}
          />
        </TabsContent>
      </Tabs>

      <CreateTargetCompanyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  )
}

export default TargetCompaniesPage
