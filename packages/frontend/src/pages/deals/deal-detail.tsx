import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { 
  ArrowLeft,
  Edit,
  MoreHorizontal,
  DollarSign,
  Calendar,
  Target,
  TrendingUp,
  Users,
  FileText,
  CheckSquare,
  AlertTriangle,
  Building,
  Globe,
  Clock
} from 'lucide-react'
import { useDeal, useUpdateDeal, useMoveToNextStage } from '@/hooks/use-deals'
import DealForm from '@/components/deals/deal-form'
import DealTimeline from '@/components/deals/deal-timeline'
import DealDocuments from '@/components/deals/deal-documents'
import { Deal, DealPriority, RiskLevel } from '@/services/deal.service'
import { formatCurrency, formatDate, formatDistanceToNow } from '@/lib/utils'

export function DealDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const { data: deal, isLoading, error } = useDeal(id!)
  const updateMutation = useUpdateDeal()
  const moveToNextStageMutation = useMoveToNextStage()

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3" />
          <div className="h-64 bg-muted rounded" />
          <div className="h-96 bg-muted rounded" />
        </div>
      </div>
    )
  }

  if (error || !deal) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-4">Deal Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The deal you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button onClick={() => navigate('/deals')}>
            Back to Deals
          </Button>
        </div>
      </div>
    )
  }

  const handleEdit = (data: any) => {
    updateMutation.mutate(
      { id: deal.id, data },
      {
        onSuccess: () => {
          setShowEditDialog(false)
        }
      }
    )
  }

  const handleMoveToNextStage = () => {
    moveToNextStageMutation.mutate({ dealId: deal.id })
  }

  const getPriorityColor = (priority: DealPriority) => {
    switch (priority) {
      case DealPriority.LOW: return 'bg-gray-100 text-gray-800'
      case DealPriority.MEDIUM: return 'bg-blue-100 text-blue-800'
      case DealPriority.HIGH: return 'bg-orange-100 text-orange-800'
      case DealPriority.URGENT: return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRiskColor = (risk: RiskLevel) => {
    switch (risk) {
      case RiskLevel.LOW: return 'bg-green-100 text-green-800'
      case RiskLevel.MEDIUM: return 'bg-yellow-100 text-yellow-800'
      case RiskLevel.HIGH: return 'bg-orange-100 text-orange-800'
      case RiskLevel.CRITICAL: return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/deals')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Deals
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{deal.title}</h1>
            <p className="text-muted-foreground">{deal.targetCompany}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setShowEditDialog(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          
          {deal.currentStage && (
            <Button 
              onClick={handleMoveToNextStage}
              disabled={moveToNextStageMutation.isPending}
            >
              {moveToNextStageMutation.isPending ? 'Moving...' : 'Next Stage'}
            </Button>
          )}
          
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Key metrics cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm font-medium">Deal Value</p>
                <p className="text-2xl font-bold">
                  {deal.dealValue ? formatCurrency(deal.dealValue, deal.currency) : 'TBD'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm font-medium">Current Stage</p>
                <p className="text-lg font-semibold">
                  {deal.currentStage?.name || deal.stage}
                </p>
                {deal.daysInCurrentStage && (
                  <p className="text-xs text-muted-foreground">
                    {deal.daysInCurrentStage} days
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm font-medium">Expected Close</p>
                <p className="text-lg font-semibold">
                  {deal.expectedCloseDate ? formatDate(deal.expectedCloseDate) : 'TBD'}
                </p>
                {deal.expectedCloseDate && (
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(deal.expectedCloseDate), { addSuffix: true })}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm font-medium">Health Score</p>
                <p className="text-2xl font-bold">
                  {deal.healthScore || 'N/A'}
                  {deal.healthScore && <span className="text-sm font-normal">/100</span>}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main content tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Deal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Building className="h-5 w-5" />
                  <span>Deal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Deal Type</p>
                    <p className="font-medium">{deal.dealType}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Source</p>
                    <p className="font-medium">{deal.dealSource}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Industry</p>
                    <p className="font-medium">{deal.targetIndustry || 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Priority</p>
                    <Badge className={getPriorityColor(deal.priority)}>
                      {deal.priority}
                    </Badge>
                  </div>
                </div>

                {deal.description && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Description</p>
                    <p className="text-sm">{deal.description}</p>
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  {deal.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Risk Assessment */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5" />
                  <span>Risk Assessment</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-2">Risk Level</p>
                  <Badge className={getRiskColor(deal.riskLevel)}>
                    {deal.riskLevel}
                  </Badge>
                </div>

                {deal.keyRisks && deal.keyRisks.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Key Risks</p>
                    <ul className="space-y-1">
                      {deal.keyRisks.map((risk, index) => (
                        <li key={index} className="text-sm flex items-start space-x-2">
                          <span className="text-destructive">•</span>
                          <span>{risk}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {deal.keyOpportunities && deal.keyOpportunities.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Key Opportunities</p>
                    <ul className="space-y-1">
                      {deal.keyOpportunities.map((opportunity, index) => (
                        <li key={index} className="text-sm flex items-start space-x-2">
                          <span className="text-green-600">•</span>
                          <span>{opportunity}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {deal.competitiveProcess && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Competitive Process</p>
                    <p className="text-sm">Yes</p>
                    {deal.competitors && deal.competitors.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-1">Competitors:</p>
                        <div className="flex flex-wrap gap-1">
                          {deal.competitors.map((competitor, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {competitor}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Notes */}
          {(deal.internalNotes || deal.nextSteps) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Notes & Next Steps</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {deal.internalNotes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Internal Notes</p>
                    <p className="text-sm whitespace-pre-wrap">{deal.internalNotes}</p>
                  </div>
                )}

                {deal.nextSteps && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Next Steps</p>
                    <p className="text-sm whitespace-pre-wrap">{deal.nextSteps}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="timeline">
          <DealTimeline deal={deal} />
        </TabsContent>

        <TabsContent value="documents">
          <DealDocuments dealId={deal.id} />
        </TabsContent>

        <TabsContent value="team">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Deal Team</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Team management coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckSquare className="h-5 w-5" />
                <span>Tasks</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Task management coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Deal</DialogTitle>
          </DialogHeader>
          <DealForm
            deal={deal}
            onSubmit={handleEdit}
            onCancel={() => setShowEditDialog(false)}
            isLoading={updateMutation.isPending}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default DealDetailPage
