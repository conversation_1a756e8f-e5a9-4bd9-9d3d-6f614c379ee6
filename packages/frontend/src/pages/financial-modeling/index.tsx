import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Calculator, 
  TrendingUp, 
  BarChart3, 
  Pie<PERSON>hart, 
  FileSpreadsheet,
  Plus,
  Settings,
  Download,
  Share2
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { DCFModelBuilder } from '@/components/financial-modeling/DCFModelBuilder'
import { ComparableCompanyAnalysis } from '@/components/financial-modeling/ComparableCompanyAnalysis'
import { PrecedentTransactionAnalysis } from '@/components/financial-modeling/PrecedentTransactionAnalysis'
import { LBOAnalysis } from '@/components/financial-modeling/LBOAnalysis'
import { SensitivityAnalysis } from '@/components/financial-modeling/SensitivityAnalysis'
import { ScenarioManagement } from '@/components/financial-modeling/ScenarioManagement'
import { ValuationSummary } from '@/components/financial-modeling/ValuationSummary'
import { ModelTemplates } from '@/components/financial-modeling/ModelTemplates'

interface FinancialModel {
  id: string
  name: string
  type: 'DCF' | 'CCA' | 'PTA' | 'LBO'
  status: 'draft' | 'complete' | 'review'
  lastModified: Date
  createdBy: string
  valuation?: {
    low: number
    mid: number
    high: number
  }
}

export default function FinancialModelingPage() {
  const [activeTab, setActiveTab] = useState('dcf')
  const [models, setModels] = useState<FinancialModel[]>([])
  const [selectedModel, setSelectedModel] = useState<FinancialModel | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadRecentModels()
  }, [])

  const loadRecentModels = async () => {
    try {
      setIsLoading(true)
      // Mock data - replace with actual API call
      const mockModels: FinancialModel[] = [
        {
          id: '1',
          name: 'TechCorp Acquisition DCF',
          type: 'DCF',
          status: 'complete',
          lastModified: new Date('2024-01-15'),
          createdBy: 'John Doe',
          valuation: { low: *********, mid: **********, high: ********** }
        },
        {
          id: '2',
          name: 'SaaS Company Comparables',
          type: 'CCA',
          status: 'review',
          lastModified: new Date('2024-01-14'),
          createdBy: 'Jane Smith',
          valuation: { low: *********, mid: *********, high: ********** }
        },
        {
          id: '3',
          name: 'Healthcare LBO Analysis',
          type: 'LBO',
          status: 'draft',
          lastModified: new Date('2024-01-13'),
          createdBy: 'Mike Johnson'
        }
      ]
      setModels(mockModels)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load financial models',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateNewModel = () => {
    // Navigate to model creation or open modal
    toast({
      title: 'New Model',
      description: 'Creating new financial model...'
    })
  }

  const handleExportModel = () => {
    toast({
      title: 'Export',
      description: 'Exporting model to Excel...'
    })
  }

  const handleShareModel = () => {
    toast({
      title: 'Share',
      description: 'Sharing model with team...'
    })
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'complete':
        return 'default'
      case 'review':
        return 'secondary'
      case 'draft':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Financial Modeling</h1>
          <p className="text-muted-foreground">
            Build comprehensive valuation models and perform scenario analysis
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExportModel}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={handleShareModel}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button onClick={handleCreateNewModel}>
            <Plus className="h-4 w-4 mr-2" />
            New Model
          </Button>
        </div>
      </div>

      {/* Recent Models Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Recent Models
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {models.map((model) => (
              <Card 
                key={model.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setSelectedModel(model)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-sm">{model.name}</h3>
                    <Badge variant={getStatusBadgeVariant(model.status)}>
                      {model.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
                    <span>{model.type}</span>
                    <span>•</span>
                    <span>{model.lastModified.toLocaleDateString()}</span>
                  </div>
                  {model.valuation && (
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">Valuation Range</div>
                      <div className="text-sm font-medium">
                        {formatCurrency(model.valuation.low)} - {formatCurrency(model.valuation.high)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Mid: {formatCurrency(model.valuation.mid)}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Modeling Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="dcf" className="flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            DCF
          </TabsTrigger>
          <TabsTrigger value="cca" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            CCA
          </TabsTrigger>
          <TabsTrigger value="pta" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            PTA
          </TabsTrigger>
          <TabsTrigger value="lbo" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            LBO
          </TabsTrigger>
          <TabsTrigger value="sensitivity" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Sensitivity
          </TabsTrigger>
          <TabsTrigger value="scenarios" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Scenarios
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            Summary
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dcf" className="space-y-6">
          <DCFModelBuilder />
        </TabsContent>

        <TabsContent value="cca" className="space-y-6">
          <ComparableCompanyAnalysis />
        </TabsContent>

        <TabsContent value="pta" className="space-y-6">
          <PrecedentTransactionAnalysis />
        </TabsContent>

        <TabsContent value="lbo" className="space-y-6">
          <LBOAnalysis />
        </TabsContent>

        <TabsContent value="sensitivity" className="space-y-6">
          <SensitivityAnalysis />
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-6">
          <ScenarioManagement />
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          <ValuationSummary />
        </TabsContent>
      </Tabs>

      {/* Model Templates Modal/Sidebar */}
      <ModelTemplates />
    </div>
  )
}
