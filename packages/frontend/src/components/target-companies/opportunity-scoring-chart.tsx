import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  TrendingUp, 
  Target, 
  DollarSign, 
  BarChart3,
  Star,
  Award
} from 'lucide-react'
import { TargetCompany } from '@/services/target-company.service'

interface OpportunityScoringChartProps {
  company: TargetCompany
  className?: string
}

interface ScoreMetric {
  label: string
  value: number
  icon: React.ReactNode
  color: string
  description: string
}

export function OpportunityScoringChart({ 
  company, 
  className = '' 
}: OpportunityScoringChartProps) {
  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getScoreGradient = (score: number): string => {
    if (score >= 80) return 'from-green-500 to-green-600'
    if (score >= 60) return 'from-yellow-500 to-yellow-600'
    if (score >= 40) return 'from-orange-500 to-orange-600'
    return 'from-red-500 to-red-600'
  }

  const getScoreLabel = (score: number): string => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    if (score >= 40) return 'Fair'
    return 'Poor'
  }

  const scoreMetrics: ScoreMetric[] = [
    {
      label: 'Opportunity Score',
      value: company.opportunityScore || 0,
      icon: <Target className="h-4 w-4" />,
      color: getScoreColor(company.opportunityScore || 0),
      description: 'Overall opportunity assessment based on market potential and business metrics'
    },
    {
      label: 'Strategic Fit',
      value: company.strategicFit || 0,
      icon: <Award className="h-4 w-4" />,
      color: getScoreColor(company.strategicFit || 0),
      description: 'Alignment with strategic objectives and portfolio synergies'
    },
    {
      label: 'Financial Health',
      value: company.financialHealth || 0,
      icon: <DollarSign className="h-4 w-4" />,
      color: getScoreColor(company.financialHealth || 0),
      description: 'Financial stability, profitability, and growth metrics'
    }
  ]

  const overallScore = Math.round(
    (scoreMetrics.reduce((sum, metric) => sum + metric.value, 0) / scoreMetrics.length)
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>Opportunity Scoring</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <div className="relative">
              <div className="mx-auto w-32 h-32 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                <div className={`w-28 h-28 rounded-full bg-gradient-to-br ${getScoreGradient(overallScore)} flex items-center justify-center text-white`}>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{overallScore}</div>
                    <div className="text-xs opacity-90">/ 100</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-1">
              <h3 className="text-lg font-semibold">{company.name}</h3>
              <Badge 
                variant="outline" 
                className={`${getScoreColor(overallScore)} border-current`}
              >
                {getScoreLabel(overallScore)} Opportunity
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Scoring Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Scoring Breakdown</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {scoreMetrics.map((metric, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={metric.color}>
                    {metric.icon}
                  </div>
                  <span className="font-medium text-sm">{metric.label}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`font-bold ${metric.color}`}>
                    {metric.value}
                  </span>
                  <span className="text-muted-foreground text-sm">/ 100</span>
                </div>
              </div>
              
              <Progress 
                value={metric.value} 
                className="h-2"
              />
              
              <p className="text-xs text-muted-foreground">
                {metric.description}
              </p>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Score Factors */}
      <Card>
        <CardHeader>
          <CardTitle>Key Factors</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Revenue Factor */}
            {company.revenue && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Revenue</span>
                </div>
                <div className="text-lg font-bold">
                  ${(company.revenue / 1000000).toFixed(1)}M
                </div>
                <div className="text-xs text-muted-foreground">
                  Annual revenue contributes to opportunity score
                </div>
              </div>
            )}

            {/* Employee Count Factor */}
            {company.employees && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Team Size</span>
                </div>
                <div className="text-lg font-bold">
                  {company.employees.toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">
                  Employee count indicates scale and maturity
                </div>
              </div>
            )}

            {/* Industry Factor */}
            {company.industry && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Industry</span>
                </div>
                <div className="text-lg font-bold">
                  {company.industry}
                </div>
                <div className="text-xs text-muted-foreground">
                  Strategic industry alignment
                </div>
              </div>
            )}

            {/* Geographic Factor */}
            {(company.city || company.country) && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Award className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Location</span>
                </div>
                <div className="text-lg font-bold">
                  {[company.city, company.country].filter(Boolean).join(', ')}
                </div>
                <div className="text-xs text-muted-foreground">
                  Geographic market presence
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Scoring Methodology */}
      <Card>
        <CardHeader>
          <CardTitle>Scoring Methodology</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">Opportunity Score (0-100)</h4>
              <p className="text-muted-foreground">
                Calculated based on revenue size, employee count, industry alignment, 
                and market position indicators.
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Strategic Fit (0-100)</h4>
              <p className="text-muted-foreground">
                Measures alignment with strategic objectives, geographic preferences, 
                and portfolio synergies.
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Financial Health (0-100)</h4>
              <p className="text-muted-foreground">
                Assesses financial stability, profitability margins, and growth trajectory 
                based on available financial data.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
