import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useCreateTargetCompany } from '@/hooks/use-target-companies'
import { CreateTargetCompanyRequest } from '@/services/target-company.service'

interface CreateTargetCompanyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const industryOptions = [
  'Technology',
  'Healthcare',
  'Financial Services',
  'Manufacturing',
  'Retail',
  'Energy',
  'Real Estate',
  'Transportation',
  'Media & Entertainment',
  'Education',
  'Agriculture',
  'Construction',
  'Telecommunications',
  'Hospitality',
  'Professional Services'
]

const countryOptions = [
  'United States',
  'Canada',
  'United Kingdom',
  'Germany',
  'France',
  'Australia',
  'Japan',
  'Singapore',
  'Netherlands',
  'Switzerland',
  'Sweden',
  'Denmark',
  'Norway',
  'Ireland',
  'Belgium'
]

export function CreateTargetCompanyDialog({
  open,
  onOpenChange
}: CreateTargetCompanyDialogProps) {
  const [formData, setFormData] = useState<CreateTargetCompanyRequest>({
    name: '',
    description: '',
    website: '',
    industry: '',
    country: '',
    city: '',
    revenue: undefined,
    employees: undefined,
    foundedYear: undefined,
    primaryContact: '',
    contactEmail: '',
    contactPhone: '',
    source: '',
    tags: [],
    notes: ''
  })

  const createMutation = useCreateTargetCompany()

  const handleInputChange = (field: keyof CreateTargetCompanyRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      return
    }

    try {
      await createMutation.mutateAsync(formData)
      onOpenChange(false)
      // Reset form
      setFormData({
        name: '',
        description: '',
        website: '',
        industry: '',
        country: '',
        city: '',
        revenue: undefined,
        employees: undefined,
        foundedYear: undefined,
        primaryContact: '',
        contactEmail: '',
        contactPhone: '',
        source: '',
        tags: [],
        notes: ''
      })
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    // Reset form
    setFormData({
      name: '',
      description: '',
      website: '',
      industry: '',
      country: '',
      city: '',
      revenue: undefined,
      employees: undefined,
      foundedYear: undefined,
      primaryContact: '',
      contactEmail: '',
      contactPhone: '',
      source: '',
      tags: [],
      notes: ''
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Target Company</DialogTitle>
          <DialogDescription>
            Add a new target company to your pipeline for evaluation and tracking.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Basic Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Company Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter company name"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of the company and its business"
                rows={3}
              />
            </div>
          </div>

          {/* Industry & Location */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Industry & Location</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="industry">Industry</Label>
                <Select value={formData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industryOptions.map((industry) => (
                      <SelectItem key={industry} value={industry}>
                        {industry}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange('country', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countryOptions.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="Enter city"
              />
            </div>
          </div>

          {/* Financial Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Financial Information</h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="revenue">Revenue ($)</Label>
                <Input
                  id="revenue"
                  type="number"
                  value={formData.revenue || ''}
                  onChange={(e) => handleInputChange('revenue', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="Annual revenue"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="employees">Employees</Label>
                <Input
                  id="employees"
                  type="number"
                  value={formData.employees || ''}
                  onChange={(e) => handleInputChange('employees', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="Number of employees"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="foundedYear">Founded Year</Label>
                <Input
                  id="foundedYear"
                  type="number"
                  min="1800"
                  max={new Date().getFullYear()}
                  value={formData.foundedYear || ''}
                  onChange={(e) => handleInputChange('foundedYear', e.target.value ? Number(e.target.value) : undefined)}
                  placeholder="Year founded"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Contact Information</h3>
            
            <div className="space-y-2">
              <Label htmlFor="primaryContact">Primary Contact</Label>
              <Input
                id="primaryContact"
                value={formData.primaryContact}
                onChange={(e) => handleInputChange('primaryContact', e.target.value)}
                placeholder="Contact person name"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contactEmail">Contact Email</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="contactPhone">Contact Phone</Label>
                <Input
                  id="contactPhone"
                  type="tel"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="+****************"
                />
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Additional Information</h3>
            
            <div className="space-y-2">
              <Label htmlFor="source">Source</Label>
              <Input
                id="source"
                value={formData.source}
                onChange={(e) => handleInputChange('source', e.target.value)}
                placeholder="How did you discover this company?"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Additional notes or observations"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!formData.name.trim() || createMutation.isPending}
            >
              {createMutation.isPending ? 'Creating...' : 'Create Company'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
