import React from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  Building2, 
  MapPin, 
  Users, 
  DollarSign,
  TrendingUp,
  Star,
  Calendar,
  Globe,
  Phone,
  Mail,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { TargetCompany, TargetCompanyStatus } from '@/services/target-company.service'
import { formatCurrency, formatNumber, formatDate } from '@/lib/utils'

interface TargetCompanyCardProps {
  company: TargetCompany
  onClick?: () => void
  onEdit?: () => void
  onDelete?: () => void
  onMoveStage?: () => void
  onCreateDeal?: () => void
  className?: string
}

const statusConfig = {
  [TargetCompanyStatus.ACTIVE]: {
    label: 'Active',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  [TargetCompanyStatus.CONTACTED]: {
    label: 'Contacted',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
  [TargetCompanyStatus.IN_DISCUSSION]: {
    label: 'In Discussion',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
  },
  [TargetCompanyStatus.QUALIFIED]: {
    label: 'Qualified',
    color: 'bg-green-100 text-green-800 border-green-200',
  },
  [TargetCompanyStatus.DISQUALIFIED]: {
    label: 'Disqualified',
    color: 'bg-red-100 text-red-800 border-red-200',
  },
  [TargetCompanyStatus.ACQUIRED]: {
    label: 'Acquired',
    color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
  },
  [TargetCompanyStatus.INACTIVE]: {
    label: 'Inactive',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
  },
}

export function TargetCompanyCard({
  company,
  onClick,
  onEdit,
  onDelete,
  onMoveStage,
  onCreateDeal,
  className = ''
}: TargetCompanyCardProps) {
  const statusInfo = statusConfig[company.status]
  
  const getCompanyInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-400'
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on interactive elements
    if ((e.target as HTMLElement).closest('button, a')) {
      return
    }
    onClick?.()
  }

  return (
    <Card 
      className={`hover:shadow-md transition-shadow cursor-pointer ${className}`}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <Avatar className="h-10 w-10 flex-shrink-0">
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {getCompanyInitials(company.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold text-sm truncate">{company.name}</h3>
                {company.website && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(company.website, '_blank')
                    }}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                )}
              </div>
              
              <Badge 
                variant="outline" 
                className={`text-xs ${statusInfo.color}`}
              >
                {statusInfo.label}
              </Badge>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onEdit}>
                Edit Company
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onMoveStage}>
                Move Stage
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onCreateDeal}>
                Create Deal
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onDelete} className="text-red-600">
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* Company Description */}
        {company.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {company.description}
          </p>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          {company.industry && (
            <div className="flex items-center space-x-1">
              <Building2 className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground truncate">{company.industry}</span>
            </div>
          )}
          
          {(company.city || company.country) && (
            <div className="flex items-center space-x-1">
              <MapPin className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground truncate">
                {[company.city, company.country].filter(Boolean).join(', ')}
              </span>
            </div>
          )}
          
          {company.employees && (
            <div className="flex items-center space-x-1">
              <Users className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                {formatNumber(company.employees)} employees
              </span>
            </div>
          )}
          
          {company.revenue && (
            <div className="flex items-center space-x-1">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                {formatCurrency(company.revenue)}
              </span>
            </div>
          )}
        </div>

        {/* Opportunity Scoring */}
        {(company.opportunityScore || company.strategicFit || company.financialHealth) && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Opportunity Score</span>
              <span className={`font-medium ${getScoreColor(company.opportunityScore)}`}>
                {company.opportunityScore || 0}/100
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Strategic Fit</span>
                <span className={getScoreColor(company.strategicFit)}>
                  {company.strategicFit || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Financial Health</span>
                <span className={getScoreColor(company.financialHealth)}>
                  {company.financialHealth || 0}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Tags */}
        {company.tags && company.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {company.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs px-2 py-0">
                {tag}
              </Badge>
            ))}
            {company.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs px-2 py-0">
                +{company.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Contact Information */}
        {(company.primaryContact || company.contactEmail || company.contactPhone) && (
          <div className="space-y-1 text-xs">
            {company.primaryContact && (
              <div className="flex items-center space-x-1">
                <span className="text-muted-foreground">Contact:</span>
                <span className="truncate">{company.primaryContact}</span>
              </div>
            )}
            
            <div className="flex items-center space-x-3">
              {company.contactEmail && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.location.href = `mailto:${company.contactEmail}`
                  }}
                >
                  <Mail className="h-3 w-3 mr-1" />
                  Email
                </Button>
              )}
              
              {company.contactPhone && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.location.href = `tel:${company.contactPhone}`
                  }}
                >
                  <Phone className="h-3 w-3 mr-1" />
                  Call
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Activity Summary */}
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
          <div className="flex items-center space-x-3">
            {company._count?.interactions && (
              <span>{company._count.interactions} interactions</span>
            )}
            {company._count?.deals && (
              <span>{company._count.deals} deals</span>
            )}
          </div>
          
          {company.lastContactDate && (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>Last contact {formatDate(company.lastContactDate)}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
