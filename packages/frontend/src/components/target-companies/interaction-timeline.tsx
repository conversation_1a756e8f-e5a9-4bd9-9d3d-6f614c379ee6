import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Mail, 
  Phone, 
  Calendar, 
  Users, 
  FileText, 
  Clock,
  Plus,
  MessageSquare,
  Video,
  Coffee,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { 
  TargetCompanyInteraction, 
  InteractionType 
} from '@/services/target-company.service'
import { formatDate, getInitials } from '@/lib/utils'
import { CreateInteractionDialog } from './create-interaction-dialog'

interface InteractionTimelineProps {
  targetId: string
  interactions: TargetCompanyInteraction[]
  onCreateInteraction?: () => void
  className?: string
}

const interactionConfig = {
  [InteractionType.EMAIL]: {
    icon: Mail,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    label: 'Email'
  },
  [InteractionType.PHONE_CALL]: {
    icon: Phone,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    label: 'Phone Call'
  },
  [InteractionType.MEETING]: {
    icon: Users,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    label: 'Meeting'
  },
  [InteractionType.CONFERENCE]: {
    icon: Video,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
    label: 'Conference'
  },
  [InteractionType.RESEARCH]: {
    icon: FileText,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    label: 'Research'
  },
  [InteractionType.NOTE]: {
    icon: MessageSquare,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    label: 'Note'
  },
  [InteractionType.FOLLOW_UP]: {
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    label: 'Follow-up'
  }
}

interface TimelineItemProps {
  interaction: TargetCompanyInteraction
  isLast: boolean
}

function TimelineItem({ interaction, isLast }: TimelineItemProps) {
  const config = interactionConfig[interaction.type]
  const IconComponent = config.icon

  const isCompleted = !!interaction.completedAt
  const isScheduled = !!interaction.scheduledAt && !interaction.completedAt
  const needsFollowUp = interaction.followUpRequired && !interaction.followUpDate

  return (
    <div className="flex space-x-3">
      {/* Timeline Line */}
      <div className="flex flex-col items-center">
        <div className={`w-8 h-8 rounded-full ${config.bgColor} flex items-center justify-center`}>
          <IconComponent className={`h-4 w-4 ${config.color}`} />
        </div>
        {!isLast && <div className="w-px h-6 bg-gray-200 mt-2" />}
      </div>

      {/* Content */}
      <div className="flex-1 pb-6">
        <div className="space-y-2">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-sm">
                  {interaction.subject || config.label}
                </h4>
                <Badge variant="outline" className="text-xs">
                  {config.label}
                </Badge>
                {isCompleted && (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                )}
                {needsFollowUp && (
                  <AlertCircle className="h-3 w-3 text-yellow-600" />
                )}
              </div>
              
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <Avatar className="h-5 w-5">
                  <AvatarFallback className="text-xs">
                    {getInitials(`${interaction.user.firstName} ${interaction.user.lastName}`)}
                  </AvatarFallback>
                </Avatar>
                <span>{interaction.user.firstName} {interaction.user.lastName}</span>
                <span>•</span>
                <span>
                  {isCompleted 
                    ? formatDate(interaction.completedAt!)
                    : isScheduled 
                    ? `Scheduled for ${formatDate(interaction.scheduledAt!)}`
                    : formatDate(interaction.createdAt)
                  }
                </span>
              </div>
            </div>
          </div>

          {/* Description */}
          {interaction.description && (
            <p className="text-sm text-muted-foreground">
              {interaction.description}
            </p>
          )}

          {/* Outcome */}
          {interaction.outcome && (
            <div className="space-y-1">
              <h5 className="text-xs font-medium text-muted-foreground">Outcome</h5>
              <p className="text-sm">{interaction.outcome}</p>
            </div>
          )}

          {/* Follow-up */}
          {interaction.followUpRequired && (
            <div className="space-y-1">
              <h5 className="text-xs font-medium text-muted-foreground">Follow-up</h5>
              <div className="flex items-center space-x-2">
                {interaction.followUpDate ? (
                  <Badge variant="outline" className="text-xs">
                    Due {formatDate(interaction.followUpDate)}
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="text-xs">
                    Follow-up Required
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export function InteractionTimeline({ 
  targetId,
  interactions, 
  onCreateInteraction,
  className = '' 
}: InteractionTimelineProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // Sort interactions by date (most recent first)
  const sortedInteractions = [...interactions].sort((a, b) => {
    const dateA = new Date(a.completedAt || a.scheduledAt || a.createdAt)
    const dateB = new Date(b.completedAt || b.scheduledAt || b.createdAt)
    return dateB.getTime() - dateA.getTime()
  })

  const upcomingInteractions = sortedInteractions.filter(
    interaction => interaction.scheduledAt && !interaction.completedAt
  )

  const completedInteractions = sortedInteractions.filter(
    interaction => interaction.completedAt
  )

  const pendingFollowUps = sortedInteractions.filter(
    interaction => interaction.followUpRequired && !interaction.followUpDate
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Interaction Timeline</h3>
          <p className="text-sm text-muted-foreground">
            Track all communications and activities
          </p>
        </div>
        
        <Button 
          size="sm" 
          onClick={() => setShowCreateDialog(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Interaction
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Upcoming</p>
                <p className="text-xl font-bold">{upcomingInteractions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-xl font-bold">{completedInteractions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">Follow-ups</p>
                <p className="text-xl font-bold">{pendingFollowUps.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Activity Timeline</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sortedInteractions.length > 0 ? (
            <div className="space-y-0">
              {sortedInteractions.map((interaction, index) => (
                <TimelineItem
                  key={interaction.id}
                  interaction={interaction}
                  isLast={index === sortedInteractions.length - 1}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No interactions recorded yet</p>
              <p className="text-xs mt-1">Start by adding your first interaction</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upcoming Activities */}
      {upcomingInteractions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Upcoming Activities</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingInteractions.slice(0, 3).map((interaction) => {
                const config = interactionConfig[interaction.type]
                const IconComponent = config.icon
                
                return (
                  <div key={interaction.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className={`w-6 h-6 rounded-full ${config.bgColor} flex items-center justify-center`}>
                      <IconComponent className={`h-3 w-3 ${config.color}`} />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">
                        {interaction.subject || config.label}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(interaction.scheduledAt!)} with {interaction.user.firstName} {interaction.user.lastName}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Create Interaction Dialog */}
      <CreateInteractionDialog
        targetId={targetId}
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          setShowCreateDialog(false)
          onCreateInteraction?.()
        }}
      />
    </div>
  )
}
