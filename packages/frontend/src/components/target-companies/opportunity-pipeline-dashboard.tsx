import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Building2, 
  TrendingUp, 
  Users, 
  DollarSign,
  MoreHorizontal,
  Plus,
  Filter,
  BarChart3,
  ArrowRight,
  Target
} from 'lucide-react'
import { 
  useTargetCompanies, 
  useOpportunityPipeline, 
  usePipelineMetrics,
  useMoveToStage,
  useMoveToNextStage
} from '@/hooks/use-target-companies'
import { 
  TargetCompany, 
  TargetCompanyStatus, 
  OpportunityPipeline,
  OpportunityMetrics 
} from '@/services/target-company.service'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { TargetCompanyCard } from './target-company-card'

interface OpportunityPipelineDashboardProps {
  onCompanySelect?: (company: TargetCompany) => void
  onCreateCompany?: () => void
  className?: string
}

interface PipelineStageProps {
  stage: {
    id: string
    name: string
    status: TargetCompanyStatus
    color?: string
    companies: TargetCompany[]
  }
  metrics?: OpportunityMetrics
  onCompanySelect?: (company: TargetCompany) => void
  onMoveCompany?: (company: TargetCompany, toStatus: TargetCompanyStatus) => void
}

function PipelineStage({ 
  stage, 
  metrics, 
  onCompanySelect, 
  onMoveCompany 
}: PipelineStageProps) {
  const moveToStageMutation = useMoveToStage()
  const moveToNextStageMutation = useMoveToNextStage()

  const handleMoveToNext = async (company: TargetCompany) => {
    try {
      await moveToNextStageMutation.mutateAsync({
        id: company.id,
        reason: `Moved from ${stage.name} via pipeline dashboard`
      })
    } catch (error) {
      console.error('Failed to move company to next stage:', error)
    }
  }

  const handleMoveToStage = async (company: TargetCompany, toStatus: TargetCompanyStatus) => {
    try {
      await moveToStageMutation.mutateAsync({
        id: company.id,
        status: toStatus,
        reason: `Moved to ${toStatus} via pipeline dashboard`
      })
    } catch (error) {
      console.error('Failed to move company to stage:', error)
    }
  }

  const totalValue = stage.companies.reduce((sum, company) => 
    sum + (company.revenue || 0), 0
  )

  const averageScore = stage.companies.length > 0
    ? Math.round(stage.companies.reduce((sum, company) => 
        sum + (company.opportunityScore || 0), 0
      ) / stage.companies.length)
    : 0

  return (
    <div className="flex-1 min-w-[300px]">
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: stage.color || '#6B7280' }}
              />
              <CardTitle className="text-sm font-medium">{stage.name}</CardTitle>
              <Badge variant="secondary" className="text-xs">
                {stage.companies.length}
              </Badge>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Filter className="h-4 w-4 mr-2" />
                  Filter Stage
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Stage Metrics */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="space-y-1">
              <div className="text-muted-foreground">Total Value</div>
              <div className="font-medium">{formatCurrency(totalValue)}</div>
            </div>
            <div className="space-y-1">
              <div className="text-muted-foreground">Avg Score</div>
              <div className="font-medium">{averageScore}/100</div>
            </div>
          </div>

          {/* Conversion Rate */}
          {metrics && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Conversion Rate</span>
                <span className="font-medium">{metrics.conversionRate.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.conversionRate} className="h-1" />
            </div>
          )}
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-3 max-h-[600px] overflow-y-auto">
            {stage.companies.map((company) => (
              <div key={company.id} className="group relative">
                <div 
                  className="cursor-pointer"
                  onClick={() => onCompanySelect?.(company)}
                >
                  <Card className="hover:shadow-sm transition-shadow">
                    <CardContent className="p-3">
                      <div className="space-y-2">
                        {/* Company Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{company.name}</h4>
                            {company.industry && (
                              <p className="text-xs text-muted-foreground truncate">
                                {company.industry}
                              </p>
                            )}
                          </div>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleMoveToNext(company)}>
                                <ArrowRight className="h-4 w-4 mr-2" />
                                Move to Next Stage
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => onCompanySelect?.(company)}>
                                View Details
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Key Metrics */}
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {company.revenue && (
                            <div className="flex items-center space-x-1">
                              <DollarSign className="h-3 w-3 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                {formatCurrency(company.revenue)}
                              </span>
                            </div>
                          )}
                          
                          {company.employees && (
                            <div className="flex items-center space-x-1">
                              <Users className="h-3 w-3 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                {formatNumber(company.employees)}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Opportunity Score */}
                        {company.opportunityScore && (
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                              <span className="text-muted-foreground">Opportunity Score</span>
                              <span className="font-medium">{company.opportunityScore}/100</span>
                            </div>
                            <Progress value={company.opportunityScore} className="h-1" />
                          </div>
                        )}

                        {/* Location */}
                        {(company.city || company.country) && (
                          <div className="text-xs text-muted-foreground">
                            {[company.city, company.country].filter(Boolean).join(', ')}
                          </div>
                        )}

                        {/* Tags */}
                        {company.tags && company.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {company.tags.slice(0, 2).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                                {tag}
                              </Badge>
                            ))}
                            {company.tags.length > 2 && (
                              <Badge variant="outline" className="text-xs px-1 py-0">
                                +{company.tags.length - 2}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}

            {stage.companies.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Building2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No companies in this stage</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function OpportunityPipelineDashboard({
  onCompanySelect,
  onCreateCompany,
  className = ''
}: OpportunityPipelineDashboardProps) {
  const [selectedFilters, setSelectedFilters] = useState<{
    industry?: string[]
    scoreRange?: [number, number]
  }>({})

  // Fetch pipeline data
  const { data: pipeline, isLoading: pipelineLoading } = useOpportunityPipeline()
  const { data: metrics, isLoading: metricsLoading } = usePipelineMetrics()
  const { data: companiesData, isLoading: companiesLoading } = useTargetCompanies(
    selectedFilters,
    'opportunityScore',
    'desc',
    1,
    1000 // Get all companies for pipeline view
  )

  const companies = companiesData?.companies || []

  // Group companies by pipeline stage
  const stageData = useMemo(() => {
    if (!pipeline || !companies) return []

    return pipeline.stages.map(stage => {
      const stageCompanies = companies.filter(company => company.status === stage.status)
      const stageMetrics = metrics?.find(m => m.stageId === stage.id)

      return {
        ...stage,
        companies: stageCompanies,
        metrics: stageMetrics
      }
    })
  }, [pipeline, companies, metrics])

  const totalCompanies = companies.length
  const totalValue = companies.reduce((sum, company) => sum + (company.revenue || 0), 0)
  const averageScore = totalCompanies > 0
    ? Math.round(companies.reduce((sum, company) => sum + (company.opportunityScore || 0), 0) / totalCompanies)
    : 0

  if (pipelineLoading || companiesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Deal Sourcing Pipeline</h2>
          <p className="text-muted-foreground">
            Track target companies through your opportunity pipeline
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          {onCreateCompany && (
            <Button onClick={onCreateCompany}>
              <Plus className="h-4 w-4 mr-2" />
              Add Company
            </Button>
          )}
        </div>
      </div>

      {/* Pipeline Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Total Companies</p>
                <p className="text-2xl font-bold">{totalCompanies}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">{formatCurrency(totalValue)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Avg Score</p>
                <p className="text-2xl font-bold">{averageScore}/100</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Qualified</p>
                <p className="text-2xl font-bold">
                  {companies.filter(c => c.status === TargetCompanyStatus.QUALIFIED).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pipeline Stages */}
      <div className="flex space-x-4 overflow-x-auto pb-4">
        {stageData.map((stage) => (
          <PipelineStage
            key={stage.id}
            stage={stage}
            metrics={stage.metrics}
            onCompanySelect={onCompanySelect}
          />
        ))}
      </div>
    </div>
  )
}
