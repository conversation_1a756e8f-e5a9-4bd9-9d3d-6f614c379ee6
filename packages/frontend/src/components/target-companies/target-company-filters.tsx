import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { X, RotateCcw } from 'lucide-react'
import { TargetCompanyFilters, TargetCompanyStatus } from '@/services/target-company.service'

interface TargetCompanyFiltersPanelProps {
  filters: TargetCompanyFilters
  onFiltersChange: (filters: TargetCompanyFilters) => void
  className?: string
}

const statusOptions = [
  { value: TargetCompanyStatus.ACTIVE, label: 'Active' },
  { value: TargetCompanyStatus.CONTACTED, label: 'Contacted' },
  { value: TargetCompanyStatus.IN_DISCUSSION, label: 'In Discussion' },
  { value: TargetCompanyStatus.QUALIFIED, label: 'Qualified' },
  { value: TargetCompanyStatus.DISQUALIFIED, label: 'Disqualified' },
  { value: TargetCompanyStatus.ACQUIRED, label: 'Acquired' },
  { value: TargetCompanyStatus.INACTIVE, label: 'Inactive' },
]

const industryOptions = [
  'Technology',
  'Healthcare',
  'Financial Services',
  'Manufacturing',
  'Retail',
  'Energy',
  'Real Estate',
  'Transportation',
  'Media & Entertainment',
  'Education',
  'Agriculture',
  'Construction',
  'Telecommunications',
  'Hospitality',
  'Professional Services'
]

const countryOptions = [
  'United States',
  'Canada',
  'United Kingdom',
  'Germany',
  'France',
  'Australia',
  'Japan',
  'Singapore',
  'Netherlands',
  'Switzerland',
  'Sweden',
  'Denmark',
  'Norway',
  'Ireland',
  'Belgium'
]

const commonTags = [
  'SaaS',
  'B2B',
  'B2C',
  'Enterprise',
  'SMB',
  'AI/ML',
  'Fintech',
  'Healthtech',
  'Edtech',
  'E-commerce',
  'Marketplace',
  'Platform',
  'Mobile',
  'Cloud',
  'IoT',
  'Blockchain',
  'Cybersecurity',
  'Data Analytics',
  'Automation',
  'Sustainability'
]

export function TargetCompanyFiltersPanel({
  filters,
  onFiltersChange,
  className = ''
}: TargetCompanyFiltersPanelProps) {
  const [localFilters, setLocalFilters] = useState<TargetCompanyFilters>(filters)

  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])

  const handleFilterUpdate = (key: keyof TargetCompanyFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const handleStatusToggle = (status: TargetCompanyStatus) => {
    const currentStatuses = localFilters.status || []
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status]
    
    handleFilterUpdate('status', newStatuses.length > 0 ? newStatuses : undefined)
  }

  const handleIndustryToggle = (industry: string) => {
    const currentIndustries = localFilters.industry || []
    const newIndustries = currentIndustries.includes(industry)
      ? currentIndustries.filter(i => i !== industry)
      : [...currentIndustries, industry]
    
    handleFilterUpdate('industry', newIndustries.length > 0 ? newIndustries : undefined)
  }

  const handleCountryToggle = (country: string) => {
    const currentCountries = localFilters.country || []
    const newCountries = currentCountries.includes(country)
      ? currentCountries.filter(c => c !== country)
      : [...currentCountries, country]
    
    handleFilterUpdate('country', newCountries.length > 0 ? newCountries : undefined)
  }

  const handleTagToggle = (tag: string) => {
    const currentTags = localFilters.tags || []
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag]
    
    handleFilterUpdate('tags', newTags.length > 0 ? newTags : undefined)
  }

  const clearAllFilters = () => {
    setLocalFilters({})
    onFiltersChange({})
  }

  const hasActiveFilters = Object.keys(localFilters).some(key => {
    const value = localFilters[key as keyof TargetCompanyFilters]
    return value !== undefined && value !== null && 
           (Array.isArray(value) ? value.length > 0 : true)
  })

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Clear Filters */}
      {hasActiveFilters && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Active Filters:</span>
            <Badge variant="secondary">
              {Object.keys(localFilters).filter(key => {
                const value = localFilters[key as keyof TargetCompanyFilters]
                return value !== undefined && value !== null && 
                       (Array.isArray(value) ? value.length > 0 : true)
              }).length}
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="flex items-center space-x-1"
          >
            <RotateCcw className="h-3 w-3" />
            <span>Clear All</span>
          </Button>
        </div>
      )}

      {/* Status Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Status</Label>
        <div className="grid grid-cols-2 gap-2">
          {statusOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <Checkbox
                id={`status-${option.value}`}
                checked={localFilters.status?.includes(option.value) || false}
                onCheckedChange={() => handleStatusToggle(option.value)}
              />
              <Label 
                htmlFor={`status-${option.value}`} 
                className="text-sm cursor-pointer"
              >
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Industry Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Industry</Label>
        <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
          {industryOptions.map((industry) => (
            <div key={industry} className="flex items-center space-x-2">
              <Checkbox
                id={`industry-${industry}`}
                checked={localFilters.industry?.includes(industry) || false}
                onCheckedChange={() => handleIndustryToggle(industry)}
              />
              <Label 
                htmlFor={`industry-${industry}`} 
                className="text-sm cursor-pointer"
              >
                {industry}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Country Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Country</Label>
        <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
          {countryOptions.map((country) => (
            <div key={country} className="flex items-center space-x-2">
              <Checkbox
                id={`country-${country}`}
                checked={localFilters.country?.includes(country) || false}
                onCheckedChange={() => handleCountryToggle(country)}
              />
              <Label 
                htmlFor={`country-${country}`} 
                className="text-sm cursor-pointer"
              >
                {country}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Financial Filters */}
      <div className="space-y-4">
        <Label className="text-sm font-medium">Financial Criteria</Label>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="min-revenue" className="text-xs">Min Revenue ($M)</Label>
            <Input
              id="min-revenue"
              type="number"
              placeholder="0"
              value={localFilters.minRevenue || ''}
              onChange={(e) => handleFilterUpdate('minRevenue', 
                e.target.value ? Number(e.target.value) * 1000000 : undefined
              )}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="max-revenue" className="text-xs">Max Revenue ($M)</Label>
            <Input
              id="max-revenue"
              type="number"
              placeholder="1000"
              value={localFilters.maxRevenue ? localFilters.maxRevenue / 1000000 : ''}
              onChange={(e) => handleFilterUpdate('maxRevenue', 
                e.target.value ? Number(e.target.value) * 1000000 : undefined
              )}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="min-employees" className="text-xs">Min Employees</Label>
            <Input
              id="min-employees"
              type="number"
              placeholder="0"
              value={localFilters.minEmployees || ''}
              onChange={(e) => handleFilterUpdate('minEmployees', 
                e.target.value ? Number(e.target.value) : undefined
              )}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="max-employees" className="text-xs">Max Employees</Label>
            <Input
              id="max-employees"
              type="number"
              placeholder="10000"
              value={localFilters.maxEmployees || ''}
              onChange={(e) => handleFilterUpdate('maxEmployees', 
                e.target.value ? Number(e.target.value) : undefined
              )}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="min-score" className="text-xs">Min Opportunity Score</Label>
          <Input
            id="min-score"
            type="number"
            min="0"
            max="100"
            placeholder="0"
            value={localFilters.minOpportunityScore || ''}
            onChange={(e) => handleFilterUpdate('minOpportunityScore', 
              e.target.value ? Number(e.target.value) : undefined
            )}
          />
        </div>
      </div>

      <Separator />

      {/* Tags Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Tags</Label>
        <div className="flex flex-wrap gap-2">
          {commonTags.map((tag) => (
            <Badge
              key={tag}
              variant={localFilters.tags?.includes(tag) ? "default" : "outline"}
              className="cursor-pointer hover:bg-primary/10"
              onClick={() => handleTagToggle(tag)}
            >
              {tag}
              {localFilters.tags?.includes(tag) && (
                <X className="h-3 w-3 ml-1" />
              )}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  )
}
