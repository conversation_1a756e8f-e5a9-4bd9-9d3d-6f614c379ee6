import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useCreateInteraction } from '@/hooks/use-target-companies'
import { CreateInteractionRequest, InteractionType } from '@/services/target-company.service'

interface CreateInteractionDialogProps {
  targetId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

const interactionTypes = [
  { value: InteractionType.EMAIL, label: 'Email' },
  { value: InteractionType.PHONE_CALL, label: 'Phone Call' },
  { value: InteractionType.MEETING, label: 'Meeting' },
  { value: InteractionType.CONFERENCE, label: 'Conference' },
  { value: InteractionType.RESEARCH, label: 'Research' },
  { value: InteractionType.NOTE, label: 'Note' },
  { value: InteractionType.FOLLOW_UP, label: 'Follow-up' },
]

export function CreateInteractionDialog({
  targetId,
  open,
  onOpenChange,
  onSuccess
}: CreateInteractionDialogProps) {
  const [formData, setFormData] = useState<CreateInteractionRequest>({
    targetId,
    type: InteractionType.EMAIL,
    subject: '',
    description: '',
    outcome: '',
    scheduledAt: undefined,
    followUpRequired: false,
    followUpDate: undefined
  })

  const createMutation = useCreateInteraction()

  const handleInputChange = (field: keyof CreateInteractionRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.type) {
      return
    }

    try {
      await createMutation.mutateAsync(formData)
      onSuccess?.()
      // Reset form
      setFormData({
        targetId,
        type: InteractionType.EMAIL,
        subject: '',
        description: '',
        outcome: '',
        scheduledAt: undefined,
        followUpRequired: false,
        followUpDate: undefined
      })
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    // Reset form
    setFormData({
      targetId,
      type: InteractionType.EMAIL,
      subject: '',
      description: '',
      outcome: '',
      scheduledAt: undefined,
      followUpRequired: false,
      followUpDate: undefined
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Record Interaction</DialogTitle>
          <DialogDescription>
            Add a new interaction or activity for this target company.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Interaction Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Interaction Type *</Label>
            <Select 
              value={formData.type} 
              onValueChange={(value) => handleInputChange('type', value as InteractionType)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select interaction type" />
              </SelectTrigger>
              <SelectContent>
                {interactionTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              placeholder="Brief subject or title for this interaction"
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Detailed description of the interaction"
              rows={3}
            />
          </div>

          {/* Outcome */}
          <div className="space-y-2">
            <Label htmlFor="outcome">Outcome</Label>
            <Textarea
              id="outcome"
              value={formData.outcome}
              onChange={(e) => handleInputChange('outcome', e.target.value)}
              placeholder="What was the result or outcome of this interaction?"
              rows={2}
            />
          </div>

          {/* Scheduled Date */}
          <div className="space-y-2">
            <Label htmlFor="scheduledAt">Scheduled Date/Time</Label>
            <Input
              id="scheduledAt"
              type="datetime-local"
              value={formData.scheduledAt ? new Date(formData.scheduledAt).toISOString().slice(0, 16) : ''}
              onChange={(e) => handleInputChange('scheduledAt', e.target.value ? new Date(e.target.value).toISOString() : undefined)}
            />
            <p className="text-xs text-muted-foreground">
              Leave empty if this interaction has already occurred
            </p>
          </div>

          {/* Follow-up Required */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="followUpRequired"
                checked={formData.followUpRequired}
                onCheckedChange={(checked) => handleInputChange('followUpRequired', checked)}
              />
              <Label htmlFor="followUpRequired" className="text-sm">
                Follow-up required
              </Label>
            </div>

            {/* Follow-up Date */}
            {formData.followUpRequired && (
              <div className="space-y-2 ml-6">
                <Label htmlFor="followUpDate">Follow-up Date</Label>
                <Input
                  id="followUpDate"
                  type="datetime-local"
                  value={formData.followUpDate ? new Date(formData.followUpDate).toISOString().slice(0, 16) : ''}
                  onChange={(e) => handleInputChange('followUpDate', e.target.value ? new Date(e.target.value).toISOString() : undefined)}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!formData.type || createMutation.isPending}
            >
              {createMutation.isPending ? 'Recording...' : 'Record Interaction'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
