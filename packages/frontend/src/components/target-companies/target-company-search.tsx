import React, { useState, useCallback, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Filter, 
  SlidersHorizontal, 
  Building2, 
  MapPin, 
  Users, 
  DollarSign,
  TrendingUp,
  Star,
  Calendar,
  Globe,
  Phone,
  Mail,
  ExternalLink,
  Plus
} from 'lucide-react'
import { 
  useTargetCompanies, 
  useTargetCompanySearch 
} from '@/hooks/use-target-companies'
import {
  TargetCompany,
  TargetCompanyFilters,
  TargetCompanyStatus
} from '@/services/target-company.service'
import { TargetCompanyCard } from './target-company-card'
import { TargetCompanyFiltersPanel } from './target-company-filters'
import { CreateTargetCompanyDialog } from './create-target-company-dialog'

interface TargetCompanySearchProps {
  onCompanySelect?: (company: TargetCompany) => void
  showCreateButton?: boolean
  className?: string
}

export function TargetCompanySearch({ 
  onCompanySelect, 
  showCreateButton = true,
  className = '' 
}: TargetCompanySearchProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedQuery, setDebouncedQuery] = useState('')
  const [filters, setFilters] = useState<TargetCompanyFilters>({})
  const [sortField, setSortField] = useState('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [showFilters, setShowFilters] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const pageSize = 20

  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery)
      setCurrentPage(1) // Reset to first page on search
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Use search query if available, otherwise use regular list
  const useSearchResults = debouncedQuery.length >= 2
  
  const {
    data: searchResults,
    isLoading: isSearchLoading,
    error: searchError
  } = useTargetCompanySearch(
    debouncedQuery,
    filters,
    50 // Higher limit for search results
  )

  const {
    data: listResults,
    isLoading: isListLoading,
    error: listError
  } = useTargetCompanies(
    { ...filters, search: debouncedQuery.length < 2 ? undefined : debouncedQuery },
    sortField,
    sortDirection,
    currentPage,
    pageSize
  )

  const companies = useSearchResults ? searchResults : listResults?.companies
  const isLoading = useSearchResults ? isSearchLoading : isListLoading
  const error = useSearchResults ? searchError : listError
  const pagination = useSearchResults ? null : listResults?.pagination

  const handleFilterChange = useCallback((newFilters: TargetCompanyFilters) => {
    setFilters(newFilters)
    setCurrentPage(1)
  }, [])

  const handleSortChange = useCallback((field: string, direction: 'asc' | 'desc') => {
    setSortField(field)
    setSortDirection(direction)
    setCurrentPage(1)
  }, [])

  const handleCompanyClick = useCallback((company: TargetCompany) => {
    onCompanySelect?.(company)
  }, [onCompanySelect])

  const filteredAndSortedCompanies = useMemo(() => {
    if (!companies) return []
    
    // If using search results, apply client-side sorting since API doesn't sort search results
    if (useSearchResults) {
      const sorted = [...companies].sort((a, b) => {
        let aValue: any = a[sortField as keyof TargetCompany]
        let bValue: any = b[sortField as keyof TargetCompany]
        
        // Handle different data types
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase()
          bValue = bValue?.toLowerCase() || ''
        }
        
        if (typeof aValue === 'number') {
          aValue = aValue || 0
          bValue = bValue || 0
        }
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
        return 0
      })
      
      return sorted
    }
    
    return companies
  }, [companies, sortField, sortDirection, useSearchResults])

  const totalResults = useSearchResults 
    ? searchResults?.length || 0 
    : pagination?.total || 0

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Header */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Target Companies</h2>
            <p className="text-muted-foreground">
              Discover and manage potential acquisition targets
            </p>
          </div>
          {showCreateButton && (
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Target Company
            </Button>
          )}
        </div>

        {/* Search and Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search companies by name, industry, location..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <SlidersHorizontal className="h-4 w-4" />
              Filters
              {Object.keys(filters).length > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {Object.keys(filters).length}
                </Badge>
              )}
            </Button>

            <Select value={sortField} onValueChange={(value) => handleSortChange(value, sortDirection)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="opportunityScore">Score</SelectItem>
                <SelectItem value="revenue">Revenue</SelectItem>
                <SelectItem value="employees">Employees</SelectItem>
                <SelectItem value="createdAt">Date Added</SelectItem>
                <SelectItem value="lastContactDate">Last Contact</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSortChange(sortField, sortDirection === 'asc' ? 'desc' : 'asc')}
            >
              {sortDirection === 'asc' ? '↑' : '↓'}
            </Button>
          </div>
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {isLoading ? (
              'Loading...'
            ) : (
              `${totalResults} ${totalResults === 1 ? 'company' : 'companies'} found`
            )}
          </div>
          
          {!useSearchResults && pagination && (
            <div>
              Page {pagination.page} of {pagination.pages}
            </div>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <TargetCompanyFiltersPanel
              filters={filters}
              onFiltersChange={handleFilterChange}
            />
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <div className="space-y-4">
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}

        {error && (
          <Card>
            <CardContent className="py-6">
              <div className="text-center text-muted-foreground">
                <p>Error loading target companies</p>
                <p className="text-sm mt-1">{error.message}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {!isLoading && !error && filteredAndSortedCompanies.length === 0 && (
          <Card>
            <CardContent className="py-12">
              <div className="text-center text-muted-foreground">
                <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">No target companies found</p>
                <p className="text-sm mt-1">
                  {searchQuery || Object.keys(filters).length > 0
                    ? 'Try adjusting your search or filters'
                    : 'Get started by adding your first target company'
                  }
                </p>
                {showCreateButton && (
                  <Button 
                    className="mt-4" 
                    onClick={() => setShowCreateDialog(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Target Company
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {!isLoading && !error && filteredAndSortedCompanies.length > 0 && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAndSortedCompanies.map((company) => (
                <TargetCompanyCard
                  key={company.id}
                  company={company}
                  onClick={() => handleCompanyClick(company)}
                />
              ))}
            </div>

            {/* Pagination for list view */}
            {!useSearchResults && pagination && pagination.pages > 1 && (
              <div className="flex items-center justify-center space-x-2 pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                  disabled={currentPage === pagination.pages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Create Target Company Dialog */}
      <CreateTargetCompanyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  )
}
