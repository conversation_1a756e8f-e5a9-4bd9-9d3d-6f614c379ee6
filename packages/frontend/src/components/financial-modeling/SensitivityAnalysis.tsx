import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3, 
  TrendingUp, 
  Activity, 
  Target,
  Play,
  Settings
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  Cell
} from 'recharts'

interface SensitivityVariable {
  name: string
  label: string
  baseValue: number
  changes: number[]
  unit: 'percentage' | 'currency' | 'number'
}

interface SensitivityResult {
  variable: string
  changes: Array<{
    changePercent: number
    newValue: number
    result: number
    impact: number
  }>
}

interface TornadoChartData {
  variable: string
  lowImpact: number
  highImpact: number
  range: number
}

interface ScenarioResult {
  name: string
  variables: Record<string, number>
  result: number
  variance: number
}

export function SensitivityAnalysis() {
  const [baseCase, setBaseCase] = useState({
    baseRevenue: 1000000000,
    revenueGrowth: 0.15,
    ebitdaMargin: 0.25,
    discountRate: 0.10,
    terminalGrowthRate: 0.025
  })

  const [variables, setVariables] = useState<SensitivityVariable[]>([
    {
      name: 'discountRate',
      label: 'Discount Rate (WACC)',
      baseValue: 0.10,
      changes: [-20, -10, -5, 0, 5, 10, 20],
      unit: 'percentage'
    },
    {
      name: 'terminalGrowthRate',
      label: 'Terminal Growth Rate',
      baseValue: 0.025,
      changes: [-20, -10, -5, 0, 5, 10, 20],
      unit: 'percentage'
    },
    {
      name: 'revenueGrowth',
      label: 'Revenue Growth Rate',
      baseValue: 0.15,
      changes: [-20, -10, -5, 0, 5, 10, 20],
      unit: 'percentage'
    },
    {
      name: 'ebitdaMargin',
      label: 'EBITDA Margin',
      baseValue: 0.25,
      changes: [-20, -10, -5, 0, 5, 10, 20],
      unit: 'percentage'
    }
  ])

  const [sensitivityResults, setSensitivityResults] = useState<SensitivityResult[]>([])
  const [tornadoData, setTornadoData] = useState<TornadoChartData[]>([])
  const [scenarios, setScenarios] = useState<ScenarioResult[]>([])
  const [selectedMetric, setSelectedMetric] = useState('enterpriseValue')
  const [isCalculating, setIsCalculating] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    performSensitivityAnalysis()
  }, [variables, baseCase])

  const performSensitivityAnalysis = async () => {
    try {
      setIsCalculating(true)

      // Mock sensitivity analysis calculation
      const baseCaseValue = 1200000000 // Mock enterprise value

      const results: SensitivityResult[] = variables.map(variable => {
        const changes = variable.changes.map(changePercent => {
          const newValue = variable.baseValue * (1 + changePercent / 100)
          
          // Mock calculation - in reality, this would call the DCF calculation with new value
          let impactFactor = 1
          if (variable.name === 'discountRate') {
            impactFactor = 1 - (changePercent / 100) * 2 // Inverse relationship
          } else if (variable.name === 'terminalGrowthRate') {
            impactFactor = 1 + (changePercent / 100) * 1.5
          } else {
            impactFactor = 1 + (changePercent / 100) * 0.8
          }
          
          const result = baseCaseValue * impactFactor
          const impact = ((result - baseCaseValue) / baseCaseValue) * 100

          return {
            changePercent,
            newValue,
            result,
            impact
          }
        })

        return {
          variable: variable.name,
          changes
        }
      })

      setSensitivityResults(results)

      // Calculate tornado chart data
      const tornado: TornadoChartData[] = variables.map(variable => {
        const variableResults = results.find(r => r.variable === variable.name)
        if (!variableResults) return { variable: variable.label, lowImpact: 0, highImpact: 0, range: 0 }

        const lowImpact = variableResults.changes.find(c => c.changePercent === -10)?.impact || 0
        const highImpact = variableResults.changes.find(c => c.changePercent === 10)?.impact || 0
        const range = Math.abs(highImpact - lowImpact)

        return {
          variable: variable.label,
          lowImpact,
          highImpact,
          range
        }
      }).sort((a, b) => b.range - a.range)

      setTornadoData(tornado)

      // Generate scenarios
      const scenarioResults: ScenarioResult[] = [
        {
          name: 'Bear Case',
          variables: {
            discountRate: baseCase.discountRate * 1.2,
            terminalGrowthRate: baseCase.terminalGrowthRate * 0.8,
            revenueGrowth: baseCase.revenueGrowth * 0.8,
            ebitdaMargin: baseCase.ebitdaMargin * 0.9
          },
          result: baseCaseValue * 0.7,
          variance: -30
        },
        {
          name: 'Base Case',
          variables: baseCase,
          result: baseCaseValue,
          variance: 0
        },
        {
          name: 'Bull Case',
          variables: {
            discountRate: baseCase.discountRate * 0.9,
            terminalGrowthRate: baseCase.terminalGrowthRate * 1.2,
            revenueGrowth: baseCase.revenueGrowth * 1.2,
            ebitdaMargin: baseCase.ebitdaMargin * 1.1
          },
          result: baseCaseValue * 1.4,
          variance: 40
        }
      ]

      setScenarios(scenarioResults)

    } catch (error) {
      toast({
        title: 'Calculation Error',
        description: 'Failed to perform sensitivity analysis',
        variant: 'destructive'
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const handleVariableChange = (index: number, field: keyof SensitivityVariable, value: any) => {
    setVariables(prev => 
      prev.map((variable, i) => 
        i === index ? { ...variable, [field]: value } : variable
      )
    )
  }

  const addVariable = () => {
    const newVariable: SensitivityVariable = {
      name: 'newVariable',
      label: 'New Variable',
      baseValue: 1,
      changes: [-20, -10, -5, 0, 5, 10, 20],
      unit: 'percentage'
    }
    setVariables(prev => [...prev, newVariable])
  }

  const removeVariable = (index: number) => {
    setVariables(prev => prev.filter((_, i) => i !== index))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getVarianceColor = (variance: number) => {
    if (variance > 10) return 'text-green-600'
    if (variance < -10) return 'text-red-600'
    return 'text-gray-600'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Activity className="h-6 w-6" />
            Sensitivity Analysis
          </h2>
          <p className="text-muted-foreground">
            Analyze how changes in key variables impact valuation outcomes
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedMetric} onValueChange={setSelectedMetric}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="enterpriseValue">Enterprise Value</SelectItem>
              <SelectItem value="equityValue">Equity Value</SelectItem>
              <SelectItem value="sharePrice">Share Price</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={performSensitivityAnalysis} disabled={isCalculating}>
            <Play className="h-4 w-4 mr-2" />
            Run Analysis
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Variable Configuration */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Variables
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {variables.map((variable, index) => (
                <div key={index} className="p-3 border rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">{variable.label}</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeVariable(index)}
                    >
                      ×
                    </Button>
                  </div>
                  <Input
                    type="number"
                    step="0.001"
                    value={variable.unit === 'percentage' ? (variable.baseValue * 100).toFixed(1) : variable.baseValue}
                    onChange={(e) => handleVariableChange(
                      index, 
                      'baseValue', 
                      variable.unit === 'percentage' ? Number(e.target.value) / 100 : Number(e.target.value)
                    )}
                  />
                  <div className="text-xs text-muted-foreground">
                    Range: ±20% in 5% increments
                  </div>
                </div>
              ))}
              <Button variant="outline" onClick={addVariable} className="w-full">
                Add Variable
              </Button>
            </CardContent>
          </Card>

          {/* Scenario Summary */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Scenario Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {scenarios.map((scenario, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-medium">{scenario.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(scenario.result)}
                      </div>
                    </div>
                    <Badge 
                      variant={scenario.variance > 0 ? 'default' : scenario.variance < 0 ? 'destructive' : 'secondary'}
                    >
                      {scenario.variance > 0 ? '+' : ''}{formatPercentage(scenario.variance)}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analysis Results */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tornado Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Tornado Chart - Variable Impact
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={tornadoData}
                  layout="horizontal"
                  margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="variable" type="category" width={100} />
                  <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Impact']} />
                  <Bar dataKey="lowImpact" fill="#ef4444" name="Downside" />
                  <Bar dataKey="highImpact" fill="#10b981" name="Upside" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Sensitivity Table */}
          <Card>
            <CardHeader>
              <CardTitle>Sensitivity Table</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Variable</TableHead>
                    <TableHead>-20%</TableHead>
                    <TableHead>-10%</TableHead>
                    <TableHead>-5%</TableHead>
                    <TableHead>Base</TableHead>
                    <TableHead>+5%</TableHead>
                    <TableHead>+10%</TableHead>
                    <TableHead>+20%</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sensitivityResults.map((result) => {
                    const variable = variables.find(v => v.name === result.variable)
                    if (!variable) return null

                    return (
                      <TableRow key={result.variable}>
                        <TableCell className="font-medium">{variable.label}</TableCell>
                        {[-20, -10, -5, 0, 5, 10, 20].map(changePercent => {
                          const change = result.changes.find(c => c.changePercent === changePercent)
                          const isBase = changePercent === 0
                          return (
                            <TableCell 
                              key={changePercent}
                              className={`${isBase ? 'font-semibold bg-muted' : ''} ${getVarianceColor(change?.impact || 0)}`}
                            >
                              {change ? formatCurrency(change.result) : '-'}
                              {change && !isBase && (
                                <div className="text-xs">
                                  {change.impact > 0 ? '+' : ''}{formatPercentage(change.impact)}
                                </div>
                              )}
                            </TableCell>
                          )
                        })}
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Scenario Comparison Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Scenario Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={250}>
                <BarChart data={scenarios}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatCurrency(value as number), 'Valuation']} />
                  <Bar dataKey="result" name="Valuation">
                    {scenarios.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={entry.variance > 0 ? '#10b981' : entry.variance < 0 ? '#ef4444' : '#3b82f6'} 
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
