import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Settings, 
  Plus, 
  Play, 
  BarChart3,
  TrendingUp,
  Dice6,
  Save,
  Trash2
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'recharts'

interface ScenarioVariant {
  id: string
  name: string
  description?: string
  probability?: number
  assumptions: Record<string, number>
  results?: Record<string, number>
  calculatedAt?: Date
}

interface ScenarioDefinition {
  id: string
  name: string
  description?: string
  templateId: string
  baseAssumptions: Record<string, number>
  scenarios: ScenarioVariant[]
  createdAt: Date
  updatedAt: Date
}

interface MonteCarloConfig {
  iterations: number
  confidenceIntervals: number[]
  variables: Array<{
    key: string
    distribution: 'normal' | 'uniform' | 'triangular'
    parameters: Record<string, number>
  }>
}

interface MonteCarloResult {
  iterations: number
  statistics: Record<string, {
    mean: number
    median: number
    standardDeviation: number
    confidenceIntervals: Array<{
      level: number
      lower: number
      upper: number
    }>
    percentiles: Record<string, number>
  }>
  riskMetrics: {
    valueAtRisk: Record<string, number>
    probabilityOfLoss: number
  }
}

export function ScenarioManagement() {
  const [scenarioDefinitions, setScenarioDefinitions] = useState<ScenarioDefinition[]>([])
  const [selectedDefinition, setSelectedDefinition] = useState<ScenarioDefinition | null>(null)
  const [newScenario, setNewScenario] = useState<Partial<ScenarioVariant>>({
    name: '',
    description: '',
    probability: 0.33,
    assumptions: {}
  })
  const [monteCarloConfig, setMonteCarloConfig] = useState<MonteCarloConfig>({
    iterations: 1000,
    confidenceIntervals: [90, 95, 99],
    variables: [
      {
        key: 'revenueGrowth',
        distribution: 'normal',
        parameters: { mean: 0.15, stdDev: 0.05 }
      },
      {
        key: 'discountRate',
        distribution: 'normal',
        parameters: { mean: 0.10, stdDev: 0.02 }
      }
    ]
  })
  const [monteCarloResults, setMonteCarloResults] = useState<MonteCarloResult | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)
  const [showNewScenarioDialog, setShowNewScenarioDialog] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadScenarioDefinitions()
  }, [])

  const loadScenarioDefinitions = async () => {
    try {
      // Mock data - replace with actual API call
      const mockDefinitions: ScenarioDefinition[] = [
        {
          id: '1',
          name: 'TechCorp Acquisition Scenarios',
          description: 'Multiple scenarios for TechCorp acquisition valuation',
          templateId: 'dcf-template-1',
          baseAssumptions: {
            baseRevenue: 1000000000,
            ebitdaMargin: 0.25,
            taxRate: 0.25,
            discountRate: 0.10,
            terminalGrowthRate: 0.025
          },
          scenarios: [
            {
              id: 's1',
              name: 'Conservative',
              description: 'Conservative growth assumptions',
              probability: 0.3,
              assumptions: {
                revenueGrowth: 0.08,
                ebitdaMargin: 0.22
              },
              results: {
                enterpriseValue: *********,
                equityValue: *********
              },
              calculatedAt: new Date()
            },
            {
              id: 's2',
              name: 'Base Case',
              description: 'Most likely scenario',
              probability: 0.4,
              assumptions: {
                revenueGrowth: 0.15,
                ebitdaMargin: 0.25
              },
              results: {
                enterpriseValue: 1200000000,
                equityValue: 1150000000
              },
              calculatedAt: new Date()
            },
            {
              id: 's3',
              name: 'Optimistic',
              description: 'High growth scenario',
              probability: 0.3,
              assumptions: {
                revenueGrowth: 0.22,
                ebitdaMargin: 0.28
              },
              results: {
                enterpriseValue: 1550000000,
                equityValue: 1500000000
              },
              calculatedAt: new Date()
            }
          ],
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-15')
        }
      ]
      setScenarioDefinitions(mockDefinitions)
      if (mockDefinitions.length > 0) {
        setSelectedDefinition(mockDefinitions[0])
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load scenario definitions',
        variant: 'destructive'
      })
    }
  }

  const createNewScenario = async () => {
    if (!selectedDefinition || !newScenario.name) return

    try {
      const scenario: ScenarioVariant = {
        id: Date.now().toString(),
        name: newScenario.name,
        description: newScenario.description,
        probability: newScenario.probability || 0.33,
        assumptions: newScenario.assumptions || {},
        calculatedAt: new Date()
      }

      const updatedDefinition = {
        ...selectedDefinition,
        scenarios: [...selectedDefinition.scenarios, scenario]
      }

      setSelectedDefinition(updatedDefinition)
      setScenarioDefinitions(prev => 
        prev.map(def => def.id === selectedDefinition.id ? updatedDefinition : def)
      )

      setNewScenario({ name: '', description: '', probability: 0.33, assumptions: {} })
      setShowNewScenarioDialog(false)

      toast({
        title: 'Scenario Created',
        description: 'New scenario has been added successfully'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create scenario',
        variant: 'destructive'
      })
    }
  }

  const runMonteCarloSimulation = async () => {
    try {
      setIsCalculating(true)

      // Mock Monte Carlo simulation
      const results: MonteCarloResult = {
        iterations: monteCarloConfig.iterations,
        statistics: {
          enterpriseValue: {
            mean: 1200000000,
            median: 1180000000,
            standardDeviation: 180000000,
            confidenceIntervals: [
              { level: 90, lower: *********, upper: 1450000000 },
              { level: 95, lower: *********, upper: 1500000000 },
              { level: 99, lower: 800000000, upper: 1600000000 }
            ],
            percentiles: {
              p5: 920000000,
              p10: 980000000,
              p25: 1080000000,
              p75: 1320000000,
              p90: 1420000000,
              p95: 1480000000
            }
          }
        },
        riskMetrics: {
          valueAtRisk: {
            '95%': *********,
            '99%': 800000000
          },
          probabilityOfLoss: 0.05
        }
      }

      setMonteCarloResults(results)

      toast({
        title: 'Simulation Complete',
        description: `Monte Carlo simulation with ${monteCarloConfig.iterations} iterations completed`
      })
    } catch (error) {
      toast({
        title: 'Simulation Error',
        description: 'Failed to run Monte Carlo simulation',
        variant: 'destructive'
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const calculateScenarioResults = async () => {
    if (!selectedDefinition) return

    try {
      setIsCalculating(true)
      // Mock calculation - replace with actual API call
      toast({
        title: 'Calculating',
        description: 'Recalculating scenario results...'
      })
    } catch (error) {
      toast({
        title: 'Calculation Error',
        description: 'Failed to calculate scenario results',
        variant: 'destructive'
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Scenario Management
          </h2>
          <p className="text-muted-foreground">
            Create and manage multiple valuation scenarios with Monte Carlo simulation
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={calculateScenarioResults} disabled={isCalculating}>
            <Play className="h-4 w-4 mr-2" />
            Calculate
          </Button>
          <Dialog open={showNewScenarioDialog} onOpenChange={setShowNewScenarioDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Scenario
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Scenario</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="scenarioName">Scenario Name</Label>
                  <Input
                    id="scenarioName"
                    value={newScenario.name}
                    onChange={(e) => setNewScenario(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="scenarioDescription">Description</Label>
                  <Textarea
                    id="scenarioDescription"
                    value={newScenario.description}
                    onChange={(e) => setNewScenario(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="scenarioProbability">Probability</Label>
                  <Input
                    id="scenarioProbability"
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={newScenario.probability}
                    onChange={(e) => setNewScenario(prev => ({ ...prev, probability: Number(e.target.value) }))}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowNewScenarioDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createNewScenario}>
                    Create Scenario
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="scenarios" className="space-y-6">
        <TabsList>
          <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
          <TabsTrigger value="monte-carlo">Monte Carlo</TabsTrigger>
          <TabsTrigger value="comparison">Comparison</TabsTrigger>
        </TabsList>

        <TabsContent value="scenarios" className="space-y-6">
          {selectedDefinition && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Scenario List */}
              <Card>
                <CardHeader>
                  <CardTitle>Scenarios</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {selectedDefinition.scenarios.map((scenario) => (
                      <Card key={scenario.id} className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="font-semibold">{scenario.name}</h4>
                            {scenario.description && (
                              <p className="text-sm text-muted-foreground">{scenario.description}</p>
                            )}
                          </div>
                          <Badge variant="outline">
                            {formatPercentage(scenario.probability || 0)}
                          </Badge>
                        </div>
                        {scenario.results && (
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">Enterprise Value:</span>
                              <div className="font-medium">{formatCurrency(scenario.results.enterpriseValue)}</div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Equity Value:</span>
                              <div className="font-medium">{formatCurrency(scenario.results.equityValue)}</div>
                            </div>
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Base Assumptions */}
              <Card>
                <CardHeader>
                  <CardTitle>Base Assumptions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(selectedDefinition.baseAssumptions).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <Label className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={typeof value === 'number' && key.includes('Rate') || key.includes('Margin') ? (value * 100).toFixed(1) : value}
                          className="w-32"
                          readOnly
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="monte-carlo" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Monte Carlo Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Dice6 className="h-5 w-5" />
                  Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="iterations">Iterations</Label>
                  <Input
                    id="iterations"
                    type="number"
                    value={monteCarloConfig.iterations}
                    onChange={(e) => setMonteCarloConfig(prev => ({ 
                      ...prev, 
                      iterations: Number(e.target.value) 
                    }))}
                  />
                </div>
                <Button 
                  onClick={runMonteCarloSimulation} 
                  disabled={isCalculating}
                  className="w-full"
                >
                  {isCalculating ? 'Running...' : 'Run Simulation'}
                </Button>
              </CardContent>
            </Card>

            {/* Results */}
            <div className="lg:col-span-2">
              {monteCarloResults && (
                <Card>
                  <CardHeader>
                    <CardTitle>Simulation Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Mean</div>
                        <div className="text-lg font-semibold">
                          {formatCurrency(monteCarloResults.statistics.enterpriseValue.mean)}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Median</div>
                        <div className="text-lg font-semibold">
                          {formatCurrency(monteCarloResults.statistics.enterpriseValue.median)}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Std Dev</div>
                        <div className="text-lg font-semibold">
                          {formatCurrency(monteCarloResults.statistics.enterpriseValue.standardDeviation)}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">VaR (95%)</div>
                        <div className="text-lg font-semibold">
                          {formatCurrency(monteCarloResults.riskMetrics.valueAtRisk['95%'])}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold">Confidence Intervals</h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Confidence Level</TableHead>
                            <TableHead>Lower Bound</TableHead>
                            <TableHead>Upper Bound</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {monteCarloResults.statistics.enterpriseValue.confidenceIntervals.map((ci) => (
                            <TableRow key={ci.level}>
                              <TableCell>{ci.level}%</TableCell>
                              <TableCell>{formatCurrency(ci.lower)}</TableCell>
                              <TableCell>{formatCurrency(ci.upper)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-6">
          {selectedDefinition && (
            <Card>
              <CardHeader>
                <CardTitle>Scenario Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <ScatterChart data={selectedDefinition.scenarios.map(scenario => ({
                    name: scenario.name,
                    probability: (scenario.probability || 0) * 100,
                    value: scenario.results?.enterpriseValue || 0,
                    size: 100
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="probability" 
                      name="Probability" 
                      unit="%" 
                      domain={[0, 100]}
                    />
                    <YAxis 
                      dataKey="value" 
                      name="Enterprise Value" 
                      tickFormatter={(value) => `$${(value / 1000000).toFixed(0)}M`}
                    />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'value' ? formatCurrency(value as number) : `${value}%`,
                        name === 'value' ? 'Enterprise Value' : 'Probability'
                      ]}
                    />
                    <Scatter dataKey="value" fill="#3b82f6" />
                  </ScatterChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
