import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Calculator, 
  TrendingUp, 
  DollarSign, 
  Percent,
  Play,
  Save,
  RefreshCw
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'

interface DCFInputs {
  baseRevenue: number
  revenueGrowth: number[]
  ebitdaMargin: number
  taxRate: number
  discountRate: number
  terminalGrowthRate: number
  capexAsPercentOfRevenue: number
  workingCapitalChange: number
}

interface DCFResults {
  projectedRevenues: number[]
  projectedEbitda: number[]
  projectedFCF: number[]
  terminalValue: number
  enterpriseValue: number
  equityValue: number
  impliedSharePrice: number
  npvBreakdown: {
    year: number
    fcf: number
    pv: number
    cumulative: number
  }[]
}

export function DCFModelBuilder() {
  const [inputs, setInputs] = useState<DCFInputs>({
    baseRevenue: 1000000000,
    revenueGrowth: [0.15, 0.12, 0.10, 0.08, 0.06],
    ebitdaMargin: 0.25,
    taxRate: 0.25,
    discountRate: 0.10,
    terminalGrowthRate: 0.025,
    capexAsPercentOfRevenue: 0.05,
    workingCapitalChange: 0.02
  })
  
  const [results, setResults] = useState<DCFResults | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    calculateDCF()
  }, [inputs])

  const calculateDCF = async () => {
    try {
      setIsCalculating(true)
      
      // Mock calculation - replace with actual API call
      const projectedRevenues = inputs.revenueGrowth.map((growth, index) => {
        return inputs.baseRevenue * inputs.revenueGrowth.slice(0, index + 1).reduce((acc, g) => acc * (1 + g), 1)
      })

      const projectedEbitda = projectedRevenues.map(revenue => revenue * inputs.ebitdaMargin)
      
      const projectedFCF = projectedEbitda.map((ebitda, index) => {
        const revenue = projectedRevenues[index]
        const capex = revenue * inputs.capexAsPercentOfRevenue
        const wcChange = revenue * inputs.workingCapitalChange
        return ebitda * (1 - inputs.taxRate) - capex - wcChange
      })

      const terminalFCF = projectedFCF[projectedFCF.length - 1] * (1 + inputs.terminalGrowthRate)
      const terminalValue = terminalFCF / (inputs.discountRate - inputs.terminalGrowthRate)
      
      const npvBreakdown = projectedFCF.map((fcf, index) => {
        const year = index + 1
        const pv = fcf / Math.pow(1 + inputs.discountRate, year)
        return { year, fcf, pv, cumulative: 0 }
      })

      // Calculate cumulative PV
      let cumulative = 0
      npvBreakdown.forEach(item => {
        cumulative += item.pv
        item.cumulative = cumulative
      })

      const terminalValuePV = terminalValue / Math.pow(1 + inputs.discountRate, 5)
      const enterpriseValue = npvBreakdown.reduce((sum, item) => sum + item.pv, 0) + terminalValuePV
      
      const mockResults: DCFResults = {
        projectedRevenues,
        projectedEbitda,
        projectedFCF,
        terminalValue,
        enterpriseValue,
        equityValue: enterpriseValue, // Simplified - would subtract net debt
        impliedSharePrice: enterpriseValue / 100000000, // Mock shares outstanding
        npvBreakdown
      }

      setResults(mockResults)
    } catch (error) {
      toast({
        title: 'Calculation Error',
        description: 'Failed to calculate DCF model',
        variant: 'destructive'
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const handleInputChange = (field: keyof DCFInputs, value: number | number[]) => {
    setInputs(prev => ({ ...prev, [field]: value }))
  }

  const handleRevenueGrowthChange = (index: number, value: number) => {
    const newGrowthRates = [...inputs.revenueGrowth]
    newGrowthRates[index] = value / 100 // Convert percentage to decimal
    handleInputChange('revenueGrowth', newGrowthRates)
  }

  const handleSaveModel = async () => {
    try {
      setIsSaving(true)
      // Save model logic here
      toast({
        title: 'Model Saved',
        description: 'DCF model has been saved successfully'
      })
    } catch (error) {
      toast({
        title: 'Save Error',
        description: 'Failed to save DCF model',
        variant: 'destructive'
      })
    } finally {
      setIsSaving(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Calculator className="h-6 w-6" />
            DCF Model Builder
          </h2>
          <p className="text-muted-foreground">
            Build a discounted cash flow valuation model with customizable assumptions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={calculateDCF} disabled={isCalculating}>
            {isCalculating ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Calculate
          </Button>
          <Button onClick={handleSaveModel} disabled={isSaving}>
            {isSaving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Model
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input Panel */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Revenue Assumptions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="baseRevenue">Base Year Revenue</Label>
                <Input
                  id="baseRevenue"
                  type="number"
                  value={inputs.baseRevenue}
                  onChange={(e) => handleInputChange('baseRevenue', Number(e.target.value))}
                />
              </div>
              
              <div>
                <Label>Revenue Growth Rates (%)</Label>
                <div className="space-y-2 mt-2">
                  {inputs.revenueGrowth.map((growth, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Label className="w-16">Year {index + 1}</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={(growth * 100).toFixed(1)}
                        onChange={(e) => handleRevenueGrowthChange(index, Number(e.target.value))}
                        className="flex-1"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Percent className="h-5 w-5" />
                Profitability & Costs
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="ebitdaMargin">EBITDA Margin (%)</Label>
                <Input
                  id="ebitdaMargin"
                  type="number"
                  step="0.1"
                  value={(inputs.ebitdaMargin * 100).toFixed(1)}
                  onChange={(e) => handleInputChange('ebitdaMargin', Number(e.target.value) / 100)}
                />
              </div>
              
              <div>
                <Label htmlFor="taxRate">Tax Rate (%)</Label>
                <Input
                  id="taxRate"
                  type="number"
                  step="0.1"
                  value={(inputs.taxRate * 100).toFixed(1)}
                  onChange={(e) => handleInputChange('taxRate', Number(e.target.value) / 100)}
                />
              </div>

              <div>
                <Label htmlFor="capexPercent">CapEx (% of Revenue)</Label>
                <Input
                  id="capexPercent"
                  type="number"
                  step="0.1"
                  value={(inputs.capexAsPercentOfRevenue * 100).toFixed(1)}
                  onChange={(e) => handleInputChange('capexAsPercentOfRevenue', Number(e.target.value) / 100)}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Valuation Parameters
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="discountRate">Discount Rate (WACC) (%)</Label>
                <Input
                  id="discountRate"
                  type="number"
                  step="0.1"
                  value={(inputs.discountRate * 100).toFixed(1)}
                  onChange={(e) => handleInputChange('discountRate', Number(e.target.value) / 100)}
                />
              </div>
              
              <div>
                <Label htmlFor="terminalGrowth">Terminal Growth Rate (%)</Label>
                <Input
                  id="terminalGrowth"
                  type="number"
                  step="0.1"
                  value={(inputs.terminalGrowthRate * 100).toFixed(1)}
                  onChange={(e) => handleInputChange('terminalGrowthRate', Number(e.target.value) / 100)}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2 space-y-6">
          {results && (
            <>
              {/* Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Enterprise Value</div>
                    <div className="text-2xl font-bold">{formatCurrency(results.enterpriseValue)}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Equity Value</div>
                    <div className="text-2xl font-bold">{formatCurrency(results.equityValue)}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Terminal Value</div>
                    <div className="text-2xl font-bold">{formatCurrency(results.terminalValue)}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Share Price</div>
                    <div className="text-2xl font-bold">{formatCurrency(results.impliedSharePrice)}</div>
                  </CardContent>
                </Card>
              </div>

              {/* Revenue & FCF Projections Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Revenue & Free Cash Flow Projections</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={results.projectedRevenues.map((revenue, index) => ({
                      year: `Year ${index + 1}`,
                      revenue: revenue / 1000000,
                      fcf: results.projectedFCF[index] / 1000000
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="year" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${value}M`, '']} />
                      <Line type="monotone" dataKey="revenue" stroke="#8884d8" name="Revenue" />
                      <Line type="monotone" dataKey="fcf" stroke="#82ca9d" name="Free Cash Flow" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* NPV Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>NPV Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={results.npvBreakdown.map(item => ({
                      ...item,
                      fcf: item.fcf / 1000000,
                      pv: item.pv / 1000000
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="year" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${value}M`, '']} />
                      <Bar dataKey="fcf" fill="#8884d8" name="Free Cash Flow" />
                      <Bar dataKey="pv" fill="#82ca9d" name="Present Value" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
