import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  BarChart3, 
  TrendingUp, 
  Building2, 
  Search,
  Plus,
  Trash2,
  Calculator
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { <PERSON>atter<PERSON>hart, Scatter, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'

interface ComparableCompany {
  id: string
  name: string
  ticker: string
  marketCap: number
  revenue: number
  ebitda: number
  netIncome: number
  enterpriseValue: number
  evRevenue: number
  evEbitda: number
  peRatio: number
  selected: boolean
}

interface TargetMetrics {
  revenue: number
  ebitda: number
  netIncome: number
}

interface CCAResults {
  multiples: {
    evRevenue: { low: number; median: number; high: number; mean: number }
    evEbitda: { low: number; median: number; high: number; mean: number }
    peRatio: { low: number; median: number; high: number; mean: number }
  }
  impliedValuation: {
    byRevenue: { low: number; mid: number; high: number }
    byEbitda: { low: number; mid: number; high: number }
    byEarnings: { low: number; mid: number; high: number }
  }
  summary: {
    lowValuation: number
    midValuation: number
    highValuation: number
  }
}

export function ComparableCompanyAnalysis() {
  const [targetMetrics, setTargetMetrics] = useState<TargetMetrics>({
    revenue: 1000000000,
    ebitda: *********,
    netIncome: *********
  })
  
  const [comparableCompanies, setComparableCompanies] = useState<ComparableCompany[]>([
    {
      id: '1',
      name: 'TechCorp Inc.',
      ticker: 'TECH',
      marketCap: *********00,
      revenue: 2000000000,
      ebitda: *********,
      netIncome: *********,
      enterpriseValue: 16000000000,
      evRevenue: 8.0,
      evEbitda: 32.0,
      peRatio: 50.0,
      selected: true
    },
    {
      id: '2',
      name: 'InnovateSoft Ltd.',
      ticker: 'INNO',
      marketCap: 8000000000,
      revenue: 1200000000,
      ebitda: *********,
      netIncome: *********,
      enterpriseValue: 8*********,
      evRevenue: 7.1,
      evEbitda: 28.3,
      peRatio: 44.4,
      selected: true
    },
    {
      id: '3',
      name: 'DataSystems Corp.',
      ticker: 'DATA',
      marketCap: 12000000000,
      revenue: *********0,
      ebitda: *********,
      netIncome: *********,
      enterpriseValue: 12800000000,
      evRevenue: 7.1,
      evEbitda: 28.4,
      peRatio: 44.4,
      selected: true
    }
  ])
  
  const [results, setResults] = useState<CCAResults | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    calculateCCA()
  }, [targetMetrics, comparableCompanies])

  const calculateCCA = async () => {
    try {
      setIsCalculating(true)
      
      const selectedCompanies = comparableCompanies.filter(comp => comp.selected)
      
      if (selectedCompanies.length === 0) {
        setResults(null)
        return
      }

      // Calculate multiples statistics
      const evRevenueMultiples = selectedCompanies.map(comp => comp.evRevenue).sort((a, b) => a - b)
      const evEbitdaMultiples = selectedCompanies.map(comp => comp.evEbitda).sort((a, b) => a - b)
      const peRatios = selectedCompanies.map(comp => comp.peRatio).sort((a, b) => a - b)

      const calculateStats = (values: number[]) => ({
        low: values[Math.floor(values.length * 0.25)],
        median: values[Math.floor(values.length * 0.5)],
        high: values[Math.floor(values.length * 0.75)],
        mean: values.reduce((sum, val) => sum + val, 0) / values.length
      })

      const multiples = {
        evRevenue: calculateStats(evRevenueMultiples),
        evEbitda: calculateStats(evEbitdaMultiples),
        peRatio: calculateStats(peRatios)
      }

      // Calculate implied valuations
      const impliedValuation = {
        byRevenue: {
          low: targetMetrics.revenue * multiples.evRevenue.low,
          mid: targetMetrics.revenue * multiples.evRevenue.median,
          high: targetMetrics.revenue * multiples.evRevenue.high
        },
        byEbitda: {
          low: targetMetrics.ebitda * multiples.evEbitda.low,
          mid: targetMetrics.ebitda * multiples.evEbitda.median,
          high: targetMetrics.ebitda * multiples.evEbitda.high
        },
        byEarnings: {
          low: targetMetrics.netIncome * multiples.peRatio.low,
          mid: targetMetrics.netIncome * multiples.peRatio.median,
          high: targetMetrics.netIncome * multiples.peRatio.high
        }
      }

      // Calculate summary
      const allValues = [
        ...Object.values(impliedValuation.byRevenue),
        ...Object.values(impliedValuation.byEbitda),
        ...Object.values(impliedValuation.byEarnings)
      ].sort((a, b) => a - b)

      const summary = {
        lowValuation: Math.min(...allValues),
        midValuation: allValues[Math.floor(allValues.length / 2)],
        highValuation: Math.max(...allValues)
      }

      setResults({ multiples, impliedValuation, summary })
    } catch (error) {
      toast({
        title: 'Calculation Error',
        description: 'Failed to calculate comparable company analysis',
        variant: 'destructive'
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const handleTargetMetricChange = (field: keyof TargetMetrics, value: number) => {
    setTargetMetrics(prev => ({ ...prev, [field]: value }))
  }

  const handleCompanySelection = (companyId: string, selected: boolean) => {
    setComparableCompanies(prev => 
      prev.map(comp => 
        comp.id === companyId ? { ...comp, selected } : comp
      )
    )
  }

  const addComparableCompany = () => {
    // Mock adding a new company
    const newCompany: ComparableCompany = {
      id: Date.now().toString(),
      name: 'New Company',
      ticker: 'NEW',
      marketCap: 10000000000,
      revenue: *********0,
      ebitda: *********,
      netIncome: *********,
      enterpriseValue: 10*********,
      evRevenue: 7.0,
      evEbitda: 28.0,
      peRatio: 46.7,
      selected: true
    }
    setComparableCompanies(prev => [...prev, newCompany])
  }

  const removeComparableCompany = (companyId: string) => {
    setComparableCompanies(prev => prev.filter(comp => comp.id !== companyId))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatMultiple = (value: number) => {
    return `${value.toFixed(1)}x`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            Comparable Company Analysis
          </h2>
          <p className="text-muted-foreground">
            Value the target company using trading multiples from comparable public companies
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={addComparableCompany}>
            <Plus className="h-4 w-4 mr-2" />
            Add Company
          </Button>
          <Button onClick={calculateCCA} disabled={isCalculating}>
            <Calculator className="h-4 w-4 mr-2" />
            Calculate
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Target Company Metrics */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Target Company Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="targetRevenue">Revenue (LTM)</Label>
                <Input
                  id="targetRevenue"
                  type="number"
                  value={targetMetrics.revenue}
                  onChange={(e) => handleTargetMetricChange('revenue', Number(e.target.value))}
                />
              </div>
              
              <div>
                <Label htmlFor="targetEbitda">EBITDA (LTM)</Label>
                <Input
                  id="targetEbitda"
                  type="number"
                  value={targetMetrics.ebitda}
                  onChange={(e) => handleTargetMetricChange('ebitda', Number(e.target.value))}
                />
              </div>

              <div>
                <Label htmlFor="targetNetIncome">Net Income (LTM)</Label>
                <Input
                  id="targetNetIncome"
                  type="number"
                  value={targetMetrics.netIncome}
                  onChange={(e) => handleTargetMetricChange('netIncome', Number(e.target.value))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Valuation Summary */}
          {results && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Valuation Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">Valuation Range</div>
                  <div className="text-2xl font-bold">
                    {formatCurrency(results.summary.lowValuation)} - {formatCurrency(results.summary.highValuation)}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    Mid: {formatCurrency(results.summary.midValuation)}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Comparable Companies & Results */}
        <div className="lg:col-span-2 space-y-6">
          {/* Comparable Companies Table */}
          <Card>
            <CardHeader>
              <CardTitle>Comparable Companies</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12"></TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Market Cap</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>EV/Revenue</TableHead>
                    <TableHead>EV/EBITDA</TableHead>
                    <TableHead>P/E</TableHead>
                    <TableHead className="w-12"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {comparableCompanies.map((company) => (
                    <TableRow key={company.id}>
                      <TableCell>
                        <Checkbox
                          checked={company.selected}
                          onCheckedChange={(checked) => 
                            handleCompanySelection(company.id, checked as boolean)
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{company.name}</div>
                          <div className="text-sm text-muted-foreground">{company.ticker}</div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(company.marketCap)}</TableCell>
                      <TableCell>{formatCurrency(company.revenue)}</TableCell>
                      <TableCell>{formatMultiple(company.evRevenue)}</TableCell>
                      <TableCell>{formatMultiple(company.evEbitda)}</TableCell>
                      <TableCell>{formatMultiple(company.peRatio)}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeComparableCompany(company.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Results */}
          {results && (
            <>
              {/* Multiple Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle>Multiple Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground mb-2">EV/Revenue</div>
                      <div className="space-y-1">
                        <div>Low: {formatMultiple(results.multiples.evRevenue.low)}</div>
                        <div className="font-semibold">Median: {formatMultiple(results.multiples.evRevenue.median)}</div>
                        <div>High: {formatMultiple(results.multiples.evRevenue.high)}</div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground mb-2">EV/EBITDA</div>
                      <div className="space-y-1">
                        <div>Low: {formatMultiple(results.multiples.evEbitda.low)}</div>
                        <div className="font-semibold">Median: {formatMultiple(results.multiples.evEbitda.median)}</div>
                        <div>High: {formatMultiple(results.multiples.evEbitda.high)}</div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground mb-2">P/E Ratio</div>
                      <div className="space-y-1">
                        <div>Low: {formatMultiple(results.multiples.peRatio.low)}</div>
                        <div className="font-semibold">Median: {formatMultiple(results.multiples.peRatio.median)}</div>
                        <div>High: {formatMultiple(results.multiples.peRatio.high)}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Implied Valuation Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Implied Valuation by Method</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={[
                      { method: 'EV/Revenue', low: results.impliedValuation.byRevenue.low / 1000000, mid: results.impliedValuation.byRevenue.mid / 1000000, high: results.impliedValuation.byRevenue.high / 1000000 },
                      { method: 'EV/EBITDA', low: results.impliedValuation.byEbitda.low / 1000000, mid: results.impliedValuation.byEbitda.mid / 1000000, high: results.impliedValuation.byEbitda.high / 1000000 },
                      { method: 'P/E Ratio', low: results.impliedValuation.byEarnings.low / 1000000, mid: results.impliedValuation.byEarnings.mid / 1000000, high: results.impliedValuation.byEarnings.high / 1000000 }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="method" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${value}M`, '']} />
                      <Bar dataKey="low" fill="#ef4444" name="Low" />
                      <Bar dataKey="mid" fill="#3b82f6" name="Mid" />
                      <Bar dataKey="high" fill="#10b981" name="High" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
