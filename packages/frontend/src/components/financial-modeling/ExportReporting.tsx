import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Download, 
  FileText, 
  FileSpreadsheet,
  Image,
  Mail,
  Settings,
  Calendar,
  Users,
  Eye,
  Share2
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ExportOptions {
  format: 'pdf' | 'excel' | 'powerpoint' | 'word'
  sections: string[]
  includeCharts: boolean
  includeComments: boolean
  includeAssumptions: boolean
  template: string
  branding: boolean
}

interface ScheduledReport {
  id: string
  name: string
  frequency: 'daily' | 'weekly' | 'monthly'
  recipients: string[]
  format: string
  lastSent: Date
  nextSend: Date
  status: 'active' | 'paused'
}

export function ExportReporting() {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    sections: ['summary', 'dcf', 'cca', 'sensitivity'],
    includeCharts: true,
    includeComments: false,
    includeAssumptions: true,
    template: 'standard',
    branding: true
  })
  
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([
    {
      id: '1',
      name: 'Weekly Valuation Summary',
      frequency: 'weekly',
      recipients: ['<EMAIL>', '<EMAIL>'],
      format: 'PDF',
      lastSent: new Date('2024-01-15'),
      nextSend: new Date('2024-01-22'),
      status: 'active'
    },
    {
      id: '2',
      name: 'Monthly Executive Report',
      frequency: 'monthly',
      recipients: ['<EMAIL>', '<EMAIL>'],
      format: 'PowerPoint',
      lastSent: new Date('2024-01-01'),
      nextSend: new Date('2024-02-01'),
      status: 'active'
    }
  ])
  
  const [isExporting, setIsExporting] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showScheduleDialog, setShowScheduleDialog] = useState(false)
  const { toast } = useToast()

  const availableSections = [
    { id: 'summary', label: 'Executive Summary', description: 'Key findings and recommendations' },
    { id: 'dcf', label: 'DCF Analysis', description: 'Discounted cash flow model and results' },
    { id: 'cca', label: 'Comparable Company Analysis', description: 'Trading multiples analysis' },
    { id: 'pta', label: 'Precedent Transactions', description: 'Transaction multiples analysis' },
    { id: 'lbo', label: 'LBO Analysis', description: 'Leveraged buyout analysis' },
    { id: 'sensitivity', label: 'Sensitivity Analysis', description: 'Key variable impact analysis' },
    { id: 'scenarios', label: 'Scenario Analysis', description: 'Multiple scenario comparison' },
    { id: 'assumptions', label: 'Key Assumptions', description: 'Model assumptions and rationale' },
    { id: 'risks', label: 'Risk Analysis', description: 'Risk factors and mitigation' },
    { id: 'appendix', label: 'Appendix', description: 'Supporting data and calculations' }
  ]

  const templates = [
    { id: 'standard', label: 'Standard Report', description: 'Professional standard template' },
    { id: 'executive', label: 'Executive Summary', description: 'High-level executive template' },
    { id: 'detailed', label: 'Detailed Analysis', description: 'Comprehensive detailed template' },
    { id: 'presentation', label: 'Presentation', description: 'Slide-based presentation template' }
  ]

  const handleExport = async () => {
    try {
      setIsExporting(true)
      
      // Mock export process
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      toast({
        title: 'Export Complete',
        description: `Report exported as ${exportOptions.format.toUpperCase()} successfully`
      })
      
      setShowExportDialog(false)
    } catch (error) {
      toast({
        title: 'Export Error',
        description: 'Failed to export report',
        variant: 'destructive'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleQuickExport = async (format: ExportOptions['format']) => {
    try {
      setIsExporting(true)
      
      // Mock quick export
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast({
        title: 'Export Complete',
        description: `Quick export as ${format.toUpperCase()} completed`
      })
    } catch (error) {
      toast({
        title: 'Export Error',
        description: 'Failed to export report',
        variant: 'destructive'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleSectionToggle = (sectionId: string, checked: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      sections: checked 
        ? [...prev.sections, sectionId]
        : prev.sections.filter(id => id !== sectionId)
    }))
  }

  const handleScheduleReport = () => {
    // Mock scheduling
    toast({
      title: 'Report Scheduled',
      description: 'Automated report has been scheduled successfully'
    })
    setShowScheduleDialog(false)
  }

  const toggleReportStatus = (reportId: string) => {
    setScheduledReports(prev => 
      prev.map(report => 
        report.id === reportId 
          ? { ...report, status: report.status === 'active' ? 'paused' : 'active' }
          : report
      )
    )
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export & Reporting
          </h3>
          <p className="text-sm text-muted-foreground">
            Generate and schedule professional reports
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Report
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Schedule Automated Report</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Report Name</Label>
                  <Input placeholder="Enter report name..." />
                </div>
                <div>
                  <Label>Frequency</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Recipients</Label>
                  <Input placeholder="Enter email addresses..." />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowScheduleDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleScheduleReport}>
                    Schedule Report
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
            <DialogTrigger asChild>
              <Button>
                <Download className="h-4 w-4 mr-2" />
                Custom Export
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Export Configuration</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                {/* Format Selection */}
                <div>
                  <Label className="text-base font-medium">Export Format</Label>
                  <div className="grid grid-cols-4 gap-2 mt-2">
                    {[
                      { format: 'pdf' as const, icon: FileText, label: 'PDF' },
                      { format: 'excel' as const, icon: FileSpreadsheet, label: 'Excel' },
                      { format: 'powerpoint' as const, icon: Image, label: 'PowerPoint' },
                      { format: 'word' as const, icon: FileText, label: 'Word' }
                    ].map(({ format, icon: Icon, label }) => (
                      <button
                        key={format}
                        onClick={() => setExportOptions(prev => ({ ...prev, format }))}
                        className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                          exportOptions.format === format 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="h-6 w-6" />
                        <span className="text-sm">{label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Sections */}
                <div>
                  <Label className="text-base font-medium">Include Sections</Label>
                  <div className="grid grid-cols-2 gap-3 mt-2">
                    {availableSections.map((section) => (
                      <div key={section.id} className="flex items-start space-x-2">
                        <Checkbox
                          id={section.id}
                          checked={exportOptions.sections.includes(section.id)}
                          onCheckedChange={(checked) => 
                            handleSectionToggle(section.id, checked as boolean)
                          }
                        />
                        <div className="grid gap-1.5 leading-none">
                          <label
                            htmlFor={section.id}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {section.label}
                          </label>
                          <p className="text-xs text-muted-foreground">
                            {section.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Options */}
                <div>
                  <Label className="text-base font-medium">Additional Options</Label>
                  <div className="space-y-3 mt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="charts"
                        checked={exportOptions.includeCharts}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, includeCharts: checked as boolean }))
                        }
                      />
                      <label htmlFor="charts" className="text-sm">Include charts and visualizations</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="comments"
                        checked={exportOptions.includeComments}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, includeComments: checked as boolean }))
                        }
                      />
                      <label htmlFor="comments" className="text-sm">Include comments and reviews</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="assumptions"
                        checked={exportOptions.includeAssumptions}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, includeAssumptions: checked as boolean }))
                        }
                      />
                      <label htmlFor="assumptions" className="text-sm">Include detailed assumptions</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="branding"
                        checked={exportOptions.branding}
                        onCheckedChange={(checked) => 
                          setExportOptions(prev => ({ ...prev, branding: checked as boolean }))
                        }
                      />
                      <label htmlFor="branding" className="text-sm">Include company branding</label>
                    </div>
                  </div>
                </div>

                {/* Template */}
                <div>
                  <Label className="text-base font-medium">Template</Label>
                  <Select 
                    value={exportOptions.template} 
                    onValueChange={(value) => 
                      setExportOptions(prev => ({ ...prev, template: value }))
                    }
                  >
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          <div>
                            <div className="font-medium">{template.label}</div>
                            <div className="text-xs text-muted-foreground">{template.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleExport} disabled={isExporting}>
                    {isExporting ? 'Exporting...' : 'Export Report'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="quick" className="space-y-6">
        <TabsList>
          <TabsTrigger value="quick">Quick Export</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Reports</TabsTrigger>
          <TabsTrigger value="history">Export History</TabsTrigger>
        </TabsList>

        <TabsContent value="quick" className="space-y-6">
          {/* Quick Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-shadow" 
                  onClick={() => handleQuickExport('pdf')}>
              <CardContent className="p-6 text-center">
                <FileText className="h-8 w-8 mx-auto mb-3 text-red-500" />
                <h3 className="font-semibold mb-2">PDF Report</h3>
                <p className="text-sm text-muted-foreground">
                  Professional PDF with all sections
                </p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" 
                  onClick={() => handleQuickExport('excel')}>
              <CardContent className="p-6 text-center">
                <FileSpreadsheet className="h-8 w-8 mx-auto mb-3 text-green-500" />
                <h3 className="font-semibold mb-2">Excel Workbook</h3>
                <p className="text-sm text-muted-foreground">
                  Detailed data and calculations
                </p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" 
                  onClick={() => handleQuickExport('powerpoint')}>
              <CardContent className="p-6 text-center">
                <Image className="h-8 w-8 mx-auto mb-3 text-orange-500" />
                <h3 className="font-semibold mb-2">PowerPoint</h3>
                <p className="text-sm text-muted-foreground">
                  Executive presentation slides
                </p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" 
                  onClick={() => handleQuickExport('word')}>
              <CardContent className="p-6 text-center">
                <FileText className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                <h3 className="font-semibold mb-2">Word Document</h3>
                <p className="text-sm text-muted-foreground">
                  Editable document format
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scheduledReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold">{report.name}</h4>
                        <Badge variant={report.status === 'active' ? 'default' : 'secondary'}>
                          {report.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <div>Frequency: {report.frequency} • Format: {report.format}</div>
                        <div>Recipients: {report.recipients.join(', ')}</div>
                        <div>Last sent: {formatDate(report.lastSent)} • Next: {formatDate(report.nextSend)}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => toggleReportStatus(report.id)}
                      >
                        {report.status === 'active' ? 'Pause' : 'Resume'}
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Export History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Export history will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
