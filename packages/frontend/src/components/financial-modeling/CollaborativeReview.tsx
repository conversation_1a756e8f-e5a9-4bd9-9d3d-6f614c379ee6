import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  MessageSquare, 
  Send, 
  ThumbsUp, 
  ThumbsDown,
  AlertCircle,
  CheckCircle,
  Clock,
  User,
  Reply
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { 
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface Comment {
  id: string
  author: {
    id: string
    name: string
    avatar?: string
    role: string
  }
  content: string
  timestamp: Date
  section: string
  status: 'open' | 'resolved' | 'acknowledged'
  replies: Reply[]
  reactions: {
    likes: number
    dislikes: number
    userReaction?: 'like' | 'dislike'
  }
}

interface Reply {
  id: string
  author: {
    id: string
    name: string
    avatar?: string
  }
  content: string
  timestamp: Date
}

interface ReviewStatus {
  reviewer: {
    id: string
    name: string
    avatar?: string
    role: string
  }
  status: 'pending' | 'approved' | 'rejected' | 'changes_requested'
  timestamp?: Date
  comments?: string
}

export function CollaborativeReview() {
  const [comments, setComments] = useState<Comment[]>([])
  const [reviewStatuses, setReviewStatuses] = useState<ReviewStatus[]>([])
  const [newComment, setNewComment] = useState('')
  const [selectedSection, setSelectedSection] = useState('overall')
  const [showCommentDialog, setShowCommentDialog] = useState(false)
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    loadComments()
    loadReviewStatuses()
  }, [])

  const loadComments = async () => {
    try {
      // Mock data - replace with actual API call
      const mockComments: Comment[] = [
        {
          id: '1',
          author: {
            id: 'user1',
            name: 'John Smith',
            avatar: '/avatars/john.jpg',
            role: 'Senior Analyst'
          },
          content: 'The revenue growth assumptions seem optimistic given the current market conditions. Have we considered the impact of increased competition?',
          timestamp: new Date('2024-01-15T10:30:00'),
          section: 'dcf_assumptions',
          status: 'open',
          replies: [
            {
              id: 'r1',
              author: {
                id: 'user2',
                name: 'Sarah Johnson',
                avatar: '/avatars/sarah.jpg'
              },
              content: 'Good point. I\'ve updated the model to include a more conservative scenario.',
              timestamp: new Date('2024-01-15T11:15:00')
            }
          ],
          reactions: {
            likes: 3,
            dislikes: 0,
            userReaction: 'like'
          }
        },
        {
          id: '2',
          author: {
            id: 'user3',
            name: 'Mike Chen',
            avatar: '/avatars/mike.jpg',
            role: 'VP Finance'
          },
          content: 'The comparable company selection looks comprehensive. However, I suggest adding TechCorp2 as they have similar business model.',
          timestamp: new Date('2024-01-15T14:20:00'),
          section: 'cca_comparables',
          status: 'acknowledged',
          replies: [],
          reactions: {
            likes: 2,
            dislikes: 0
          }
        },
        {
          id: '3',
          author: {
            id: 'user4',
            name: 'Lisa Wang',
            avatar: '/avatars/lisa.jpg',
            role: 'Director'
          },
          content: 'Excellent work on the sensitivity analysis. The tornado chart clearly shows the key value drivers.',
          timestamp: new Date('2024-01-15T16:45:00'),
          section: 'sensitivity',
          status: 'resolved',
          replies: [],
          reactions: {
            likes: 5,
            dislikes: 0
          }
        }
      ]

      setComments(mockComments)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load comments',
        variant: 'destructive'
      })
    }
  }

  const loadReviewStatuses = async () => {
    try {
      // Mock data - replace with actual API call
      const mockStatuses: ReviewStatus[] = [
        {
          reviewer: {
            id: 'user1',
            name: 'John Smith',
            avatar: '/avatars/john.jpg',
            role: 'Senior Analyst'
          },
          status: 'changes_requested',
          timestamp: new Date('2024-01-15T17:00:00'),
          comments: 'Please address the revenue growth assumptions before final approval.'
        },
        {
          reviewer: {
            id: 'user3',
            name: 'Mike Chen',
            avatar: '/avatars/mike.jpg',
            role: 'VP Finance'
          },
          status: 'approved',
          timestamp: new Date('2024-01-15T16:30:00'),
          comments: 'Comprehensive analysis. Approved for presentation.'
        },
        {
          reviewer: {
            id: 'user4',
            name: 'Lisa Wang',
            avatar: '/avatars/lisa.jpg',
            role: 'Director'
          },
          status: 'pending',
          timestamp: undefined
        }
      ]

      setReviewStatuses(mockStatuses)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load review statuses',
        variant: 'destructive'
      })
    }
  }

  const handleAddComment = async () => {
    if (!newComment.trim()) return

    try {
      const comment: Comment = {
        id: Date.now().toString(),
        author: {
          id: 'current-user',
          name: 'Current User',
          role: 'Analyst'
        },
        content: newComment,
        timestamp: new Date(),
        section: selectedSection,
        status: 'open',
        replies: [],
        reactions: {
          likes: 0,
          dislikes: 0
        }
      }

      setComments(prev => [comment, ...prev])
      setNewComment('')
      setShowCommentDialog(false)

      toast({
        title: 'Comment Added',
        description: 'Your comment has been added successfully'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add comment',
        variant: 'destructive'
      })
    }
  }

  const handleAddReply = async (commentId: string) => {
    if (!replyContent.trim()) return

    try {
      const reply: Reply = {
        id: Date.now().toString(),
        author: {
          id: 'current-user',
          name: 'Current User'
        },
        content: replyContent,
        timestamp: new Date()
      }

      setComments(prev => 
        prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, replies: [...comment.replies, reply] }
            : comment
        )
      )

      setReplyContent('')
      setReplyingTo(null)

      toast({
        title: 'Reply Added',
        description: 'Your reply has been added successfully'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add reply',
        variant: 'destructive'
      })
    }
  }

  const handleReaction = async (commentId: string, reaction: 'like' | 'dislike') => {
    try {
      setComments(prev => 
        prev.map(comment => {
          if (comment.id === commentId) {
            const currentReaction = comment.reactions.userReaction
            const newReactions = { ...comment.reactions }

            if (currentReaction === reaction) {
              // Remove reaction
              newReactions.userReaction = undefined
              newReactions[reaction === 'like' ? 'likes' : 'dislikes']--
            } else {
              // Add or change reaction
              if (currentReaction) {
                newReactions[currentReaction === 'like' ? 'likes' : 'dislikes']--
              }
              newReactions.userReaction = reaction
              newReactions[reaction === 'like' ? 'likes' : 'dislikes']++
            }

            return { ...comment, reactions: newReactions }
          }
          return comment
        })
      )
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update reaction',
        variant: 'destructive'
      })
    }
  }

  const handleStatusChange = async (commentId: string, status: Comment['status']) => {
    try {
      setComments(prev => 
        prev.map(comment => 
          comment.id === commentId ? { ...comment, status } : comment
        )
      )

      toast({
        title: 'Status Updated',
        description: `Comment marked as ${status}`
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update status',
        variant: 'destructive'
      })
    }
  }

  const getStatusIcon = (status: Comment['status']) => {
    switch (status) {
      case 'open':
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      case 'resolved':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'acknowledged':
        return <Clock className="h-4 w-4 text-blue-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadgeVariant = (status: ReviewStatus['status']) => {
    switch (status) {
      case 'approved':
        return 'default'
      case 'rejected':
        return 'destructive'
      case 'changes_requested':
        return 'secondary'
      case 'pending':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(timestamp)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Collaborative Review
          </h3>
          <p className="text-sm text-muted-foreground">
            Comments and feedback from team members
          </p>
        </div>
        <Dialog open={showCommentDialog} onOpenChange={setShowCommentDialog}>
          <DialogTrigger asChild>
            <Button>
              <MessageSquare className="h-4 w-4 mr-2" />
              Add Comment
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Comment</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Section</label>
                <select 
                  value={selectedSection} 
                  onChange={(e) => setSelectedSection(e.target.value)}
                  className="w-full mt-1 p-2 border rounded-md"
                >
                  <option value="overall">Overall</option>
                  <option value="dcf_assumptions">DCF Assumptions</option>
                  <option value="cca_comparables">CCA Comparables</option>
                  <option value="sensitivity">Sensitivity Analysis</option>
                  <option value="scenarios">Scenarios</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Comment</label>
                <Textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Enter your comment..."
                  className="mt-1"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCommentDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddComment}>
                  Add Comment
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Review Status */}
      <Card>
        <CardHeader>
          <CardTitle>Review Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {reviewStatuses.map((status, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={status.reviewer.avatar} />
                    <AvatarFallback>
                      {status.reviewer.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{status.reviewer.name}</div>
                    <div className="text-sm text-muted-foreground">{status.reviewer.role}</div>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant={getStatusBadgeVariant(status.status)}>
                    {status.status.replace('_', ' ')}
                  </Badge>
                  {status.timestamp && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {formatTimestamp(status.timestamp)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Comments */}
      <Card>
        <CardHeader>
          <CardTitle>Comments ({comments.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {comments.map((comment) => (
              <div key={comment.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comment.author.avatar} />
                      <AvatarFallback>
                        {comment.author.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{comment.author.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {comment.author.role} • {formatTimestamp(comment.timestamp)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(comment.status)}
                    <Badge variant="outline">{comment.section}</Badge>
                  </div>
                </div>

                <div className="mb-3">
                  <p className="text-sm">{comment.content}</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={() => handleReaction(comment.id, 'like')}
                      className={`flex items-center gap-1 text-sm ${
                        comment.reactions.userReaction === 'like' 
                          ? 'text-blue-600' 
                          : 'text-muted-foreground hover:text-blue-600'
                      }`}
                    >
                      <ThumbsUp className="h-4 w-4" />
                      {comment.reactions.likes}
                    </button>
                    <button
                      onClick={() => handleReaction(comment.id, 'dislike')}
                      className={`flex items-center gap-1 text-sm ${
                        comment.reactions.userReaction === 'dislike' 
                          ? 'text-red-600' 
                          : 'text-muted-foreground hover:text-red-600'
                      }`}
                    >
                      <ThumbsDown className="h-4 w-4" />
                      {comment.reactions.dislikes}
                    </button>
                    <button
                      onClick={() => setReplyingTo(comment.id)}
                      className="flex items-center gap-1 text-sm text-muted-foreground hover:text-blue-600"
                    >
                      <Reply className="h-4 w-4" />
                      Reply
                    </button>
                  </div>
                  <div className="flex items-center gap-2">
                    {comment.status === 'open' && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusChange(comment.id, 'acknowledged')}
                        >
                          Acknowledge
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleStatusChange(comment.id, 'resolved')}
                        >
                          Resolve
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Replies */}
                {comment.replies.length > 0 && (
                  <div className="mt-4 pl-4 border-l-2 border-gray-200 space-y-3">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex items-start gap-3">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={reply.author.avatar} />
                          <AvatarFallback>
                            {reply.author.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">{reply.author.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {formatTimestamp(reply.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm">{reply.content}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Reply Input */}
                {replyingTo === comment.id && (
                  <div className="mt-4 pl-4 border-l-2 border-gray-200">
                    <div className="flex gap-2">
                      <Input
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        placeholder="Write a reply..."
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        onClick={() => handleAddReply(comment.id)}
                        disabled={!replyContent.trim()}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setReplyingTo(null)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
