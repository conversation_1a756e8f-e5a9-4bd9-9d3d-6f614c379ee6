import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui/table'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import {
    AlertTriangle,
    CheckCircle,
    Download,
    FileSpreadsheet,
    Share2,
    Target
} from 'lucide-react'
import { useEffect, useState } from 'react'
import {
    Bar,
    BarChart,
    CartesianGrid,
    Cell,
    Pie,
    PieChart,
    PolarAngleAxis,
    PolarGrid,
    PolarRadiusAxis,
    Radar,
    RadarChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts'

interface ValuationMethod {
  method: string
  approach: 'DCF' | 'CCA' | 'PTA' | 'LBO'
  lowValue: number
  midValue: number
  highValue: number
  confidence: 'High' | 'Medium' | 'Low'
  weight: number
  lastUpdated: Date
}

interface ValuationSummaryData {
  targetCompany: string
  valuationDate: Date
  methods: ValuationMethod[]
  weightedAverage: {
    low: number
    mid: number
    high: number
  }
  recommendation: {
    value: number
    range: [number, number]
    rationale: string
  }
  keyAssumptions: Array<{
    category: string
    assumption: string
    value: string
    sensitivity: 'High' | 'Medium' | 'Low'
  }>
  riskFactors: Array<{
    factor: string
    impact: 'High' | 'Medium' | 'Low'
    description: string
  }>
}

export function ValuationSummary() {
  const [summaryData, setSummaryData] = useState<ValuationSummaryData | null>(null)
  const [selectedView, setSelectedView] = useState('summary')
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadValuationSummary()
  }, [])

  const loadValuationSummary = async () => {
    try {
      // Mock data - replace with actual API call
      const mockData: ValuationSummaryData = {
        targetCompany: 'TechCorp Inc.',
        valuationDate: new Date(),
        methods: [
          {
            method: 'Discounted Cash Flow',
            approach: 'DCF',
            lowValue: *********,
            midValue: 1200000000,
            highValue: 1450000000,
            confidence: 'High',
            weight: 0.4,
            lastUpdated: new Date()
          },
          {
            method: 'Comparable Company Analysis',
            approach: 'CCA',
            lowValue: 1050000000,
            midValue: 1300000000,
            highValue: 1550000000,
            confidence: 'Medium',
            weight: 0.3,
            lastUpdated: new Date()
          },
          {
            method: 'Precedent Transaction Analysis',
            approach: 'PTA',
            lowValue: 1100000000,
            midValue: 1400000000,
            highValue: 1700000000,
            confidence: 'Medium',
            weight: 0.2,
            lastUpdated: new Date()
          },
          {
            method: 'LBO Analysis',
            approach: 'LBO',
            lowValue: *********,
            midValue: 1150000000,
            highValue: 1400000000,
            confidence: 'Low',
            weight: 0.1,
            lastUpdated: new Date()
          }
        ],
        weightedAverage: {
          low: 1000000000,
          mid: 1250000000,
          high: 1500000000
        },
        recommendation: {
          value: 1250000000,
          range: [1150000000, 1350000000],
          rationale: 'Based on DCF analysis with strong fundamentals and conservative growth assumptions, supported by trading multiples analysis.'
        },
        keyAssumptions: [
          {
            category: 'Growth',
            assumption: 'Revenue Growth Rate (5-year avg)',
            value: '12%',
            sensitivity: 'High'
          },
          {
            category: 'Profitability',
            assumption: 'EBITDA Margin',
            value: '25%',
            sensitivity: 'Medium'
          },
          {
            category: 'Valuation',
            assumption: 'Discount Rate (WACC)',
            value: '10%',
            sensitivity: 'High'
          },
          {
            category: 'Terminal',
            assumption: 'Terminal Growth Rate',
            value: '2.5%',
            sensitivity: 'Medium'
          }
        ],
        riskFactors: [
          {
            factor: 'Market Competition',
            impact: 'High',
            description: 'Increasing competition from new market entrants'
          },
          {
            factor: 'Regulatory Changes',
            impact: 'Medium',
            description: 'Potential regulatory changes affecting the industry'
          },
          {
            factor: 'Technology Disruption',
            impact: 'Medium',
            description: 'Risk of technological disruption in core business'
          }
        ]
      }

      setSummaryData(mockData)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load valuation summary',
        variant: 'destructive'
      })
    }
  }

  const handleExportPDF = async () => {
    try {
      setIsExporting(true)
      // Mock export - replace with actual PDF generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast({
        title: 'Export Complete',
        description: 'Valuation summary exported to PDF successfully'
      })
    } catch (error) {
      toast({
        title: 'Export Error',
        description: 'Failed to export valuation summary',
        variant: 'destructive'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleExportExcel = async () => {
    try {
      setIsExporting(true)
      // Mock export - replace with actual Excel generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      toast({
        title: 'Export Complete',
        description: 'Valuation summary exported to Excel successfully'
      })
    } catch (error) {
      toast({
        title: 'Export Error',
        description: 'Failed to export valuation summary',
        variant: 'destructive'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleShare = () => {
    toast({
      title: 'Share',
      description: 'Sharing functionality will be implemented'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getConfidenceBadgeVariant = (confidence: string) => {
    switch (confidence) {
      case 'High':
        return 'default'
      case 'Medium':
        return 'secondary'
      case 'Low':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getRiskBadgeVariant = (impact: string) => {
    switch (impact) {
      case 'High':
        return 'destructive'
      case 'Medium':
        return 'secondary'
      case 'Low':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getSensitivityColor = (sensitivity: string) => {
    switch (sensitivity) {
      case 'High':
        return '#ef4444'
      case 'Medium':
        return '#f59e0b'
      case 'Low':
        return '#10b981'
      default:
        return '#6b7280'
    }
  }

  if (!summaryData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading valuation summary...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <FileSpreadsheet className="h-6 w-6" />
            Valuation Summary
          </h2>
          <p className="text-muted-foreground">
            Comprehensive summary of all valuation approaches and results for {summaryData.targetCompany}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" onClick={handleExportExcel} disabled={isExporting}>
            <Download className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button onClick={handleExportPDF} disabled={isExporting}>
            <Download className="h-4 w-4 mr-2" />
            PDF Report
          </Button>
        </div>
      </div>

      <Tabs value={selectedView} onValueChange={setSelectedView} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="methods">Methods</TabsTrigger>
          <TabsTrigger value="assumptions">Assumptions</TabsTrigger>
          <TabsTrigger value="risks">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Recommended Value</div>
                <div className="text-2xl font-bold">{formatCurrency(summaryData.recommendation.value)}</div>
                <div className="text-xs text-muted-foreground">
                  Range: {formatCurrency(summaryData.recommendation.range[0])} - {formatCurrency(summaryData.recommendation.range[1])}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Weighted Low</div>
                <div className="text-2xl font-bold">{formatCurrency(summaryData.weightedAverage.low)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Weighted Mid</div>
                <div className="text-2xl font-bold">{formatCurrency(summaryData.weightedAverage.mid)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Weighted High</div>
                <div className="text-2xl font-bold">{formatCurrency(summaryData.weightedAverage.high)}</div>
              </CardContent>
            </Card>
          </div>

          {/* Valuation Methods Comparison */}
          <Card>
            <CardHeader>
              <CardTitle>Valuation Methods Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={summaryData.methods.map(method => ({
                  method: method.method,
                  low: method.lowValue / 1000000,
                  mid: method.midValue / 1000000,
                  high: method.highValue / 1000000
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="method" angle={-45} textAnchor="end" height={100} />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}M`, '']} />
                  <Bar dataKey="low" fill="#ef4444" name="Low" />
                  <Bar dataKey="mid" fill="#3b82f6" name="Mid" />
                  <Bar dataKey="high" fill="#10b981" name="High" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Recommendation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Valuation Recommendation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-lg font-semibold">
                  Recommended Valuation: {formatCurrency(summaryData.recommendation.value)}
                </div>
                <div className="text-muted-foreground">
                  {summaryData.recommendation.rationale}
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Based on comprehensive analysis of {summaryData.methods.length} valuation methods</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="methods" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Valuation Methods Detail</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Method</TableHead>
                    <TableHead>Approach</TableHead>
                    <TableHead>Low Value</TableHead>
                    <TableHead>Mid Value</TableHead>
                    <TableHead>High Value</TableHead>
                    <TableHead>Confidence</TableHead>
                    <TableHead>Weight</TableHead>
                    <TableHead>Last Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {summaryData.methods.map((method, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{method.method}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{method.approach}</Badge>
                      </TableCell>
                      <TableCell>{formatCurrency(method.lowValue)}</TableCell>
                      <TableCell className="font-semibold">{formatCurrency(method.midValue)}</TableCell>
                      <TableCell>{formatCurrency(method.highValue)}</TableCell>
                      <TableCell>
                        <Badge variant={getConfidenceBadgeVariant(method.confidence)}>
                          {method.confidence}
                        </Badge>
                      </TableCell>
                      <TableCell>{(method.weight * 100).toFixed(0)}%</TableCell>
                      <TableCell>{method.lastUpdated.toLocaleDateString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Method Weights Pie Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Method Weights</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={summaryData.methods.map(method => ({
                      name: method.approach,
                      value: method.weight * 100,
                      fill: method.approach === 'DCF' ? '#3b82f6' :
                            method.approach === 'CCA' ? '#10b981' :
                            method.approach === 'PTA' ? '#f59e0b' : '#ef4444'
                    }))}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name}: ${value}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {summaryData.methods.map((entry, index) => (
                      <Cell key={`cell-${index}`} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assumptions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Key Assumptions</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Assumption</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Sensitivity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {summaryData.keyAssumptions.map((assumption, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{assumption.category}</TableCell>
                      <TableCell>{assumption.assumption}</TableCell>
                      <TableCell className="font-semibold">{assumption.value}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          style={{
                            borderColor: getSensitivityColor(assumption.sensitivity),
                            color: getSensitivityColor(assumption.sensitivity)
                          }}
                        >
                          {assumption.sensitivity}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Sensitivity Radar Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Assumption Sensitivity Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={summaryData.keyAssumptions.map(assumption => ({
                  assumption: assumption.assumption.split(' ')[0],
                  sensitivity: assumption.sensitivity === 'High' ? 3 : assumption.sensitivity === 'Medium' ? 2 : 1,
                  fullMark: 3
                }))}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="assumption" />
                  <PolarRadiusAxis angle={90} domain={[0, 3]} />
                  <Radar
                    name="Sensitivity"
                    dataKey="sensitivity"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Risk Factors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {summaryData.riskFactors.map((risk, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-semibold">{risk.factor}</h4>
                      <Badge variant={getRiskBadgeVariant(risk.impact)}>
                        {risk.impact} Impact
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{risk.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Risk Impact Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Risk Impact Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={summaryData.riskFactors.map(risk => ({
                  factor: risk.factor,
                  impact: risk.impact === 'High' ? 3 : risk.impact === 'Medium' ? 2 : 1
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="factor" angle={-45} textAnchor="end" height={100} />
                  <YAxis domain={[0, 3]} />
                  <Tooltip />
                  <Bar dataKey="impact" fill="#ef4444" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
