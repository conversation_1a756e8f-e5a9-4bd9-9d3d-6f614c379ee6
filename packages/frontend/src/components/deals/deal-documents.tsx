import React, { useState, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { 
  Upload,
  FileText,
  Download,
  Eye,
  Trash2,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  File,
  Image,
  Archive,
  Lock,
  Globe,
  Users
} from 'lucide-react'
import { DealDocument, DocumentType, useDealDocuments, useUploadDocument } from '@/services/deal.service'
import { formatBytes, formatDistanceToNow } from '@/lib/utils'

interface DealDocumentsProps {
  dealId: string
  className?: string
}

interface UploadDialogProps {
  dealId: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

const documentTypes = [
  { value: DocumentType.CONTRACT, label: 'Contract', icon: FileText },
  { value: DocumentType.FINANCIAL, label: 'Financial', icon: FileText },
  { value: DocumentType.LEGAL, label: 'Legal', icon: FileText },
  { value: DocumentType.TECHNICAL, label: 'Technical', icon: FileText },
  { value: DocumentType.OTHER, label: 'Other', icon: File }
]

const accessLevels = [
  { value: 'PUBLIC', label: 'Public', icon: Globe, description: 'Visible to everyone' },
  { value: 'TEAM', label: 'Team', icon: Users, description: 'Visible to deal team' },
  { value: 'RESTRICTED', label: 'Restricted', icon: Lock, description: 'Restricted access' }
]

function UploadDialog({ dealId, open, onOpenChange }: UploadDialogProps) {
  const [files, setFiles] = useState<FileList | null>(null)
  const [documentType, setDocumentType] = useState<DocumentType>(DocumentType.OTHER)
  const [accessLevel, setAccessLevel] = useState<'PUBLIC' | 'TEAM' | 'RESTRICTED'>('TEAM')
  const [description, setDescription] = useState('')
  const [isConfidential, setIsConfidential] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const uploadMutation = useUploadDocument()

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFiles(e.target.files)
  }

  const handleUpload = async () => {
    if (!files || files.length === 0) return

    const formData = new FormData()
    
    // Add each file
    Array.from(files).forEach((file, index) => {
      formData.append(`files`, file)
    })
    
    // Add metadata
    formData.append('type', documentType)
    formData.append('accessLevel', accessLevel)
    formData.append('description', description)
    formData.append('isConfidential', isConfidential.toString())

    try {
      await uploadMutation.mutateAsync({ dealId, documentData: formData })
      
      // Reset form
      setFiles(null)
      setDescription('')
      setIsConfidential(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
      
      onOpenChange(false)
    } catch (error) {
      console.error('Upload failed:', error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Documents</DialogTitle>
          <DialogDescription>
            Upload documents related to this deal
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="files">Select Files</Label>
            <Input
              ref={fileInputRef}
              id="files"
              type="file"
              multiple
              onChange={handleFileSelect}
              className="cursor-pointer"
            />
            {files && files.length > 0 && (
              <div className="text-sm text-muted-foreground">
                {files.length} file(s) selected
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="documentType">Document Type</Label>
            <Select value={documentType} onValueChange={(value) => setDocumentType(value as DocumentType)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="accessLevel">Access Level</Label>
            <Select value={accessLevel} onValueChange={(value) => setAccessLevel(value as any)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {accessLevels.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    <div className="flex items-center space-x-2">
                      <level.icon className="h-4 w-4" />
                      <div>
                        <div>{level.label}</div>
                        <div className="text-xs text-muted-foreground">{level.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of the document(s)"
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isConfidential"
              checked={isConfidential}
              onChange={(e) => setIsConfidential(e.target.checked)}
              className="rounded"
            />
            <Label htmlFor="isConfidential" className="text-sm">
              Mark as confidential
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleUpload}
            disabled={!files || files.length === 0 || uploadMutation.isPending}
          >
            {uploadMutation.isPending ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function DealDocuments({ dealId, className = '' }: DealDocumentsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<DocumentType | 'ALL'>('ALL')
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  
  const { data: documents = [], isLoading, error } = useDealDocuments(dealId)

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'ALL' || doc.type === typeFilter
    
    return matchesSearch && matchesType
  })

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return Image
    if (mimeType.includes('pdf')) return FileText
    if (mimeType.includes('zip') || mimeType.includes('archive')) return Archive
    return File
  }

  const getAccessIcon = (accessLevel: string) => {
    switch (accessLevel) {
      case 'PUBLIC': return Globe
      case 'TEAM': return Users
      case 'RESTRICTED': return Lock
      default: return Users
    }
  }

  const handleDownload = async (document: DealDocument) => {
    try {
      // This would call the download API
      console.log('Downloading document:', document.id)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handlePreview = (document: DealDocument) => {
    // This would open a preview modal or new tab
    console.log('Previewing document:', document.id)
  }

  const handleDelete = async (document: DealDocument) => {
    if (confirm('Are you sure you want to delete this document?')) {
      try {
        // This would call the delete API
        console.log('Deleting document:', document.id)
      } catch (error) {
        console.error('Delete failed:', error)
      }
    }
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            Failed to load documents
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Documents</span>
            <Badge variant="secondary">{documents.length}</Badge>
          </CardTitle>
          
          <Button onClick={() => setShowUploadDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Upload
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Search and filters */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Types</SelectItem>
              {documentTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Documents list */}
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-muted rounded-lg" />
              </div>
            ))}
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No documents found</p>
            <p className="text-sm">
              {searchTerm || typeFilter !== 'ALL' 
                ? 'Try adjusting your search or filters'
                : 'Upload documents to get started'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredDocuments.map((document) => {
              const FileIcon = getFileIcon(document.mimeType)
              const AccessIcon = getAccessIcon(document.accessLevel)
              
              return (
                <div
                  key={document.id}
                  className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-shrink-0">
                    <FileIcon className="h-8 w-8 text-muted-foreground" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium truncate">{document.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {document.type}
                      </Badge>
                      {document.isConfidential && (
                        <Badge variant="destructive" className="text-xs">
                          Confidential
                        </Badge>
                      )}
                    </div>
                    
                    {document.description && (
                      <p className="text-sm text-muted-foreground mb-1 truncate">
                        {document.description}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>{formatBytes(document.fileSize)}</span>
                      <span>v{document.version}</span>
                      <span>{formatDistanceToNow(new Date(document.uploadedAt), { addSuffix: true })}</span>
                      <div className="flex items-center space-x-1">
                        <AccessIcon className="h-3 w-3" />
                        <span>{document.accessLevel.toLowerCase()}</span>
                      </div>
                      {document.downloadCount > 0 && (
                        <span>{document.downloadCount} downloads</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePreview(document)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownload(document)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(document)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
      
      <UploadDialog
        dealId={dealId}
        open={showUploadDialog}
        onOpenChange={setShowUploadDialog}
      />
    </Card>
  )
}

export default DealDocuments
