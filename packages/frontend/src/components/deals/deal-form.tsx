import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import {
    ConfidentialityLevel,
    CreateDealRequest,
    Deal,
    DealPriority,
    DealSource,
    DealType,
    RiskLevel,
    UpdateDealRequest
} from '@/services/deal.service'
import {
    Building,
    DollarSign,
    FileText,
    Plus,
    Target,
    X
} from 'lucide-react'
import React, { useState } from 'react'

interface DealFormProps {
  deal?: Deal
  onSubmit: (data: CreateDealRequest | UpdateDealRequest) => void
  onCancel: () => void
  isLoading?: boolean
  className?: string
}

const dealTypes = [
  { value: DealType.ACQUISITION, label: 'Acquisition' },
  { value: DealType.MERGER, label: 'Merger' },
  { value: DealType.JOINT_VENTURE, label: 'Joint Venture' },
  { value: DealType.STRATEGIC_PARTNERSHIP, label: 'Strategic Partnership' },
  { value: DealType.ASSET_PURCHASE, label: 'Asset Purchase' },
  { value: DealType.DIVESTITURE, label: 'Divestiture' }
]

const dealSources = [
  { value: DealSource.INBOUND, label: 'Inbound' },
  { value: DealSource.OUTBOUND, label: 'Outbound' },
  { value: DealSource.REFERRAL, label: 'Referral' },
  { value: DealSource.INVESTMENT_BANK, label: 'Investment Bank' },
  { value: DealSource.BROKER, label: 'Broker' },
  { value: DealSource.DIRECT, label: 'Direct' },
  { value: DealSource.CONFERENCE, label: 'Conference' },
  { value: DealSource.OTHER, label: 'Other' }
]

const priorities = [
  { value: DealPriority.LOW, label: 'Low', color: 'bg-gray-100 text-gray-800' },
  { value: DealPriority.MEDIUM, label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  { value: DealPriority.HIGH, label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: DealPriority.URGENT, label: 'Urgent', color: 'bg-red-100 text-red-800' }
]

const riskLevels = [
  { value: RiskLevel.LOW, label: 'Low', color: 'bg-green-100 text-green-800' },
  { value: RiskLevel.MEDIUM, label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
  { value: RiskLevel.HIGH, label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: RiskLevel.CRITICAL, label: 'Critical', color: 'bg-red-100 text-red-800' }
]

const confidentialityLevels = [
  { value: ConfidentialityLevel.PUBLIC, label: 'Public' },
  { value: ConfidentialityLevel.INTERNAL, label: 'Internal' },
  { value: ConfidentialityLevel.CONFIDENTIAL, label: 'Confidential' },
  { value: ConfidentialityLevel.HIGHLY_CONFIDENTIAL, label: 'Highly Confidential' }
]

export function DealForm({ 
  deal, 
  onSubmit, 
  onCancel, 
  isLoading = false,
  className = '' 
}: DealFormProps) {
  const [formData, setFormData] = useState<CreateDealRequest | UpdateDealRequest>({
    title: deal?.title || '',
    description: deal?.description || '',
    dealType: deal?.dealType || DealType.ACQUISITION,
    dealSource: deal?.dealSource || DealSource.INBOUND,
    targetCompany: deal?.targetCompany || '',
    targetIndustry: deal?.targetIndustry || '',
    dealValue: deal?.dealValue || undefined,
    currency: deal?.currency || 'USD',
    priority: deal?.priority || DealPriority.MEDIUM,
    expectedCloseDate: deal?.expectedCloseDate ? deal.expectedCloseDate.split('T')[0] : '',
    assignedTo: deal?.assignedTo || '',
    tags: deal?.tags || [],
    confidentiality: deal?.confidentiality || ConfidentialityLevel.INTERNAL,
    ...(deal && {
      riskLevel: deal.riskLevel,
      competitiveProcess: deal.competitiveProcess,
      competitors: deal.competitors,
      internalNotes: deal.internalNotes,
      nextSteps: deal.nextSteps,
      keyRisks: deal.keyRisks,
      keyOpportunities: deal.keyOpportunities
    })
  })

  const [newTag, setNewTag] = useState('')
  const [newCompetitor, setNewCompetitor] = useState('')
  const [newRisk, setNewRisk] = useState('')
  const [newOpportunity, setNewOpportunity] = useState('')

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayAdd = (field: string, value: string, setter: (value: string) => void) => {
    if (value.trim()) {
      const currentArray = (formData as any)[field] || []
      setFormData(prev => ({
        ...prev,
        [field]: [...currentArray, value.trim()]
      }))
      setter('')
    }
  }

  const handleArrayRemove = (field: string, index: number) => {
    const currentArray = (formData as any)[field] || []
    setFormData(prev => ({
      ...prev,
      [field]: currentArray.filter((_: any, i: number) => i !== index)
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title?.trim() || !formData.targetCompany?.trim()) {
      return
    }

    // Clean up the data
    const submitData = {
      ...formData,
      dealValue: formData.dealValue ? Number(formData.dealValue) : undefined,
      expectedCloseDate: formData.expectedCloseDate || undefined
    }

    onSubmit(submitData)
  }

  return (
    <form onSubmit={handleSubmit} className={`space-y-6 ${className}`}>
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="competitive">Competitive</TabsTrigger>
          <TabsTrigger value="notes">Notes & Risks</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Deal Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Deal Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter deal title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetCompany">Target Company *</Label>
                  <Input
                    id="targetCompany"
                    value={formData.targetCompany}
                    onChange={(e) => handleInputChange('targetCompany', e.target.value)}
                    placeholder="Enter target company name"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Brief description of the deal"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dealType">Deal Type</Label>
                  <Select value={formData.dealType} onValueChange={(value) => handleInputChange('dealType', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {dealTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dealSource">Deal Source</Label>
                  <Select value={formData.dealSource} onValueChange={(value) => handleInputChange('dealSource', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {dealSources.map((source) => (
                        <SelectItem key={source.value} value={source.value}>
                          {source.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetIndustry">Target Industry</Label>
                  <Input
                    id="targetIndustry"
                    value={formData.targetIndustry}
                    onChange={(e) => handleInputChange('targetIndustry', e.target.value)}
                    placeholder="e.g., Technology, Healthcare"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorities.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confidentiality">Confidentiality</Label>
                  <Select value={formData.confidentiality} onValueChange={(value) => handleInputChange('confidentiality', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {confidentialityLevels.map((level) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expectedCloseDate">Expected Close Date</Label>
                  <Input
                    id="expectedCloseDate"
                    type="date"
                    value={formData.expectedCloseDate}
                    onChange={(e) => handleInputChange('expectedCloseDate', e.target.value)}
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {(formData.tags || []).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                      <span>{tag}</span>
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleArrayRemove('tags', index)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add tag"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleArrayAdd('tags', newTag, setNewTag)
                      }
                    }}
                  />
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleArrayAdd('tags', newTag, setNewTag)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Financial Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dealValue">Deal Value</Label>
                  <Input
                    id="dealValue"
                    type="number"
                    value={formData.dealValue || ''}
                    onChange={(e) => handleInputChange('dealValue', e.target.value ? Number(e.target.value) : undefined)}
                    placeholder="Enter deal value"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                      <SelectItem value="AUD">AUD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {deal && (
                <div className="space-y-2">
                  <Label htmlFor="riskLevel">Risk Level</Label>
                  <Select value={formData.riskLevel} onValueChange={(value) => handleInputChange('riskLevel', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {riskLevels.map((level) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="competitive" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Competitive Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {deal && (
                <>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="competitiveProcess"
                      checked={formData.competitiveProcess || false}
                      onChange={(e) => handleInputChange('competitiveProcess', e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="competitiveProcess">Competitive Process</Label>
                  </div>

                  <div className="space-y-2">
                    <Label>Competitors</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {(formData.competitors || []).map((competitor, index) => (
                        <Badge key={index} variant="outline" className="flex items-center space-x-1">
                          <span>{competitor}</span>
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => handleArrayRemove('competitors', index)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex space-x-2">
                      <Input
                        value={newCompetitor}
                        onChange={(e) => setNewCompetitor(e.target.value)}
                        placeholder="Add competitor"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleArrayAdd('competitors', newCompetitor, setNewCompetitor)
                          }
                        }}
                      />
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleArrayAdd('competitors', newCompetitor, setNewCompetitor)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Notes & Risk Assessment</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {deal && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="internalNotes">Internal Notes</Label>
                    <Textarea
                      id="internalNotes"
                      value={formData.internalNotes || ''}
                      onChange={(e) => handleInputChange('internalNotes', e.target.value)}
                      placeholder="Internal notes and observations"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nextSteps">Next Steps</Label>
                    <Textarea
                      id="nextSteps"
                      value={formData.nextSteps || ''}
                      onChange={(e) => handleInputChange('nextSteps', e.target.value)}
                      placeholder="Planned next steps and actions"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Key Risks</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {(formData.keyRisks || []).map((risk, index) => (
                        <Badge key={index} variant="destructive" className="flex items-center space-x-1">
                          <span>{risk}</span>
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => handleArrayRemove('keyRisks', index)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex space-x-2">
                      <Input
                        value={newRisk}
                        onChange={(e) => setNewRisk(e.target.value)}
                        placeholder="Add key risk"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleArrayAdd('keyRisks', newRisk, setNewRisk)
                          }
                        }}
                      />
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleArrayAdd('keyRisks', newRisk, setNewRisk)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Key Opportunities</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {(formData.keyOpportunities || []).map((opportunity, index) => (
                        <Badge key={index} variant="default" className="flex items-center space-x-1">
                          <span>{opportunity}</span>
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => handleArrayRemove('keyOpportunities', index)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex space-x-2">
                      <Input
                        value={newOpportunity}
                        onChange={(e) => setNewOpportunity(e.target.value)}
                        placeholder="Add key opportunity"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleArrayAdd('keyOpportunities', newOpportunity, setNewOpportunity)
                          }
                        }}
                      />
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleArrayAdd('keyOpportunities', newOpportunity, setNewOpportunity)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-4 pt-6 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={!formData.title?.trim() || !formData.targetCompany?.trim() || isLoading}
        >
          {isLoading ? 'Saving...' : deal ? 'Update Deal' : 'Create Deal'}
        </Button>
      </div>
    </form>
  )
}

export default DealForm
