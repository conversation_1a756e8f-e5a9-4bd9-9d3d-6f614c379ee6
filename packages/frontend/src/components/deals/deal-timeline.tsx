import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  Clock,
  CheckCircle,
  Circle,
  AlertCircle,
  Calendar,
  User,
  FileText,
  MessageSquare,
  ArrowRight,
  Target,
  TrendingUp
} from 'lucide-react'
import { Deal, DealStageHistory } from '@/services/deal.service'
import { formatDistanceToNow, format } from 'date-fns'

interface DealTimelineProps {
  deal: Deal
  onStageChange?: (stageId: string) => void
  className?: string
}

interface TimelineEvent {
  id: string
  type: 'stage_change' | 'milestone' | 'activity' | 'note' | 'document' | 'task'
  title: string
  description?: string
  timestamp: string
  user?: {
    name: string
    avatar?: string
  }
  metadata?: Record<string, any>
  status?: 'completed' | 'in_progress' | 'pending' | 'overdue'
}

export function DealTimeline({ deal, onStageChange, className = '' }: DealTimelineProps) {
  // Combine all timeline events from different sources
  const timelineEvents: TimelineEvent[] = React.useMemo(() => {
    const events: TimelineEvent[] = []

    // Add stage history events
    if (deal.stageHistory) {
      deal.stageHistory.forEach((stage) => {
        events.push({
          id: `stage-${stage.id}`,
          type: 'stage_change',
          title: `Moved to ${stage.stageName || 'Unknown Stage'}`,
          description: stage.reason || 'Stage changed',
          timestamp: stage.enteredAt,
          user: {
            name: stage.changedBy || 'System'
          },
          metadata: {
            stageId: stage.stageId,
            daysInStage: stage.daysInStage
          }
        })
      })
    }

    // Add milestone events
    if (deal.milestones) {
      deal.milestones.forEach((milestone) => {
        events.push({
          id: `milestone-${milestone.id}`,
          type: 'milestone',
          title: milestone.title,
          description: milestone.description,
          timestamp: milestone.targetDate,
          status: milestone.isCompleted ? 'completed' : 
                  new Date(milestone.targetDate) < new Date() ? 'overdue' : 'pending',
          metadata: {
            isCompleted: milestone.isCompleted,
            completedAt: milestone.completedAt
          }
        })
      })
    }

    // Add activity events
    if (deal.activities) {
      deal.activities.slice(0, 10).forEach((activity) => {
        events.push({
          id: `activity-${activity.id}`,
          type: 'activity',
          title: activity.title,
          description: activity.description,
          timestamp: activity.startTime,
          user: {
            name: activity.createdBy || 'Unknown'
          },
          metadata: {
            activityType: activity.type,
            duration: activity.duration
          }
        })
      })
    }

    // Add note events
    if (deal.notes) {
      deal.notes.slice(0, 5).forEach((note) => {
        events.push({
          id: `note-${note.id}`,
          type: 'note',
          title: 'Note Added',
          description: note.content.substring(0, 100) + (note.content.length > 100 ? '...' : ''),
          timestamp: note.createdAt,
          user: {
            name: note.createdBy || 'Unknown'
          }
        })
      })
    }

    // Add document events
    if (deal.documents) {
      deal.documents.slice(0, 5).forEach((doc) => {
        events.push({
          id: `document-${doc.id}`,
          type: 'document',
          title: `Document: ${doc.name}`,
          description: `${doc.type} document uploaded`,
          timestamp: doc.uploadedAt,
          user: {
            name: doc.uploadedBy || 'Unknown'
          },
          metadata: {
            documentType: doc.type,
            fileSize: doc.fileSize
          }
        })
      })
    }

    // Add task events
    if (deal.tasks) {
      deal.tasks.slice(0, 5).forEach((task) => {
        events.push({
          id: `task-${task.id}`,
          type: 'task',
          title: `Task: ${task.title}`,
          description: task.description,
          timestamp: task.createdAt,
          status: task.status === 'DONE' ? 'completed' : 
                  task.status === 'IN_PROGRESS' ? 'in_progress' : 
                  task.dueDate && new Date(task.dueDate) < new Date() ? 'overdue' : 'pending',
          user: {
            name: task.createdBy || 'Unknown'
          },
          metadata: {
            taskStatus: task.status,
            priority: task.priority,
            dueDate: task.dueDate
          }
        })
      })
    }

    // Sort events by timestamp (newest first)
    return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }, [deal])

  const getEventIcon = (event: TimelineEvent) => {
    switch (event.type) {
      case 'stage_change':
        return <ArrowRight className="h-4 w-4" />
      case 'milestone':
        return event.status === 'completed' ? 
          <CheckCircle className="h-4 w-4 text-green-600" /> : 
          <Target className="h-4 w-4" />
      case 'activity':
        return <TrendingUp className="h-4 w-4" />
      case 'note':
        return <MessageSquare className="h-4 w-4" />
      case 'document':
        return <FileText className="h-4 w-4" />
      case 'task':
        return event.status === 'completed' ? 
          <CheckCircle className="h-4 w-4 text-green-600" /> : 
          event.status === 'overdue' ? 
          <AlertCircle className="h-4 w-4 text-red-600" /> :
          <Circle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getEventColor = (event: TimelineEvent) => {
    switch (event.type) {
      case 'stage_change':
        return 'border-blue-200 bg-blue-50'
      case 'milestone':
        return event.status === 'completed' ? 
          'border-green-200 bg-green-50' : 
          event.status === 'overdue' ? 
          'border-red-200 bg-red-50' :
          'border-yellow-200 bg-yellow-50'
      case 'activity':
        return 'border-purple-200 bg-purple-50'
      case 'note':
        return 'border-gray-200 bg-gray-50'
      case 'document':
        return 'border-indigo-200 bg-indigo-50'
      case 'task':
        return event.status === 'completed' ? 
          'border-green-200 bg-green-50' : 
          event.status === 'overdue' ? 
          'border-red-200 bg-red-50' :
          'border-orange-200 bg-orange-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getStatusBadge = (event: TimelineEvent) => {
    if (!event.status) return null

    const statusConfig = {
      completed: { label: 'Completed', variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      in_progress: { label: 'In Progress', variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
      pending: { label: 'Pending', variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' },
      overdue: { label: 'Overdue', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' }
    }

    const config = statusConfig[event.status]
    if (!config) return null

    return (
      <Badge variant={config.variant} className={`text-xs ${config.color}`}>
        {config.label}
      </Badge>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Deal Timeline</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {timelineEvents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No timeline events yet</p>
              <p className="text-sm">Events will appear here as the deal progresses</p>
            </div>
          ) : (
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-6 top-0 bottom-0 w-px bg-border" />
              
              {timelineEvents.map((event, index) => (
                <div key={event.id} className="relative flex items-start space-x-4 pb-6">
                  {/* Timeline dot */}
                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-2 ${getEventColor(event)}`}>
                    {getEventIcon(event)}
                  </div>
                  
                  {/* Event content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-medium text-foreground">
                            {event.title}
                          </h4>
                          {getStatusBadge(event)}
                        </div>
                        
                        {event.description && (
                          <p className="text-sm text-muted-foreground mb-2">
                            {event.description}
                          </p>
                        )}
                        
                        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{format(new Date(event.timestamp), 'MMM d, yyyy')}</span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatDistanceToNow(new Date(event.timestamp), { addSuffix: true })}</span>
                          </div>
                          
                          {event.user && (
                            <div className="flex items-center space-x-1">
                              <User className="h-3 w-3" />
                              <span>{event.user.name}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Additional metadata */}
                        {event.metadata && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {event.type === 'stage_change' && event.metadata.daysInStage && (
                              <Badge variant="outline" className="text-xs">
                                {event.metadata.daysInStage} days in stage
                              </Badge>
                            )}
                            
                            {event.type === 'task' && event.metadata.priority && (
                              <Badge variant="outline" className="text-xs">
                                {event.metadata.priority} priority
                              </Badge>
                            )}
                            
                            {event.type === 'document' && event.metadata.documentType && (
                              <Badge variant="outline" className="text-xs">
                                {event.metadata.documentType}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Current stage info */}
        {deal.currentStage && (
          <>
            <Separator className="my-6" />
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Current Stage</h4>
                <p className="text-sm text-muted-foreground">
                  {deal.currentStage.name}
                  {deal.daysInCurrentStage && (
                    <span className="ml-2">
                      ({deal.daysInCurrentStage} days)
                    </span>
                  )}
                </p>
              </div>
              
              {onStageChange && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    // This would open a stage selection dialog
                    // For now, we'll just call the callback
                  }}
                >
                  Change Stage
                </Button>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default DealTimeline
