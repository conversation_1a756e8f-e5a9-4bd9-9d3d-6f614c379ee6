import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CalendarIcon, ChevronDown } from 'lucide-react'
import { format, addDays, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfQuarter, endOfQuarter, startOfYear, endOfYear } from 'date-fns'
import { cn } from '@/lib/utils'

interface DateRange {
  from: Date
  to: Date
}

interface DatePickerWithRangeProps {
  date: DateRange
  onDateChange: (date: DateRange) => void
  className?: string
}

const presetRanges = [
  {
    label: 'Today',
    getValue: () => ({
      from: new Date(),
      to: new Date()
    })
  },
  {
    label: 'Yesterday',
    getValue: () => ({
      from: subDays(new Date(), 1),
      to: subDays(new Date(), 1)
    })
  },
  {
    label: 'Last 7 days',
    getValue: () => ({
      from: subDays(new Date(), 6),
      to: new Date()
    })
  },
  {
    label: 'Last 30 days',
    getValue: () => ({
      from: subDays(new Date(), 29),
      to: new Date()
    })
  },
  {
    label: 'Last 90 days',
    getValue: () => ({
      from: subDays(new Date(), 89),
      to: new Date()
    })
  },
  {
    label: 'This week',
    getValue: () => ({
      from: startOfWeek(new Date()),
      to: endOfWeek(new Date())
    })
  },
  {
    label: 'Last week',
    getValue: () => {
      const lastWeek = subDays(new Date(), 7)
      return {
        from: startOfWeek(lastWeek),
        to: endOfWeek(lastWeek)
      }
    }
  },
  {
    label: 'This month',
    getValue: () => ({
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date())
    })
  },
  {
    label: 'Last month',
    getValue: () => {
      const lastMonth = subDays(startOfMonth(new Date()), 1)
      return {
        from: startOfMonth(lastMonth),
        to: endOfMonth(lastMonth)
      }
    }
  },
  {
    label: 'This quarter',
    getValue: () => ({
      from: startOfQuarter(new Date()),
      to: endOfQuarter(new Date())
    })
  },
  {
    label: 'Last quarter',
    getValue: () => {
      const lastQuarter = subDays(startOfQuarter(new Date()), 1)
      return {
        from: startOfQuarter(lastQuarter),
        to: endOfQuarter(lastQuarter)
      }
    }
  },
  {
    label: 'This year',
    getValue: () => ({
      from: startOfYear(new Date()),
      to: endOfYear(new Date())
    })
  },
  {
    label: 'Last year',
    getValue: () => {
      const lastYear = new Date(new Date().getFullYear() - 1, 0, 1)
      return {
        from: startOfYear(lastYear),
        to: endOfYear(lastYear)
      }
    }
  }
]

export function DatePickerWithRange({ 
  date, 
  onDateChange, 
  className 
}: DatePickerWithRangeProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedRange, setSelectedRange] = useState<{ from?: Date; to?: Date }>({
    from: date.from,
    to: date.to
  })

  const handlePresetSelect = (preset: typeof presetRanges[0]) => {
    const range = preset.getValue()
    setSelectedRange(range)
    onDateChange(range)
    setIsOpen(false)
  }

  const handleCalendarSelect = (range: { from?: Date; to?: Date } | undefined) => {
    if (range?.from && range?.to) {
      setSelectedRange(range as DateRange)
      onDateChange(range as DateRange)
      setIsOpen(false)
    } else if (range?.from) {
      setSelectedRange({ from: range.from, to: range.from })
    }
  }

  const formatDateRange = (from: Date, to: Date) => {
    if (format(from, 'yyyy-MM-dd') === format(to, 'yyyy-MM-dd')) {
      return format(from, 'MMM d, yyyy')
    }
    
    if (from.getFullYear() === to.getFullYear()) {
      if (from.getMonth() === to.getMonth()) {
        return `${format(from, 'MMM d')} - ${format(to, 'd, yyyy')}`
      }
      return `${format(from, 'MMM d')} - ${format(to, 'MMM d, yyyy')}`
    }
    
    return `${format(from, 'MMM d, yyyy')} - ${format(to, 'MMM d, yyyy')}`
  }

  return (
    <div className={cn('grid gap-2', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              'w-[280px] justify-start text-left font-normal',
              !date && 'text-muted-foreground'
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              formatDateRange(date.from, date.to)
            ) : (
              <span>Pick a date range</span>
            )}
            <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex">
            {/* Preset ranges */}
            <div className="border-r p-3 space-y-1 min-w-[140px]">
              <div className="text-sm font-medium mb-2">Quick ranges</div>
              {presetRanges.map((preset) => (
                <Button
                  key={preset.label}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-sm h-8"
                  onClick={() => handlePresetSelect(preset)}
                >
                  {preset.label}
                </Button>
              ))}
            </div>
            
            {/* Calendar */}
            <div className="p-3">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={selectedRange}
                onSelect={handleCalendarSelect}
                numberOfMonths={2}
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

// Simple date picker for single dates
interface DatePickerProps {
  date?: Date
  onDateChange: (date: Date | undefined) => void
  placeholder?: string
  className?: string
}

export function DatePicker({ 
  date, 
  onDateChange, 
  placeholder = "Pick a date",
  className 
}: DatePickerProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleSelect = (selectedDate: Date | undefined) => {
    onDateChange(selectedDate)
    setIsOpen(false)
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-[240px] justify-start text-left font-normal',
            !date && 'text-muted-foreground',
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, 'PPP') : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}

export default DatePickerWithRange
